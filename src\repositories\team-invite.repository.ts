import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { TeamInvite, InviteStatus, InviteType } from '../modules/team-invite/entities/team-invite.entity';

@Injectable()
export class TeamInviteRepository {
  constructor(
    @InjectRepository(TeamInvite)
    private teamInviteRepository: Repository<TeamInvite>
  ) {}

  async create(invite: Partial<TeamInvite>): Promise<TeamInvite> {
    return await this.teamInviteRepository.save(invite);
  }

  async findByInviteCode(inviteCode: string): Promise<TeamInvite | null> {
    return await this.teamInviteRepository.findOne({
      where: { inviteCode },
      relations: ['inviter', 'invitee']
    });
  }

  async findByInviterId(inviterId: number): Promise<TeamInvite[]> {
    return await this.teamInviteRepository.find({
      where: { inviterId },
      relations: ['invitee'],
      order: { createdAt: 'DESC' }
    });
  }

  async findByInviteeId(inviteeId: number): Promise<TeamInvite[]> {
    return await this.teamInviteRepository.find({
      where: { inviteeId },
      relations: ['inviter'],
      order: { createdAt: 'DESC' }
    });
  }

  async updateStatus(id: number, status: InviteStatus, inviteeId?: number): Promise<void> {
    const updateData: any = { status };
    if (inviteeId) {
      updateData.inviteeId = inviteeId;
    }
    if (status === InviteStatus.ACCEPTED) {
      updateData.acceptedAt = new Date();
    }
    await this.teamInviteRepository.update(id, updateData);
  }

  async getInviteStats(inviterId: number): Promise<{
    totalInvites: number;
    successfulInvites: number;
    pendingInvites: number;
    expiredInvites: number;
  }> {
    const [totalInvites, successfulInvites, pendingInvites, expiredInvites] = await Promise.all([
      this.teamInviteRepository.count({ where: { inviterId } }),
      this.teamInviteRepository.count({ where: { inviterId, status: InviteStatus.ACCEPTED } }),
      this.teamInviteRepository.count({ where: { inviterId, status: InviteStatus.PENDING } }),
      this.teamInviteRepository.count({ where: { inviterId, status: InviteStatus.EXPIRED } })
    ]);

    return {
      totalInvites,
      successfulInvites,
      pendingInvites,
      expiredInvites
    };
  }

  async findExpiredInvites(): Promise<TeamInvite[]> {
    return await this.teamInviteRepository
      .createQueryBuilder('invite')
      .where('invite.status = :status', { status: InviteStatus.PENDING })
      .andWhere('invite.expiresAt IS NOT NULL')
      .andWhere('invite.expiresAt < :now', { now: new Date() })
      .getMany();
  }

  async markExpiredInvites(): Promise<void> {
    await this.teamInviteRepository
      .createQueryBuilder()
      .update(TeamInvite)
      .set({ status: InviteStatus.EXPIRED })
      .where('status = :status', { status: InviteStatus.PENDING })
      .andWhere('expiresAt IS NOT NULL')
      .andWhere('expiresAt < :now', { now: new Date() })
      .execute();
  }

  async delete(id: number): Promise<void> {
    await this.teamInviteRepository.delete(id);
  }

  async findById(id: number): Promise<TeamInvite | null> {
    return await this.teamInviteRepository.findOne({
      where: { id },
      relations: ['inviter', 'invitee']
    });
  }

  async isInviteCodeExists(inviteCode: string): Promise<boolean> {
    const count = await this.teamInviteRepository.count({
      where: { inviteCode }
    });
    return count > 0;
  }

  async generateUniqueInviteCode(): Promise<string> {
    const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789';
    let inviteCode: string = '';
    let exists = true;
    
    while (exists) {
      inviteCode = '';
      for (let i = 0; i < 8; i++) {
        inviteCode += chars.charAt(Math.floor(Math.random() * chars.length));
      }
      exists = await this.isInviteCodeExists(inviteCode);
    }
    
    return inviteCode;
  }
} 