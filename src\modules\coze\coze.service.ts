import { Injectable, OnModuleInit, Logger, NotFoundException, BadRequestException } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { CozeAPI, ChatEventType, RoleType, ChatStatus } from '@coze/api';
import { Subject, Observable } from 'rxjs';
import { ConversationRepository } from '../../repositories/conversation.repository';
import { MessageRepository } from '../../repositories/message.repository';
import { RedisService } from '../redis/redis.service';
import { CreateConversationDto, UpdateConversationDto } from './dto/conversation.dto';
import { CreateMessageDto } from './dto/message.dto';
import { Conversation, BotType } from './entities/conversation.entity';
import { Message, MessageRole, MessageType, MessageStatus } from './entities/message.entity';

@Injectable()
export class CozeService implements OnModuleInit {
  private readonly logger = new Logger(CozeService.name);
  private clients: Record<string, { client: CozeAPI; botId: string }> = {};

  constructor(
    private configService: ConfigService,
    private conversationRepository: ConversationRepository,
    private messageRepository: MessageRepository,
    private redisService: RedisService,
  ) { }

  onModuleInit() {
    const cozeConfig = this.configService.get('coze');

    // 动态初始化所有配置的客户端
    Object.keys(cozeConfig).forEach(region => {
      if (region !== 'defaultRegion') {
        this.clients[region] = {
          client: new CozeAPI({
            baseURL: cozeConfig[region].COZE_BASE_URL,
            baseWsURL: cozeConfig[region].COZE_BASE_WS_URL,
            token: cozeConfig[region].COZE_API_KEY,
            debug: false,
          }),
          botId: cozeConfig[region].COZE_BOT_ID,
        };
      }
    });
  }

  /**
   * 根据botType获取对应的配置区域
   */
  private getRegionByBotType(botType: BotType): string {
    switch (botType) {
      case BotType.DEFAULT:
        return 'zh';
      case BotType.INTERPRETATION:
        return 'interpretation';
      case BotType.PROTO:
        return 'proto';
      default:
        return 'zh';
    }
  }

  /**
   * 创建新对话
   */
  async createConversation(userId: number, createDto: CreateConversationDto): Promise<Conversation> {
    const botType = createDto.botType || BotType.DEFAULT;
    const region = this.getRegionByBotType(botType);
    console.log('userId:', userId); 
    // 验证区域是否支持
    if (!this.clients[region]) {
      throw new BadRequestException(`不支持的机器人区域: ${region}`);
    }

    const { client, botId } = this.clients[region];

    try {
      // 创建Coze对话
      const cozeConversation = await client.conversations.create({
        bot_id: botId,
        messages: createDto.initialMessage ? [
          {
            role: RoleType.User,
            content_type: 'text',
            content: createDto.initialMessage,
          },
        ] : [],
        meta_data: createDto.metadata || {},
      });

      // 保存到数据库
      const conversation = await this.conversationRepository.create(
        userId,
        createDto,
        cozeConversation.id,
        botId,
        botType,
      );

      // 如果有初始消息，创建用户消息记录
      if (createDto.initialMessage) {
        await this.messageRepository.create(
          conversation.id,
          MessageRole.USER,
          MessageType.TEXT,
          createDto.initialMessage,
          undefined,
          createDto.metadata,
        );
      }

      // 设置为用户的活跃对话
      await this.redisService.setUserActiveConversation(userId, conversation.id);

      return conversation;
    } catch (error) {
      this.logger.error('Error creating conversation', error);
      throw new BadRequestException('Failed to create conversation');
    }
  }

  /**
   * 获取对话详情
   */
  async getConversation(conversationId: string, userId: number): Promise<Conversation> {
    const conversation = await this.conversationRepository.findById(conversationId);

    if (!conversation) {
      throw new NotFoundException('Conversation not found');
    }

    if (conversation.userId !== userId) {
      throw new NotFoundException('Conversation not found');
    }

    return conversation;
  }

  /**
   * 验证对话类型是否匹配
   */
  async validateConversationType(conversationId: string, userId: number, expectedBotType: BotType): Promise<Conversation> {
    const conversation = await this.getConversation(conversationId, userId);

    if (conversation.botType !== expectedBotType) {
      throw new BadRequestException(
        `对话类型不匹配。期望类型: ${expectedBotType}, 实际类型: ${conversation.botType}`
      );
    }

    return conversation;
  }

  /**
   * 获取用户的对话列表
   */
  async getUserConversations(userId: number, page: number = 1, pageSize: number = 20, botType?: BotType): Promise<{
    conversations: Conversation[];
    total: number;
    page: number;
    pageSize: number;
  }> {
    const result = await this.conversationRepository.getUserConversationsPaginated(userId, page, pageSize, botType);

    return {
      conversations: result.conversations,
      total: result.total,
      page,
      pageSize,
    };
  }

  /**
   * 更新对话
   */
  async updateConversation(conversationId: string, userId: number, updateDto: UpdateConversationDto): Promise<Conversation> {
    const conversation = await this.getConversation(conversationId, userId);

    const updated = await this.conversationRepository.update(conversationId, updateDto);

    if (!updated) {
      throw new BadRequestException('Failed to update conversation');
    }

    return updated;
  }

  /**
   * 删除对话
   */
  async deleteConversation(conversationId: string, userId: number): Promise<boolean> {
    const conversation = await this.getConversation(conversationId, userId);

    return await this.conversationRepository.delete(conversationId);
  }

  /**
   * 发送消息（非流式）
   */
  async sendMessage(userId: number, createDto: CreateMessageDto): Promise<{
    userMessage: Message;
    assistantMessage: Message;
  }> {
    const conversation = await this.getConversation(createDto.conversationId, userId);
    const region = conversation.region;

    if (!this.clients[region]) {
      throw new BadRequestException(`机器人 ${region} 不可用`);
    }

    // 验证用户是否有权限使用该机器人
    const hasAccess = await this.validateBotAccess(userId, region);
    if (!hasAccess) {
      throw new BadRequestException(`您没有权限使用机器人 ${region}`);
    }

    const { client } = this.clients[region];

    try {
      // 确保type有默认值
      const messageType = createDto.type || MessageType.TEXT;

      // 创建用户消息记录
      const userMessage = await this.messageRepository.create(
        conversation.id,
        MessageRole.USER,
        messageType,
        createDto.content,
        undefined,
        createDto.metadata,
      );

      // 发送到Coze
      const response = await client.chat.createAndPoll({
        bot_id: conversation.botId,
        conversation_id: conversation.cozeConversationId,
        additional_messages: [
          {
            role: RoleType.User,
            content_type: messageType === MessageType.TEXT ? 'text' : 'object_string',
            content: createDto.content,
          },
        ],
      });

      if (response.chat.status === ChatStatus.COMPLETED) {
        await this.conversationRepository.incrementMessageCount(conversation.id);
      }

      if (response.chat.status !== ChatStatus.COMPLETED) {
        throw new Error('Chat not completed');
      }

      // 检查messages是否存在，获取助手回复
      if (!response.messages) {
        throw new Error('No messages in response');
      }

      const assistantMessage = response.messages.find(msg => msg.role === 'assistant' && msg.type === 'answer');

      if (assistantMessage) {
        // 创建助手消息记录
        const savedAssistantMessage = await this.messageRepository.create(
          conversation.id,
          MessageRole.ASSISTANT,
          MessageType.ANSWER,
          assistantMessage.content,
          assistantMessage.id,
          { cozeMessageId: assistantMessage.id },
        );

        // 更新消息状态
        await this.messageRepository.updateStatus(
          savedAssistantMessage.id,
          MessageStatus.COMPLETED,
          response.chat.usage,
        );

        // 更新对话的最后活跃时间和消息数量
        await this.conversationRepository.updateLastActiveTime(conversation.id);
        await this.conversationRepository.incrementMessageCount(conversation.id);

        return {
          userMessage,
          assistantMessage: savedAssistantMessage,
        };
      }

      throw new Error('No assistant response received');
    } catch (error) {
      this.logger.error('Error sending message', error);
      throw new BadRequestException('Failed to send message');
    }
  }

  /**
   * 发送消息（流式）
   */
  streamMessage(userId: number, createDto: CreateMessageDto): Observable<any> {
    const messageSubject = new Subject<any>();

    (async () => {
      try {
        const conversation = await this.getConversation(createDto.conversationId, userId);
        const region = conversation.region;

        if (!this.clients[region]) {
          throw new BadRequestException(`Unsupported region: ${region}`);
        }

        const { client } = this.clients[region];

        // 确保type有默认值
        const messageType = createDto.type || MessageType.TEXT;

        // 创建用户消息记录
        const userMessage = await this.messageRepository.create(
          conversation.id,
          MessageRole.USER,
          messageType,
          createDto.content,
          undefined,
          createDto.metadata,
        );

        messageSubject.next({
          event: 'user_message',
          data: userMessage
        });

        // 发送到Coze
        const streamResponse = await client.chat.stream({
          bot_id: conversation.botId,
          conversation_id: conversation.cozeConversationId,
          auto_save_history: false,
          additional_messages: [
            {
              role: RoleType.User,
              content_type: messageType === MessageType.TEXT ? 'text' : 'object_string',
              content: createDto.content,
            },
          ],
        });

        let fullContent = '';
        let assistantMessage: Message | null = null;
        let cozeChatId: string | null = null;

        for await (const part of streamResponse) {
          if (part.event === ChatEventType.CONVERSATION_CHAT_CREATED) {
            cozeChatId = part.data.id;

            // 创建助手消息记录
            assistantMessage = await this.messageRepository.create(
              conversation.id,
              MessageRole.ASSISTANT,
              MessageType.ANSWER,
              '',
              part.data.id,
              { cozeChatId: part.data.id },
            );

            messageSubject.next({
              event: 'assistant_message_created',
              data: assistantMessage
            });

            // 缓存流式状态
            await this.redisService.setStreamingState(
              conversation.cozeConversationId,
              cozeChatId,
              {
                conversationId: conversation.id,
                assistantMessageId: assistantMessage.id,
                userId,
              },
            );
          } else if (part.event === ChatEventType.CONVERSATION_MESSAGE_DELTA) {
            fullContent += part.data.content;

            messageSubject.next({
              event: 'delta',
              data: {
                content: part.data.content,
                fullContent: fullContent
              }
            });

            // 实时更新消息内容
            if (assistantMessage) {
              await this.messageRepository.updateContent(assistantMessage.id, fullContent);
            }
          } else if (part.event === ChatEventType.CONVERSATION_CHAT_COMPLETED) {
            if (assistantMessage) {
              // 更新消息状态
              await this.messageRepository.updateStatus(
                assistantMessage.id,
                MessageStatus.COMPLETED,
                part.data.usage,
              );
            }

            // 更新对话信息
            await this.conversationRepository.updateLastActiveTime(conversation.id);
            await this.conversationRepository.incrementMessageCount(conversation.id);
            await this.conversationRepository.incrementMessageCount(conversation.id);

            messageSubject.next({
              event: 'completed',
              data: {
                usage: part.data.usage,
                fullContent: fullContent
              }
            });

            // 清理流式状态
            if (cozeChatId) {
              await this.redisService.deleteStreamingState(conversation.cozeConversationId, cozeChatId);
            }
          } else if (part.event === ChatEventType.ERROR) {
            messageSubject.error(part.data);
          }
        }

        messageSubject.next({ event: 'done' });
        messageSubject.complete();
      } catch (error) {
        this.logger.error('Error in stream message', error);
        messageSubject.error(error);
      }
    })();

    return messageSubject.asObservable();
  }

  /**
   * 取消聊天
   */
  async cancelChat(conversationId: string, chatId: string, userId: number): Promise<boolean> {
    const conversation = await this.getConversation(conversationId, userId);
    const region = conversation.region;

    if (!this.clients[region]) {
      throw new BadRequestException(`Unsupported region: ${region}`);
    }

    const { client } = this.clients[region];

    try {
      await client.chat.cancel(conversation.cozeConversationId, chatId);

      // 清理流式状态
      await this.redisService.deleteStreamingState(conversation.cozeConversationId, chatId);

      return true;
    } catch (error) {
      this.logger.error('Error canceling chat', error);
      return false;
    }
  }

  /**
   * 获取对话消息列表
   */
  async getConversationMessages(conversationId: string, userId: number, page: number = 1, pageSize: number = 50): Promise<{
    messages: Message[];
    total: number;
    page: number;
    pageSize: number;
  }> {
    const conversation = await this.getConversation(conversationId, userId);

    const result = await this.messageRepository.findByConversationIdPaginated(
      conversationId,
      page,
      pageSize,
    );

    return {
      messages: result.messages,
      total: result.total,
      page,
      pageSize,
    };
  }

  // 获取可用机器人列表
  async getAvailableBots(): Promise<Array<{
    id: string;
    name: string;
    description?: string;
  }>> {
    const cozeConfig = this.configService.get('coze');
    const bots = [];

    // Object.keys(cozeConfig).forEach(region => {
    //   if (region !== 'defaultRegion') {
    //     bots.push({
    //       id: region,
    //       name: this.getBotName(region),
    //       description: this.getBotDescription(region)
    //     });
    //   }
    // });

    return bots;
  }

  // 获取机器人名称
  private getBotName(region: string): string {
    const botNames = {
      zh: '中文助手',
      bot2: '专业助手'
    };
    return botNames[region] || region;
  }

  // 验证机器人权限
  async validateBotAccess(userId: number, region: string): Promise<boolean> {
    // 可以根据用户权限或订阅状态验证是否可以使用特定机器人
    // 例如：免费用户只能使用基础机器人，付费用户可以使用专业机器人
    return true;
  }
}