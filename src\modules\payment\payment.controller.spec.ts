import { Test, TestingModule } from '@nestjs/testing';
import { PaymentController } from './payment.controller';
import { PaymentService } from './payment.service';
import { PaymentStatus } from './entities/payment.entity';
import { PaymentType, PaymentMethod } from './dto/create-payment.dto';
import { CreatePaymentRequestDto } from './dto/create-payment-request.dto';
import { UpdatePaymentStatusDto } from './dto/update-payment-status.dto';

describe('PaymentController', () => {
  let controller: PaymentController;
  let paymentService: jest.Mocked<PaymentService>;

  const mockPaymentService = {
    createPaymentOrder: jest.fn(),
    getPaymentById: jest.fn(),
    getPaymentByOrderNo: jest.fn(),
    updatePaymentStatus: jest.fn(),
    getUserPaymentStats: jest.fn(),
    verifyPaymentStatus: jest.fn(),
    queryPayments: jest.fn(),
    handlePaymentCallback: jest.fn(),
    createPayment: jest.fn(),
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      controllers: [PaymentController],
      providers: [
        {
          provide: PaymentService,
          useValue: mockPaymentService,
        },
      ],
    }).compile();

    controller = module.get<PaymentController>(PaymentController);
    paymentService = module.get(PaymentService);
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  it('should be defined', () => {
    expect(controller).toBeDefined();
  });

  describe('createPaymentOrder', () => {
    it('should create payment order successfully', async () => {
      // Arrange
      const createPaymentDto: CreatePaymentRequestDto = {
        userId: 123,
        type: PaymentType.TEAM_PERMISSION,
        method: PaymentMethod.WECHAT_PAY,
        amount: 9900,
        quantity: 1,
        description: '开通团队权限',
      };

      const mockResult = {
        paymentId: 1,
        orderNo: 'ORDER_20240115103000123456',
        paymentParams: { appId: 'test_appid' },
        expiresAt: new Date(),
        createdAt: new Date(),
      };

      paymentService.createPaymentOrder.mockResolvedValue(mockResult);

      // Act
      const result = await controller.createPaymentOrder(createPaymentDto);

      // Assert
      expect(paymentService.createPaymentOrder).toHaveBeenCalledWith(createPaymentDto);
      expect(result).toEqual(mockResult);
    });

    it('should handle service errors', async () => {
      // Arrange
      const createPaymentDto: CreatePaymentRequestDto = {
        userId: 123,
        type: PaymentType.TEAM_PERMISSION,
        method: PaymentMethod.WECHAT_PAY,
        amount: 9900,
      };

      paymentService.createPaymentOrder.mockRejectedValue(new Error('Service error'));

      // Act & Assert
      await expect(controller.createPaymentOrder(createPaymentDto)).rejects.toThrow();
    });
  });

  describe('getPaymentById', () => {
    it('should return payment by id', async () => {
      // Arrange
      const mockPayment = {
        id: 1,
        orderNo: 'ORDER_20240115103000123456',
        userId: 123,
        status: PaymentStatus.SUCCESS,
        amount: 9900,
        description: '开通团队权限',
        thirdPartyTransactionId: 'wx_transaction_123',
        paidAt: new Date(),
        failureReason: undefined,
        createdAt: new Date(),
        updatedAt: new Date(),
      };

      paymentService.getPaymentById.mockResolvedValue(mockPayment);

      // Act
      const result = await controller.getPaymentById(1);

      // Assert
      expect(paymentService.getPaymentById).toHaveBeenCalledWith(1);
      expect(result).toEqual(mockPayment);
    });
  });

  describe('getHello', () => {
    it('should return hello message', () => {
      // Act
      const result = controller.getHello();

      // Assert
      expect(result).toBe('Hello Payment Module!');
    });
  });

  describe('adminTest', () => {
    it('should return admin test response', () => {
      // Act
      const result = controller.adminTest();

      // Assert
      expect(result).toEqual({
        status: 200,
        message: '支付模块运行正常',
        timestamp: expect.any(String),
        module: 'PaymentModule',
        version: '2.0.0',
      });
    });
  });
});
