import { <PERSON><PERSON><PERSON>, PrimaryGeneratedColumn, Column, OneToMany } from 'typeorm';
import { ApiProperty } from '@nestjs/swagger';
import { Option } from './option.entity';

@Entity()
export class Question {
    @PrimaryGeneratedColumn()
    id: number;

    @ApiProperty()
    @Column()
    text: string;

    @ApiProperty()
    @Column()
    dimension: string;

    @OneToMany(() => Option, (option) => option.question, { cascade: true })
    options: Option[];
}
