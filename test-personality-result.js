const axios = require('axios');

const BASE_URL = 'http://localhost:3000';

async function testPersonalityResults() {
  try {
    console.log('🧪 开始测试人格测试结果功能...\n');

    // 1. 批量创建人格测试结果数据
    console.log('1️⃣ 批量创建人格测试结果数据...');
    const seedResponse = await axios.post(`${BASE_URL}/questions/personality-results/seed`);
    console.log('✅ 批量创建成功:', seedResponse.data.message);

    // 2. 获取所有人格测试结果
    console.log('\n2️⃣ 获取所有人格测试结果...');
    const allResultsResponse = await axios.get(`${BASE_URL}/questions/personality-results`);
    console.log(`✅ 获取成功，共 ${allResultsResponse.data.data.length} 条记录`);

    // 3. 根据类型代码获取特定人格测试结果
    console.log('\n3️⃣ 根据类型代码获取特定人格测试结果...');
    const getResultResponse = await axios.post(`${BASE_URL}/questions/personality-results/get`, {
      typeCode: 'ISTJ-D'
    });
    console.log('✅ 获取成功:', {
      typeCode: getResultResponse.data.data.typeCode,
      title: getResultResponse.data.data.title,
      symbol: getResultResponse.data.data.symbol
    });

    // 4. 测试答题计算并获取人格结果
    console.log('\n4️⃣ 测试答题计算并获取人格结果...');
    const testAnswers = [
      0, 1, 2, 3, 0, 1, 2, 3, // 能量互动 (E/I)
      0, 1, 2, 3, 0, 1, 2, 3, // 信息感知 (S/N)
      0, 1, 2, 3, 0, 1, 2, 3, // 决策形成 (T/F)
      0, 1, 2, 3, 0, 1, 2, 3  // 行动实施 (J/P)
    ];

    const calculateResponse = await axios.post(`${BASE_URL}/questions/personality-results/calculate`, {
      answers: testAnswers
    });
    console.log('✅ 计算成功:', {
      typeCode: calculateResponse.data.data.typeCode,
      title: calculateResponse.data.data.title,
      coreTraits: calculateResponse.data.data.coreTraits.substring(0, 50) + '...',
      symbol: calculateResponse.data.data.symbol
    });

    // 5. 测试创建新的人格测试结果
    console.log('\n5️⃣ 测试创建新的人格测试结果...');
    const newResult = {
      typeCode: 'TEST-001',
      title: '测试人格类型',
      coreTraits: '这是一个测试人格类型的核心特质描述',
      strengths: '测试优势描述',
      sceneMatch: '测试适用场景',
      blindSpots: '测试盲点描述',
      suggestions: '测试建议',
      symbol: '测试',
      lifePath: '测试人生路径',
      valueGuide: '测试价值指导'
    };

    const createResponse = await axios.post(`${BASE_URL}/questions/personality-results`, newResult);
    console.log('✅ 创建成功:', {
      id: createResponse.data.data.id,
      typeCode: createResponse.data.data.typeCode,
      title: createResponse.data.data.title
    });

    console.log('\n🎉 所有测试通过！人格测试结果功能正常工作。');

    // 6. 测试用户答题记录功能
    console.log('\n6️⃣ 测试用户答题记录功能...');
    
    // 保存用户答题记录
    const testUserId = 1;
    const saveRecordResponse = await axios.post(`${BASE_URL}/questions/user-answer-records`, {
      userId: testUserId,
      answers: testAnswers,
      remark: '测试答题记录'
    });
    console.log('✅ 保存答题记录成功:', {
      id: saveRecordResponse.data.data.id,
      finalType: saveRecordResponse.data.data.finalType,
      isCurrent: saveRecordResponse.data.data.isCurrent
    });

    // 获取用户答题历史
    const historyResponse = await axios.post(`${BASE_URL}/questions/user-answer-records/history`, {
      userId: testUserId
    });
    console.log(`✅ 获取答题历史成功，共 ${historyResponse.data.data.length} 条记录`);

    // 获取用户当前类型
    const currentTypeResponse = await axios.post(`${BASE_URL}/questions/user-answer-records/current-type`, {
      userId: testUserId
    });
    console.log('✅ 获取当前类型成功:', {
      finalType: currentTypeResponse.data.data.currentType.finalType,
      totalTests: currentTypeResponse.data.data.testHistory.totalTests,
      personalityTitle: currentTypeResponse.data.data.currentType.personalityResult?.title
    });

    // 获取用户的proto类型
    const protoResponse = await axios.get(`${BASE_URL}/questions/user-answer-records/proto/${testUserId}`);
    console.log('✅ 获取proto类型成功:', {
      proto: protoResponse.data.data.proto
    });

    console.log('\n🎉 所有测试通过！用户答题记录功能正常工作。');

  } catch (error) {
    console.error('❌ 测试失败:', error.response?.data || error.message);
  }
}

// 运行测试
testPersonalityResults(); 