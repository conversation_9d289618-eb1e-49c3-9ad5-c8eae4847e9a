module.exports = {
  extends: ['@commitlint/config-conventional'],
  rules: {
    // 类型定义
    'type-enum': [
      2,
      'always',
      [
        'feat',     // 新功能
        'fix',      // 修复bug
        'docs',     // 文档变更
        'style',    // 代码格式(不影响功能，例如空格、分号等格式修正)
        'refactor', // 代码重构
        'perf',     // 性能优化
        'test',     // 增加测试
        'chore',    // 构建过程或辅助工具的变动
        'revert',   // 回退
        'build',    // 打包构建
        'ci',       // CI配置
      ],
    ],
    // 主题最大长度
    'subject-max-length': [2, 'always', 50],
    // 主题不能为空
    'subject-empty': [2, 'never'],
    // 类型不能为空
    'type-empty': [2, 'never'],
    // 主题结尾不能有句号
    'subject-full-stop': [2, 'never', '.'],
    // 主题格式，不能有大写开头
    'subject-case': [2, 'never', ['start-case', 'pascal-case', 'upper-case']],
  },
}; 