import { Test, TestingModule } from '@nestjs/testing';
import { CozeService } from './coze.service';
import { ConfigService } from '@nestjs/config';
import { ConversationRepository } from '../../repositories/conversation.repository';
import { MessageRepository } from '../../repositories/message.repository';
import { RedisService } from '../redis/redis.service';
import { BotType } from './entities/conversation.entity';

describe('CozeService', () => {
  let service: CozeService;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        CozeService,
        {
          provide: ConfigService,
          useValue: {
            get: jest.fn().mockReturnValue({
              zh: {
                COZE_BOT_ID: 'test-bot-id',
                COZE_BASE_URL: 'https://api.coze.cn',
                COZE_BASE_WS_URL: 'wss://ws.coze.cn',
                COZE_API_KEY: 'test-api-key',
              },
              interpretation: {
                COZE_BOT_ID: 'interpretation-bot-id',
                COZE_BASE_URL: 'https://api.coze.cn',
                COZE_BASE_WS_URL: 'wss://ws.coze.cn',
                COZE_API_KEY: 'interpretation-api-key',
              },
              proto: {
                COZE_BOT_ID: 'proto-bot-id',
                COZE_BASE_URL: 'https://api.coze.cn',
                COZE_BASE_WS_URL: 'wss://ws.coze.cn',
                COZE_API_KEY: 'proto-api-key',
              },
              defaultRegion: 'zh',
            }),
          },
        },
        {
          provide: ConversationRepository,
          useValue: {
            create: jest.fn(),
            findById: jest.fn(),
            getUserConversationsPaginated: jest.fn(),
            update: jest.fn(),
            delete: jest.fn(),
            incrementMessageCount: jest.fn(),
            updateLastActiveTime: jest.fn(),
          },
        },
        {
          provide: MessageRepository,
          useValue: {
            create: jest.fn(),
            findByConversationId: jest.fn(),
            updateStatus: jest.fn(),
            updateContent: jest.fn(),
          },
        },
        {
          provide: RedisService,
          useValue: {
            setUserActiveConversation: jest.fn(),
            setStreamingState: jest.fn(),
          },
        },
      ],
    }).compile();

    service = module.get<CozeService>(CozeService);
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  describe('getRegionByBotType', () => {
    it('should return correct region for default bot type', () => {
      const result = (service as any).getRegionByBotType(BotType.DEFAULT);
      expect(result).toBe('zh');
    });

    it('should return correct region for interpretation bot type', () => {
      const result = (service as any).getRegionByBotType(BotType.INTERPRETATION);
      expect(result).toBe('interpretation');
    });

    it('should return correct region for proto bot type', () => {
      const result = (service as any).getRegionByBotType(BotType.PROTO);
      expect(result).toBe('proto');
    });

    it('should return default region for unknown bot type', () => {
      const result = (service as any).getRegionByBotType('unknown' as BotType);
      expect(result).toBe('zh');
    });
  });
});
