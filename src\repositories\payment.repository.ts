import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository, FindOptionsWhere, FindManyOptions } from 'typeorm';
import { Payment, PaymentStatus } from '../modules/payment/entities/payment.entity';
import { CreatePaymentDto, PaymentType, PaymentMethod } from '../modules/payment/dto/create-payment.dto';

/**
 * Payment Repository
 * 支付记录数据访问层
 */
@Injectable()
export class PaymentRepository {
  constructor(
    @InjectRepository(Payment)
    private readonly paymentRepository: Repository<Payment>,
  ) {}

  /**
   * 创建支付记录
   * @param createPaymentData 支付创建数据
   * @returns 创建的支付记录
   */
  async create(createPaymentData: Partial<Payment>): Promise<Payment> {
    const payment = this.paymentRepository.create(createPaymentData);
    return await this.paymentRepository.save(payment);
  }

  /**
   * 根据ID查找支付记录
   * @param id 支付记录ID
   * @returns 支付记录或null
   */
  async findById(id: number): Promise<Payment | null> {
    if (!id || isNaN(id) || id <= 0) {
      return null;
    }

    return await this.paymentRepository.findOne({
      where: { id },
      relations: ['user'],
    });
  }

  /**
   * 根据订单号查找支付记录
   * @param orderNo 订单号
   * @returns 支付记录或null
   */
  async findByOrderNo(orderNo: string): Promise<Payment | null> {
    if (!orderNo) {
      return null;
    }

    return await this.paymentRepository.findOne({
      where: { orderNo },
      relations: ['user'],
    });
  }

  /**
   * 根据第三方交易号查找支付记录
   * @param thirdPartyTransactionId 第三方交易号
   * @returns 支付记录或null
   */
  async findByThirdPartyTransactionId(thirdPartyTransactionId: string): Promise<Payment | null> {
    if (!thirdPartyTransactionId) {
      return null;
    }

    return await this.paymentRepository.findOne({
      where: { thirdPartyTransactionId },
      relations: ['user'],
    });
  }

  /**
   * 根据用户ID查找支付记录
   * @param userId 用户ID
   * @param options 查询选项
   * @returns 支付记录列表
   */
  async findByUserId(
    userId: number,
    options?: {
      status?: PaymentStatus;
      type?: PaymentType;
      method?: PaymentMethod;
      limit?: number;
      offset?: number;
    },
  ): Promise<Payment[]> {
    if (!userId || isNaN(userId) || userId <= 0) {
      return [];
    }

    const where: FindOptionsWhere<Payment> = { userId };
    
    if (options?.status) {
      where.status = options.status;
    }
    if (options?.type) {
      where.type = options.type;
    }
    if (options?.method) {
      where.method = options.method;
    }

    const findOptions: FindManyOptions<Payment> = {
      where,
      relations: ['user'],
      order: { createdAt: 'DESC' },
    };

    if (options?.limit) {
      findOptions.take = options.limit;
    }
    if (options?.offset) {
      findOptions.skip = options.offset;
    }

    return await this.paymentRepository.find(findOptions);
  }

  /**
   * 更新支付记录
   * @param id 支付记录ID
   * @param updateData 更新数据
   * @returns 更新结果
   */
  async update(id: number, updateData: Partial<Payment>): Promise<void> {
    if (!id || isNaN(id) || id <= 0) {
      throw new Error('Invalid payment ID');
    }

    await this.paymentRepository.update(id, updateData);
  }

  /**
   * 更新支付状态
   * @param id 支付记录ID
   * @param status 新状态
   * @param additionalData 额外数据（如失败原因、第三方响应等）
   */
  async updateStatus(
    id: number,
    status: PaymentStatus,
    additionalData?: {
      thirdPartyTransactionId?: string;
      failureReason?: string;
      thirdPartyResponse?: Record<string, any>;
      paidAt?: Date;
      refundedAt?: Date;
      refundAmount?: number;
    },
  ): Promise<void> {
    if (!id || isNaN(id) || id <= 0) {
      throw new Error('Invalid payment ID');
    }

    const updateData: Partial<Payment> = {
      status,
      ...additionalData,
    };

    await this.paymentRepository.update(id, updateData);
  }

  /**
   * 根据订单号更新支付状态
   * @param orderNo 订单号
   * @param status 新状态
   * @param additionalData 额外数据
   */
  async updateStatusByOrderNo(
    orderNo: string,
    status: PaymentStatus,
    additionalData?: {
      thirdPartyTransactionId?: string;
      failureReason?: string;
      thirdPartyResponse?: Record<string, any>;
      paidAt?: Date;
      refundedAt?: Date;
      refundAmount?: number;
    },
  ): Promise<void> {
    if (!orderNo) {
      throw new Error('Invalid order number');
    }

    const updateData: Partial<Payment> = {
      status,
      ...additionalData,
    };

    await this.paymentRepository.update({ orderNo }, updateData);
  }

  /**
   * 获取用户支付统计信息
   * @param userId 用户ID
   * @returns 支付统计信息
   */
  async getPaymentStats(userId: number): Promise<{
    totalPayments: number;
    successfulPayments: number;
    failedPayments: number;
    totalAmount: number;
    successfulAmount: number;
  }> {
    if (!userId || isNaN(userId) || userId <= 0) {
      return {
        totalPayments: 0,
        successfulPayments: 0,
        failedPayments: 0,
        totalAmount: 0,
        successfulAmount: 0,
      };
    }

    const [totalResult, successResult, failedResult] = await Promise.all([
      this.paymentRepository
        .createQueryBuilder('payment')
        .select('COUNT(*)', 'count')
        .addSelect('SUM(payment.amount)', 'totalAmount')
        .where('payment.userId = :userId', { userId })
        .getRawOne(),
      
      this.paymentRepository
        .createQueryBuilder('payment')
        .select('COUNT(*)', 'count')
        .addSelect('SUM(payment.amount)', 'totalAmount')
        .where('payment.userId = :userId', { userId })
        .andWhere('payment.status = :status', { status: PaymentStatus.SUCCESS })
        .getRawOne(),
      
      this.paymentRepository
        .createQueryBuilder('payment')
        .select('COUNT(*)', 'count')
        .where('payment.userId = :userId', { userId })
        .andWhere('payment.status = :status', { status: PaymentStatus.FAILED })
        .getRawOne(),
    ]);

    return {
      totalPayments: parseInt(totalResult?.count || '0'),
      successfulPayments: parseInt(successResult?.count || '0'),
      failedPayments: parseInt(failedResult?.count || '0'),
      totalAmount: parseInt(totalResult?.totalAmount || '0'),
      successfulAmount: parseInt(successResult?.totalAmount || '0'),
    };
  }

  /**
   * 查找所有支付记录（分页）
   * @param options 查询选项
   * @returns 支付记录列表和总数
   */
  async findAll(options?: {
    status?: PaymentStatus;
    type?: PaymentType;
    method?: PaymentMethod;
    userId?: number;
    limit?: number;
    offset?: number;
  }): Promise<{ payments: Payment[]; total: number }> {
    const where: FindOptionsWhere<Payment> = {};

    if (options?.status) {
      where.status = options.status;
    }
    if (options?.type) {
      where.type = options.type;
    }
    if (options?.method) {
      where.method = options.method;
    }
    if (options?.userId) {
      where.userId = options.userId;
    }

    const findOptions: FindManyOptions<Payment> = {
      where,
      relations: ['user'],
      order: { createdAt: 'DESC' },
    };

    if (options?.limit) {
      findOptions.take = options.limit;
    }
    if (options?.offset) {
      findOptions.skip = options.offset;
    }

    const [payments, total] = await this.paymentRepository.findAndCount(findOptions);

    return { payments, total };
  }

  /**
   * 删除支付记录（软删除，实际上是标记为已取消）
   * @param id 支付记录ID
   */
  async delete(id: number): Promise<void> {
    if (!id || isNaN(id) || id <= 0) {
      throw new Error('Invalid payment ID');
    }

    await this.paymentRepository.update(id, { status: PaymentStatus.CANCELLED });
  }
}
