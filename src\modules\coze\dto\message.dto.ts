import { IsNotEmpty, IsString, IsOptional, <PERSON>Enum, IsUUID, IsBoolean } from 'class-validator';
import { ApiProperty } from '@nestjs/swagger';
import { MessageRole, MessageType } from '../entities/message.entity';
import { BotType } from '../entities/conversation.entity';

export class CreateMessageDto {
    @IsNotEmpty()
    @IsUUID()
    @ApiProperty({ description: '对话ID' })
    conversationId: string;

    @IsNotEmpty()
    @IsString()
    @ApiProperty({ description: '消息内容' })
    content: string;

    @IsOptional()
    @IsEnum(BotType)
    @ApiProperty({ enum: BotType, description: '智能体类型', default: BotType.DEFAULT })
    botType?: BotType;

    @IsOptional()
    @IsEnum(MessageType)
    @ApiProperty({ enum: MessageType, description: '消息类型', default: MessageType.TEXT })
    type?: MessageType = MessageType.TEXT;

    @IsOptional()
    @IsBoolean()
    @ApiProperty({ description: '是否使用流式响应', default: false })
    streaming?: boolean = false;

    @IsOptional()
    @ApiProperty({ description: '消息元数据', required: false })
    metadata?: Record<string, any>;
}

export class MessageListDto {
    @IsNotEmpty()
    @IsUUID()
    @ApiProperty({ description: '对话ID' })
    conversationId: string;

    @IsOptional()
    @IsEnum(MessageRole)
    @ApiProperty({ enum: MessageRole, description: '消息角色过滤', required: false })
    role?: MessageRole;

    @IsOptional()
    @ApiProperty({ description: '页码', default: 1 })
    page?: number = 1;

    @IsOptional()
    @ApiProperty({ description: '页大小', default: 50 })
    pageSize?: number = 50;
}