const axios = require("axios");

// 测试配置
const BASE_URL = "http://localhost:3000";
const TEST_TOKEN = "your_test_jwt_token"; // 需要替换为实际的JWT token

// 测试数据
const testPaymentData = {
  type: "team_permission",
  method: "wechat_pay",
  amount: 9900,
  quantity: 1,
  description: "测试开通团队权限",
  clientIp: "127.0.0.1",
};

const testWechatPaymentData = {
  appid: "your_miniprogram_appid",
  payer: "test_user_uuid",
  openid: "test_user_openid",
  amount: "99.00",
  orderNo: "TEST" + Date.now(),
  description: "测试支付",
};

// 测试创建支付订单
async function testCreatePayment() {
  try {
    console.log("测试创建支付订单...");
    const response = await axios.post(`${BASE_URL}/payment`, testPaymentData, {
      headers: {
        Authorization: `Bearer ${TEST_TOKEN}`,
        "Content-Type": "application/json",
      },
    });

    console.log("创建支付订单成功:", response.data);
    return response.data.data.payment.orderNo;
  } catch (error) {
    console.error("创建支付订单失败:", error.response?.data || error.message);
    return null;
  }
}

// 测试微信支付预支付
async function testPrePayment(orderNo) {
  try {
    console.log("测试微信支付预支付...");
    const wechatData = { ...testWechatPaymentData, orderNo };

    const response = await axios.post(
      `${BASE_URL}/payment/wechat/pre-payment`,
      wechatData,
      {
        headers: {
          "Content-Type": "application/json",
        },
      }
    );

    console.log("微信支付预支付成功:", response.data);
    return response.data;
  } catch (error) {
    console.error("微信支付预支付失败:", error.response?.data || error.message);
    return null;
  }
}

// 测试获取支付历史
async function testGetPaymentHistory() {
  try {
    console.log("测试获取支付历史...");
    const response = await axios.get(
      `${BASE_URL}/payment/history?page=1&limit=10`,
      {
        headers: {
          Authorization: `Bearer ${TEST_TOKEN}`,
        },
      }
    );

    console.log("获取支付历史成功:", response.data);
  } catch (error) {
    console.error("获取支付历史失败:", error.response?.data || error.message);
  }
}

// 主测试函数
async function runTests() {
  console.log("开始微信支付功能测试...\n");

  // 1. 测试创建支付订单
  const orderNo = await testCreatePayment();
  if (!orderNo) {
    console.log("创建支付订单失败，跳过后续测试");
    return;
  }

  console.log("\n" + "=".repeat(50) + "\n");

  // 2. 测试微信支付预支付
  await testPrePayment(orderNo);

  console.log("\n" + "=".repeat(50) + "\n");

  // 3. 测试获取支付历史
  await testGetPaymentHistory();

  console.log("\n测试完成！");
}

// 运行测试
if (require.main === module) {
  runTests().catch(console.error);
}

module.exports = {
  testCreatePayment,
  testPrePayment,
  testGetPaymentHistory,
  runTests,
};
