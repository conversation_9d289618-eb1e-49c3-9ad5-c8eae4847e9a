import { Injectable, BadRequestException, UnauthorizedException } from "@nestjs/common";
import { CreateUserDto } from "./dto/create-user.dto";
import { LoginUserDto } from "./dto/login-user.dto";
import { UpdateUserDto } from "./dto/update-user.dto";
import { WechatLoginDto } from "./dto/wechat-login.dto";
import { DecryptUserInfoDto, DecryptedUserInfoResponseDto } from "./dto/decrypt-user-info.dto";
import { User, Role, UserType, PermissionLevel } from "./entities/user.entity";
import { CaptchaService } from "../captcha/captcha.service";
import { JwtService } from "@nestjs/jwt";
import { UserRepository } from "../../repositories/user.repository";
import { RedisService } from "../redis/redis.service";
import { InjectRepository } from "@nestjs/typeorm";
import { Repository } from "typeorm";
import { UpdateMembershipDto } from "./dto/membership.dto";
import { ConfigService } from "@nestjs/config";
import { WechatDecryptUtil } from "../../common/utils/wechat.util";
import {
  generateRandomUserName,
  generateUniqueInviteCode,
  getWechatOpenid
} from "../../common/utils/user.utils";
import { UserValidationService } from "../../guards/permission.guard";

@Injectable()
export class UsersService {
  constructor(
    private userRepository: UserRepository,
    private captchaService: CaptchaService,
    private jwtService: JwtService,
    private redisService: RedisService,
    private configService: ConfigService,
    private userValidationService: UserValidationService,
    @InjectRepository(User)
    private entityRepository: Repository<User>
  ) { }

  async register(createUserDto: CreateUserDto) {
    // 1. 验证验证码（如果提供了手机号）
    if (createUserDto.phoneNumber) {
      if (!createUserDto.captcha) {
        throw new BadRequestException("提供手机号时必须提供验证码");
      }
      await this.captchaService.verifyCaptcha(
        createUserDto.phoneNumber,
        createUserDto.captcha
      );
    }

    // 使用Repository的manager进行事务处理
    return await this.entityRepository.manager.transaction(async (manager) => {
      // 2. 检查用户名是否已被使用
      const existingUserByName = await manager.findOne(User, {
        where: { name: createUserDto.name },
      });
      if (existingUserByName) {
        throw new BadRequestException("该用户名已被使用");
      }

      // 3. 检查手机号是否已被注册（如果提供了手机号）
      if (createUserDto.phoneNumber) {
        const existingUserByPhone = await manager.findOne(User, {
          where: { phoneNumber: createUserDto.phoneNumber },
        });
        if (existingUserByPhone) {
          throw new BadRequestException("该手机号已被注册");
        }
      }

      // 4. 检查邮箱是否已被注册（如果提供了邮箱）
      if (createUserDto.email) {
        const existingUserByEmail = await manager.findOne(User, {
          where: { email: createUserDto.email },
        });
        if (existingUserByEmail) {
          throw new BadRequestException("该邮箱已被注册");
        }
      }

      // 5. 生成唯一的邀请码
      const inviteCode = await generateUniqueInviteCode(manager);

      // 6. 创建新用户
      const user = new User();
      user.name = createUserDto.name;
      user.email = createUserDto.email || null;
      user.password = createUserDto.password;
      user.phoneNumber = createUserDto.phoneNumber || null;
      user.role = Role.User;
      user.userType = createUserDto.userType || UserType.Individual;
      user.myInviteCode = inviteCode; // 设置唯一邀请码

      // 设置可选字段
      if (createUserDto.fullName) user.fullName = createUserDto.fullName;
      if (createUserDto.address) user.address = createUserDto.address;
      if (createUserDto.gender) user.gender = createUserDto.gender;
      if (createUserDto.birthDate) user.birthDate = new Date(createUserDto.birthDate);
      if (createUserDto.profilePicture) user.profilePicture = createUserDto.profilePicture;
      if (createUserDto.profileBackgroundPicture) user.profileBackgroundPicture = createUserDto.profileBackgroundPicture;

      // 设置权限相关字段
      user.permissionLevel = PermissionLevel.Individual; // 默认个人权限
      user.canInviteOthers = false; // 个人权限不能邀请他人
      user.hasShareProfitRights = false; // 个人权限没有分享获利权益

      const savedUser = await manager.save(User, user);

      // 返回用户信息（不包含密码）
      const { password, ...result } = savedUser;
      return {
        user: result,
        message: "注册成功",
      };
    });
  }

  async login(loginUserDto: LoginUserDto) {
    const { phoneNumber, password } = loginUserDto;

    // 使用 UserValidationService 验证用户存在性
    const user = await this.userValidationService.validateUserByPhone(phoneNumber);

    // 检查用户是否激活
    this.userValidationService.validateUserActive(user);

    if (!(await user.validatePassword(password))) {
      throw new BadRequestException("密码错误");
    }

    // 更新登录信息
    await this.userRepository.updateLoginInfo(user.id);

    // 生成JWT token
    const payload = {
      sub: user.id,
      phoneNumber: user.phoneNumber,
      role: user.role,
      userType: user.userType,
      permissionLevel: user.permissionLevel,
      canInviteOthers: user.canInviteOthers,
      hasShareProfitRights: user.hasShareProfitRights,
    };
    const token = this.jwtService.sign(payload);

    // 返回用户信息（不包含密码）和token
    const { password: userPassword, ...result } = user;
    return {
      user: result,
      token: token,
      message: "登录成功",
    };
  }

  /**
   * 退出登录
   * @param token JWT token
   * @returns 退出登录结果
   */
  async logout(token: string) {
    try {
      // 1. 验证并解析token
      const decoded = this.jwtService.verify(token);

      // 2. 验证用户ID
      const userId = parseInt(decoded.sub, 10);
      if (isNaN(userId)) {
        throw new UnauthorizedException("无效的用户信息");
      }

      // 3. 计算token剩余有效时间
      const currentTime = Math.floor(Date.now() / 1000);
      const remainingTime = decoded.exp - currentTime;

      if (remainingTime <= 0) {
        throw new UnauthorizedException("Token已过期");
      }

      // 4. 将token添加到黑名单，设置过期时间为token的剩余有效期
      const blacklistKey = `blacklist:${token}`;
      await this.redisService.set(blacklistKey, "1", remainingTime);

      // 5. 可选：清除用户相关的缓存数据
      await this.clearUserCache(userId);

      return {
        message: "退出登录成功",
        timestamp: new Date().toISOString(),
      };
    } catch (error) {
      if (error.name === 'JsonWebTokenError') {
        console.log("USERS-SERVICE-ERROR", error);
        throw new UnauthorizedException("无效的token");
      }
      if (error.name === 'TokenExpiredError') {
        throw new UnauthorizedException("Token已过期");
      }
      throw error;
    }
  }

  /**
   * 检查token是否在黑名单中
   * @param token JWT token
   * @returns boolean
   */
  async isTokenBlacklisted(token: string): Promise<boolean> {
    const blacklistKey = `blacklist:${token}`;
    return await this.redisService.exists(blacklistKey);
  }

  /**
   * 清除用户相关缓存
   * @param userId 用户ID
   */
  private async clearUserCache(userId: number): Promise<void> {
    // 这里可以清除用户相关的缓存数据
    // 例如：用户信息缓存、权限缓存等
    const userCacheKey = `user:${userId}`;
    const userPermissionKey = `user:permissions:${userId}`;

    await Promise.all([
      this.redisService.delete(userCacheKey),
      this.redisService.delete(userPermissionKey),
    ]);
  }

  /**
   * 批量清理过期的黑名单token（可选的清理任务）
   */
  async cleanupExpiredTokens(): Promise<void> {
    // 这个方法可以作为定时任务来清理过期的黑名单token
    // Redis会自动处理过期的key，所以这个方法主要用于统计或其他清理逻辑
  }

  async findAll() {
    const users = await this.userRepository.findAll();
    return {
      users,
      total: users.length,
    };
  }

  async findOne(id: number) {
    // 使用 UserValidationService 验证用户存在性
    const user = await this.userValidationService.validateUserById(id);
    return {
      user,
    };
  }

  async update(id: number, updateUserDto: UpdateUserDto) {
    // 使用 UserValidationService 验证用户存在性
    const user = await this.userValidationService.validateUserById(id);

    // 检查出生日期修改权限
    if (updateUserDto.birthDate && user.birthDateLocked) {
      throw new BadRequestException("出生日期已锁定，无法修改");
    }

    // 构建更新数据
    const updateData: Partial<User> = {};

    if (updateUserDto.name) updateData.name = updateUserDto.name;
    if (updateUserDto.email) updateData.email = updateUserDto.email;
    if (updateUserDto.fullName) updateData.fullName = updateUserDto.fullName;
    if (updateUserDto.address) updateData.address = updateUserDto.address;
    if (updateUserDto.gender) updateData.gender = updateUserDto.gender;
    if (updateUserDto.profilePicture) updateData.profilePicture = updateUserDto.profilePicture;
    if (updateUserDto.profileBackgroundPicture) updateData.profileBackgroundPicture = updateUserDto.profileBackgroundPicture;
    if (updateUserDto.userType) updateData.userType = updateUserDto.userType;
    if (updateUserDto.membershipType) updateData.membershipType = updateUserDto.membershipType;
    if (updateUserDto.membershipExpireDate) updateData.membershipExpireDate = new Date(updateUserDto.membershipExpireDate);
    if (typeof updateUserDto.hasTeamPermission === 'boolean') updateData.hasTeamPermission = updateUserDto.hasTeamPermission;
    if (typeof updateUserDto.isActive === 'boolean') updateData.isActive = updateUserDto.isActive;
    if (typeof updateUserDto.birthDateLocked === 'boolean') updateData.birthDateLocked = updateUserDto.birthDateLocked;

    if (updateUserDto.birthDate) {
      updateData.birthDate = new Date(updateUserDto.birthDate);
      // 如果这是第一次设置出生日期，则锁定它
      if (!user.birthDate) {
        updateData.birthDateLocked = true;
      }
    }

    await this.userRepository.update(id, updateData);

    return {
      message: "用户信息更新成功",
    };
  }

  async remove(id: number) {
    // 使用 UserValidationService 验证用户存在性
    await this.userValidationService.validateUserById(id);

    await this.userRepository.delete(id);

    return {
      message: "用户删除成功",
    };
  }

  /**
   * 消费免费次数或检查权限
   */
  async consumeOrCheckLimit(userId: number, type: 'assessment' | 'aiInterpretation'): Promise<boolean> {
    // 使用 UserValidationService 验证用户存在性
    const user = await this.userValidationService.validateUserById(userId);

    const freeCountField = type === 'assessment' ? 'freeAssessmentCount' : 'freeAIInterpretationCount';

    if (user[freeCountField] > 0) {
      // 有免费次数，消费一次
      const updateData = { [freeCountField]: user[freeCountField] - 1 };
      await this.userRepository.update(userId, updateData);

      // 更新总使用次数
      await this.userRepository.updateUsageCount(userId, type);

      return true;
    }

    // 检查会员权限
    if (user.membershipExpireDate && new Date() < user.membershipExpireDate) {
      // 会员有效，直接允许使用
      await this.userRepository.updateUsageCount(userId, type);
      return true;
    }

    return false; // 需要付费
  }

  /**
   * 检查使用限制
   */
  async checkUsageLimit(userId: number, type: 'assessment' | 'aiInterpretation') {
    // 使用 UserValidationService 验证用户存在性
    const user = await this.userValidationService.validateUserById(userId);

    const freeCountField = type === 'assessment' ? 'freeAssessmentCount' : 'freeAIInterpretationCount';
    const remainingFreeCount = user[freeCountField];

    // 检查会员状态
    const isMember = user.membershipExpireDate && new Date() < user.membershipExpireDate;
    const canUse = remainingFreeCount > 0 || isMember;

    let message = "";
    if (canUse) {
      if (remainingFreeCount > 0) {
        message = `您还有 ${remainingFreeCount} 次免费使用机会`;
      } else if (isMember) {
        message = "会员权限有效，可以使用";
      }
    } else {
      message = "免费次数已用完，需要购买会员或单次付费";
    }

    return {
      canUse,
      remainingFreeCount,
      isMember: !!isMember,
      membershipExpireDate: user.membershipExpireDate,
      message
    };
  }

  /**
   * 消费使用次数
   */
  async consumeUsage(userId: number, type: 'assessment' | 'aiInterpretation') {
    // 使用 UserValidationService 验证用户存在性
    const user = await this.userValidationService.validateUserById(userId);

    const freeCountField = type === 'assessment' ? 'freeAssessmentCount' : 'freeAIInterpretationCount';

    if (user[freeCountField] > 0) {
      // 消费免费次数
      const updateData = { [freeCountField]: user[freeCountField] - 1 };
      await this.userRepository.update(userId, updateData);

      // 更新总使用次数
      await this.userRepository.updateUsageCount(userId, type);

      return {
        success: true,
        message: "使用成功",
        remainingCount: user[freeCountField] - 1
      };
    }

    // 检查会员权限
    if (user.membershipExpireDate && new Date() < user.membershipExpireDate) {
      // 会员有效，直接允许使用
      await this.userRepository.updateUsageCount(userId, type);

      return {
        success: true,
        message: "会员权限使用成功",
        remainingCount: -1 // 会员无限制，用-1表示
      };
    }

    throw new BadRequestException("免费次数已用完，需要购买会员或单次付费");
  }

  /**
   * 获取会员状态
   */
  async getMembershipStatus(userId: number) {
    // 使用 UserValidationService 验证用户存在性
    const user = await this.userValidationService.validateUserById(userId);

    const isActive = user.membershipExpireDate && new Date() < user.membershipExpireDate;
    let remainingDays: number | undefined = undefined;

    if (isActive && user.membershipExpireDate) {
      const diffTime = user.membershipExpireDate.getTime() - new Date().getTime();
      remainingDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
    }

    return {
      membershipType: user.membershipType,
      membershipExpireDate: user.membershipExpireDate,
      hasTeamPermission: user.hasTeamPermission,
      isActive: !!isActive,
      remainingDays
    };
  }

  /**
   * 更新会员信息
   */
  async updateMembership(userId: number, updateMembershipDto: UpdateMembershipDto) {
    // 使用 UserValidationService 验证用户存在性
    await this.userValidationService.validateUserById(userId);

    const updateData: Partial<User> = {
      membershipType: updateMembershipDto.membershipType,
      membershipExpireDate: new Date(updateMembershipDto.membershipExpireDate)
    };

    if (typeof updateMembershipDto.hasTeamPermission === 'boolean') {
      updateData.hasTeamPermission = updateMembershipDto.hasTeamPermission;
    }

    await this.userRepository.update(userId, updateData);

    return {
      message: "会员信息更新成功",
    };
  }

  /**
   * 微信一键登录
   */
  async wechatLogin(wechatLoginDto: WechatLoginDto) {
    // 1. 调用微信官方API获取openid
    const openidResult = await getWechatOpenid(wechatLoginDto.code, this.configService);

    if (!openidResult || !openidResult.openid) {
      throw new BadRequestException("微信登录失败，无法获取用户信息");
    }

    // 2. 根据openid查找用户
    let user = await this.userRepository.findByOpenid(openidResult.openid);
    let isNewUser = false;

    if (!user) {
      // 3. 用户不存在，创建新用户
      user = await this.createWechatUser(openidResult.openid);
      isNewUser = true;
    }

    // 检查用户是否激活
    this.userValidationService.validateUserActive(user);

    // 4. 更新登录信息
    await this.userRepository.updateLoginInfo(user.id);

    // 5. 生成JWT token
    const payload = {
      sub: user.id,
      phoneNumber: user.phoneNumber,
      openidWx: user.openidWx,
      role: user.role,
      userType: user.userType,
      permissionLevel: user.permissionLevel,
      canInviteOthers: user.canInviteOthers,
      hasShareProfitRights: user.hasShareProfitRights,
    };
    const token = this.jwtService.sign(payload);

    // 6. 返回用户信息（不包含密码）和token
    const { password: userPassword, ...result } = user;
    return {
      user: result,
      token: token,
      message: isNewUser ? "注册并登录成功" : "登录成功",
      isNewUser
    };
  }

  /**
   * 创建微信用户
   */
  private async createWechatUser(openid: string): Promise<User> {
    return await this.entityRepository.manager.transaction(async (manager) => {
      // 生成唯一的邀请码
      const inviteCode = await generateUniqueInviteCode(manager);

      // 生成随机用户名
      const randomUserName = generateRandomUserName();

      // 创建新用户
      const user = new User();
      user.name = randomUserName;
      user.email = null;
      user.password = Math.random().toString(36).slice(-8); 
      user.phoneNumber = null; 
      user.openidWx = openid;
      user.role = Role.User;
      user.userType = UserType.Individual;
      user.myInviteCode = inviteCode;
      user.permissionLevel = PermissionLevel.Individual;
      user.canInviteOthers = false;
      user.hasShareProfitRights = false;

      return await manager.save(User, user);
    });
  }

  /**
   * 解密微信用户信息
   */
  async decryptUserInfo(decryptUserInfoDto: DecryptUserInfoDto): Promise<DecryptedUserInfoResponseDto> {
    try {
      let sessionKey: string;

      // 1. 获取session_key（优先使用直接传递的sessionKey，否则通过code获取）
      if (decryptUserInfoDto.sessionKey) {
        sessionKey = decryptUserInfoDto.sessionKey;
      } else if (decryptUserInfoDto.code) {
        const sessionResult = await getWechatOpenid(decryptUserInfoDto.code, this.configService);
        if (!sessionResult || !sessionResult.session_key) {
          throw new BadRequestException("无法获取微信会话密钥");
        }
        sessionKey = sessionResult.session_key;
      } else {
        throw new BadRequestException("必须提供code或sessionKey其中之一");
      }

      // 2. 获取微信配置
      const wechatConfig = this.configService.get('wechat');

      // 3. 创建解密工具实例
      const decryptUtil = new WechatDecryptUtil(wechatConfig.appid, sessionKey);

      // 4. 根据数据类型进行解密
      let decryptedData: any;

      if (decryptUserInfoDto.dataType === 'phoneNumber') {
        decryptedData = decryptUtil.decryptPhoneNumber(
          decryptUserInfoDto.encryptedData,
          decryptUserInfoDto.iv
        );
      } else {
        // 默认为用户信息解密
        decryptedData = decryptUtil.decryptUserInfo(
          decryptUserInfoDto.encryptedData,
          decryptUserInfoDto.iv
        );
      }

      return decryptedData;
    } catch (error) {
      console.error('微信用户信息解密失败:', error);
      throw new BadRequestException(`解密失败: ${error.message}`);
    }
  }
}
