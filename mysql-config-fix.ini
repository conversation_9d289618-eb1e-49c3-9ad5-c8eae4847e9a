# MySQL配置文件修复方案
# 将以下内容添加到MySQL配置文件中

# Windows: C:\ProgramData\MySQL\MySQL Server 8.0\my.ini
# Linux/Mac: /etc/mysql/my.cnf

[client]
default-character-set = utf8mb4

[mysql]
default-character-set = utf8mb4

[mysqld]
character-set-server = utf8mb4
collation-server = utf8mb4_unicode_ci
init-connect = 'SET NAMES utf8mb4 COLLATE utf8mb4_unicode_ci'
init-connect = 'SET character_set_client = utf8mb4'
init-connect = 'SET character_set_connection = utf8mb4'
init-connect = 'SET character_set_results = utf8mb4'

# 重启MySQL服务后生效
# Windows: net stop mysql80 && net start mysql80
# Linux: sudo systemctl restart mysql 