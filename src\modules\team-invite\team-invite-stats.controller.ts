import {
  Controller,
  Get,
  Query,
  Request,
  UseGuards,
  UnauthorizedException,
} from '@nestjs/common';
import { TeamInviteStatsService } from './team-invite-stats.service';
import { 
  StatsQueryDto, 
  TrendQueryDto, 
  RankingQueryDto, 
  ActivityQueryDto, 
  FeatureQueryDto 
} from './dto/stats-query.dto';
import { AuthGuard } from '../../guards/auth.guard';
import { ApiTags, ApiOperation, ApiResponse, ApiBearerAuth } from '@nestjs/swagger';

@Controller('invite-stats')
@ApiTags('邀请统计')
@UseGuards(AuthGuard)
@ApiBearerAuth()
export class TeamInviteStatsController {
  constructor(private readonly inviteStatsService: TeamInviteStatsService) {}

  @Get('overview')
  @ApiOperation({ summary: '获取邀请用户统计概览' })
  @ApiResponse({
    status: 200,
    description: '获取成功',
    schema: {
      example: {
        code: 200,
        message: '请求成功',
        data: {
          totalInvitedUsers: 15,
          activeUsers: 12,
          avgUsagePerUser: 25.6,
          activeRate: 80.0,
          totalUsage: 384,
          growthRate: 15.5,
          timeRange: '30d'
        }
      }
    }
  })
  async getOverview(@Request() req: any, @Query() query: StatsQueryDto) {
    const userId = parseInt(req.user.sub, 10);
    if (isNaN(userId)) {
      throw new UnauthorizedException('无效的用户信息');
    }
    return await this.inviteStatsService.getOverview(userId, query);
  }

  @Get('usage-trend')
  @ApiOperation({ summary: '获取使用趋势数据（ECharts格式）' })
  @ApiResponse({
    status: 200,
    description: '获取成功',
    schema: {
      example: {
        code: 200,
        message: '请求成功',
        data: {
          chartData: {
            xAxis: { type: 'category', data: ['2024-01-01', '2024-01-02'] },
            yAxis: { type: 'value', name: '使用次数' },
            series: [
              { name: '总使用次数', type: 'line', data: [25, 30] },
              { name: '活跃用户数', type: 'line', data: [8, 10] }
            ]
          },
          overview: {
            totalInvitedUsers: 15,
            activeUsers: 12,
            avgUsagePerUser: 25.6,
            activeRate: 80.0,
            totalUsage: 384,
            growthRate: 15.5,
            timeRange: '30d'
          },
          timeRange: '30d'
        }
      }
    }
  })
  async getUsageTrend(@Request() req: any, @Query() query: TrendQueryDto) {
    const userId = parseInt(req.user.sub, 10);
    if (isNaN(userId)) {
      throw new UnauthorizedException('无效的用户信息');
    }
    return await this.inviteStatsService.getUsageTrend(userId, query);
  }

  @Get('user-ranking')
  @ApiOperation({ summary: '获取用户活跃度排名（ECharts格式）' })
  @ApiResponse({
    status: 200,
    description: '获取成功',
    schema: {
      example: {
        code: 200,
        message: '请求成功',
        data: {
          chartData: {
            xAxis: { type: 'category', data: ['用户A', '用户B', '用户C'] },
            yAxis: { type: 'value', name: '使用次数' },
            series: [{ name: '使用次数', type: 'bar', data: [45, 38, 32] }]
          },
          userList: [
            {
              userId: 1,
              userName: '用户A',
              userEmail: '<EMAIL>',
              rank: 1,
              usageCount: 45,
              lastActiveDate: '2024-01-15T10:30:00.000Z',
              joinDate: '2024-01-01T00:00:00.000Z'
            }
          ],
          totalUsers: 10
        }
      }
    }
  })
  async getUserRanking(@Request() req: any, @Query() query: RankingQueryDto) {
    const userId = parseInt(req.user.sub, 10);
    if (isNaN(userId)) {
      throw new UnauthorizedException('无效的用户信息');
    }
    return await this.inviteStatsService.getUserRanking(userId, query);
  }

  @Get('daily-activity')
  @ApiOperation({ summary: '获取每日活跃度热力图数据（ECharts格式）' })
  @ApiResponse({
    status: 200,
    description: '获取成功',
    schema: {
      example: {
        code: 200,
        message: '请求成功',
        data: {
          chartData: {
            tooltip: { position: 'top' },
            xAxis: { type: 'category', data: ['周一', '周二', '周三'] },
            yAxis: { type: 'category', data: ['第1周', '第2周'] },
            visualMap: { min: 0, max: 20 },
            series: [{ name: '活跃度', type: 'heatmap', data: [[0, 0, 15], [1, 0, 12]] }]
          },
          dailyData: [
            { date: '2024-01-01', value: 15, usageCount: 25 },
            { date: '2024-01-02', value: 12, usageCount: 20 }
          ],
          timeRange: '30d'
        }
      }
    }
  })
  async getDailyActivity(@Request() req: any, @Query() query: ActivityQueryDto) {
    const userId = parseInt(req.user.sub, 10);
    if (isNaN(userId)) {
      throw new UnauthorizedException('无效的用户信息');
    }
    return await this.inviteStatsService.getDailyActivity(userId, query);
  }

  @Get('feature-usage')
  @ApiOperation({ summary: '获取功能使用分布数据（ECharts格式）' })
  @ApiResponse({
    status: 200,
    description: '获取成功',
    schema: {
      example: {
        code: 200,
        message: '请求成功',
        data: {
          chartData: {
            tooltip: { trigger: 'item' },
            legend: { data: ['AI解读', '评估查询'] },
            series: [{
              name: '功能使用',
              type: 'pie',
              radius: '50%',
              data: [
                { name: 'AI解读', value: 45 },
                { name: '评估查询', value: 35 }
              ]
            }]
          },
          featureList: [
            { name: 'AI解读', value: 45, percentage: 45 },
            { name: '评估查询', value: 35, percentage: 35 }
          ],
          totalUsage: 100
        }
      }
    }
  })
  async getFeatureUsage(@Request() req: any, @Query() query: FeatureQueryDto) {
    const userId = parseInt(req.user.sub, 10);
    if (isNaN(userId)) {
      throw new UnauthorizedException('无效的用户信息');
    }
    return await this.inviteStatsService.getFeatureUsage(userId, query);
  }
} 