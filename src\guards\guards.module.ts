import { Modu<PERSON> } from "@nestjs/common";
import { JwtModule } from "@nestjs/jwt";
import { ConfigModule, ConfigService } from "@nestjs/config";
import { AuthGuard } from "./auth.guard";
import { PermissionGuard, UsageLimitGuard, UserValidationService } from "./permission.guard";
import { RepositoriesModule } from "../repositories/repositories.module";
import { RedisModule } from "../modules/redis/redis.module";
import { CaptchaModule } from "../modules/captcha/captcha.module";
import { UsersService } from "../modules/users/users.service";
import { TypeOrmModule } from "@nestjs/typeorm";
import { User } from "../modules/users/entities/user.entity";

@Module({
  imports: [
    ConfigModule,
    JwtModule.registerAsync({
      useFactory: async (configService: ConfigService) => ({
        secret: configService.get("jwt.secret"),
        signOptions: {
          expiresIn: configService.get("jwt.expiresIn") || "7d"
        },
      }),
      inject: [ConfigService],
    }),
    TypeOrmModule.forFeature([User]),
    RepositoriesModule,
    RedisModule,
    CaptchaModule,
  ],
  providers: [AuthGuard, PermissionGuard, UsageLimitGuard, UserValidationService, UsersService],
  exports: [AuthGuard, PermissionGuard, UsageLimitGuard, UserValidationService, JwtModule, UsersService],
})
export class GuardsModule { } 