import {
  Controller,
  Get,
  Post,
  Body,
  Param,
  Delete,
  UseGuards,
  Request,
  UnauthorizedException,
  ParseIntPipe,
} from '@nestjs/common';
import { TeamInviteService } from './team-invite.service';
import { CreateInviteDto } from './dto/create-invite.dto';
import { AcceptInviteDto } from './dto/accept-invite.dto';
import { AuthGuard } from '../../guards/auth.guard';
import { ApiTags, ApiOperation, ApiResponse, ApiBearerAuth } from '@nestjs/swagger';
import { InviteResponseDto, MyInviteInfoDto, MyInvitedUsersDto } from './dto/invite-response.dto';

@Controller('team-invite')
@ApiTags('团队邀请')
@UseGuards(AuthGuard)
@ApiBearerAuth()
export class TeamInviteController {
  constructor(private readonly teamInviteService: TeamInviteService) {}

  @Get('my-invite-code')
  @ApiOperation({ summary: '获取我的永久邀请码' })
  @ApiResponse({
    status: 200,
    description: '获取成功',
    schema: {
      example: {
        code: 200,
        message: '请求成功',
        data: {
          inviteCode: 'ABC12345',
          inviteLink: 'http://localhost:3000/invite/ABC12345'
        }
      }
    }
  })
  @ApiResponse({ status: 401, description: '未授权' })
  @ApiResponse({ status: 403, description: '没有邀请权限' })
  async getMyInviteCode(@Request() req: any) {
    const userId = parseInt(req.user.sub, 10);
    if (isNaN(userId)) {
      throw new UnauthorizedException('无效的用户信息');
    }
    return await this.teamInviteService.getMyInviteCode(userId);
  }

  @Get('check/:inviteCode')
  @ApiOperation({ summary: '检查邀请码是否有效' })
  @ApiResponse({
    status: 200,
    description: '检查成功',
    schema: {
      example: {
        code: 200,
        message: '请求成功',
        data: {
          valid: true,
          inviterName: '张三',
          inviterId: 1
        }
      }
    }
  })
  @ApiResponse({ status: 401, description: '未授权' })
  async checkInviteCode(@Param('inviteCode') inviteCode: string) {
    return await this.teamInviteService.checkInviteCode(inviteCode);
  }

  @Post('accept')
  @ApiOperation({ summary: '接受邀请' })
  @ApiResponse({
    status: 200,
    description: '邀请接受成功',
    schema: {
      example: {
        code: 200,
        message: '请求成功',
        data: {
          success: true,
          message: '邀请接受成功，您已获得团队权限',
          teamPermissionGranted: true
        }
      }
    }
  })
  @ApiResponse({ status: 400, description: '邀请码无效或已过期' })
  @ApiResponse({ status: 401, description: '未授权' })
  async acceptInvite(@Request() req: any, @Body() acceptInviteDto: AcceptInviteDto) {
    const userId = parseInt(req.user.sub, 10);
    if (isNaN(userId)) {
      throw new UnauthorizedException('无效的用户信息');
    }
    return await this.teamInviteService.acceptInvite(userId, acceptInviteDto);
  }

  @Get('my-info')
  @ApiOperation({ summary: '获取我的邀请信息' })
  @ApiResponse({
    status: 200,
    description: '获取成功',
    type: MyInviteInfoDto,
    schema: {
      example: {
        code: 200,
        message: '请求成功',
        data: {
          myInviteCode: 'ABC12345',
          inviteCount: 5,
          canInviteOthers: true,
          stats: {
            totalInvites: 5,
            successfulInvites: 5,
            pendingInvites: 0,
            expiredInvites: 0,
            successRate: 100
          },
          sentInvites: []
        }
      }
    }
  })
  @ApiResponse({ status: 401, description: '未授权' })
  async getMyInviteInfo(@Request() req: any) {
    const userId = parseInt(req.user.sub, 10);
    if (isNaN(userId)) {
      throw new UnauthorizedException('无效的用户信息');
    }
    return await this.teamInviteService.getMyInviteInfo(userId);
  }

  @Get('my-invited-users')
  @ApiOperation({ summary: '获取使用我邀请码的用户详细信息' })
  @ApiResponse({
    status: 200,
    description: '获取成功',
    type: MyInvitedUsersDto,
    schema: {
      example: {
        code: 200,
        message: '请求成功',
        data: {
          myInviteCode: 'ABC12345',
          totalCount: 3,
          invitedUsers: [
            {
              id: 2,
              name: '李四',
              email: '<EMAIL>',
              phoneNumber: '13800138001',
              joinedAt: '2024-01-15T10:30:00.000Z',
              permissionLevel: 'team',
              userType: 'individual',
              membershipType: 'free'
            },
            {
              id: 3,
              name: '王五',
              email: '<EMAIL>',
              phoneNumber: '13800138002',
              joinedAt: '2024-01-16T14:20:00.000Z',
              permissionLevel: 'team',
              userType: 'individual',
              membershipType: 'basic'
            }
          ],
          summary: {
            totalUsers: 3,
            activeUsers: 3,
            teamUsers: 3,
            individualUsers: 0
          }
        }
      }
    }
  })
  @ApiResponse({ status: 401, description: '未授权' })
  async getMyInvitedUsers(@Request() req: any) {
    const userId = parseInt(req.user.sub, 10);
    if (isNaN(userId)) {
      throw new UnauthorizedException('无效的用户信息');
    }
    return await this.teamInviteService.getMyInvitedUsers(userId);
  }

  @Get('my-invited-users-count')
  @ApiOperation({ summary: '获取使用我邀请码的用户数量统计' })
  @ApiResponse({
    status: 200,
    description: '获取成功',
    schema: {
      example: {
        code: 200,
        message: '请求成功',
        data: {
          myInviteCode: 'ABC12345',
          totalCount: 5,
          activeCount: 4,
          teamCount: 3
        }
      }
    }
  })
  @ApiResponse({ status: 401, description: '未授权' })
  async getMyInvitedUsersCount(@Request() req: any) {
    const userId = parseInt(req.user.sub, 10);
    if (isNaN(userId)) {
      throw new UnauthorizedException('无效的用户信息');
    }
    return await this.teamInviteService.getMyInvitedUsersCount(userId);
  }

  // 保留旧的API以兼容现有前端，但标记为废弃
  @Post()
  @ApiOperation({ 
    summary: '创建邀请码（已废弃）',
    deprecated: true,
    description: '该API已废弃，请使用 GET /my-invite-code 获取永久邀请码'
  })
  @ApiResponse({
    status: 200,
    description: '返回用户的永久邀请码',
    type: InviteResponseDto
  })
  @ApiResponse({ status: 401, description: '未授权' })
  @ApiResponse({ status: 403, description: '没有邀请权限' })
  async createInvite(@Request() req: any, @Body() createInviteDto: CreateInviteDto) {
    const userId = parseInt(req.user.sub, 10);
    if (isNaN(userId)) {
      throw new UnauthorizedException('无效的用户信息');
    }
    return await this.teamInviteService.createInvite(userId, createInviteDto);
  }

  @Get('detail/:id')
  @ApiOperation({ 
    summary: '获取邀请记录详情（已废弃）',
    deprecated: true,
    description: '该API已废弃，永久邀请码系统不再需要此功能'
  })
  @ApiResponse({ status: 400, description: '该功能已废弃' })
  async getInviteDetail(@Request() req: any, @Param('id', ParseIntPipe) id: number) {
    const userId = parseInt(req.user.sub, 10);
    if (isNaN(userId)) {
      throw new UnauthorizedException('无效的用户信息');
    }
    return await this.teamInviteService.getInviteDetail(id, userId);
  }

  @Delete(':id')
  @ApiOperation({ 
    summary: '取消邀请（已废弃）',
    deprecated: true,
    description: '该API已废弃，永久邀请码无法取消'
  })
  @ApiResponse({ status: 400, description: '永久邀请码无法取消' })
  async cancelInvite(@Request() req: any, @Param('id', ParseIntPipe) id: number) {
    const userId = parseInt(req.user.sub, 10);
    if (isNaN(userId)) {
      throw new UnauthorizedException('无效的用户信息');
    }
    return await this.teamInviteService.cancelInvite(id, userId);
  }

  @Post('cleanup-expired')
  @ApiOperation({ 
    summary: '清理过期邀请（已废弃）',
    deprecated: true,
    description: '该API已废弃，永久邀请码不会过期'
  })
  @ApiResponse({
    status: 200,
    description: '永久邀请码不会过期',
    schema: {
      example: {
        code: 200,
        message: '请求成功',
        data: {
          count: 0
        }
      }
    }
  })
  async cleanupExpiredInvites() {
    return await this.teamInviteService.cleanupExpiredInvites();
  }
} 