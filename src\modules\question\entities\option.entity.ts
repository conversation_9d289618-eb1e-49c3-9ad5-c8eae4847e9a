import { <PERSON>tity, PrimaryGeneratedColumn, Column, ManyToOne } from 'typeorm';
import { Question } from './question.entity';
import { ApiProperty } from '@nestjs/swagger';

@Entity()
export class Option {
  @PrimaryGeneratedColumn()
  id: number;

  @ApiProperty()
  @Column()
  letter: string;

  @ApiProperty()
  @Column()
  text: string;

  @ManyToOne(() => Question, (question) => question.options)
  question: Question;
}
