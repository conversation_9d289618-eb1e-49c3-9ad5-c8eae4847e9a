import { Entity, PrimaryGeneratedColumn, Column, CreateDateColumn, UpdateDateColumn } from 'typeorm';

@Entity('query_templates')
export class QueryTemplate {
    @PrimaryGeneratedColumn()
    id: number;

    @Column({ type: 'varchar', length: 100, comment: '模板名称' })
    name: string;

    @Column({ type: 'text', comment: '模板描述' })
    description: string;

    @Column({ type: 'json', comment: '查询数据' })
    data: any;

    @Column({ type: 'varchar', length: 50, default: 'active', comment: '状态' })
    status: string;

    @CreateDateColumn({ comment: '创建时间' })
    createdAt: Date;

    @UpdateDateColumn({ comment: '更新时间' })
    updatedAt: Date;
} 