import { IsEnum, IsOptional, IsString, IsDateString, IsInt, Min, Max } from 'class-validator';
import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { InviteType } from '../entities/team-invite.entity';
import { Transform } from 'class-transformer';

export class CreateInviteDto {
  @ApiProperty({ 
    enum: InviteType, 
    description: '邀请类型（已废弃：永久邀请码统一为DIRECT类型）', 
    default: InviteType.DIRECT,
    example: InviteType.DIRECT,
    deprecated: true
  })
  @IsEnum(InviteType, { message: '邀请类型格式不正确' })
  type: InviteType;

  @ApiPropertyOptional({ 
    description: '邀请过期时间（已废弃：永久邀请码不会过期）', 
    example: '2024-12-31T23:59:59.000Z',
    deprecated: true
  })
  @IsOptional()
  @IsDateString({}, { message: '过期时间格式不正确' })
  expiresAt?: string;

  @ApiPropertyOptional({ 
    description: '邀请备注', 
    example: '邀请同事加入团队' 
  })
  @IsOptional()
  @IsString()
  note?: string;

  @ApiPropertyOptional({ 
    description: '邀请链接有效期（已废弃：永久邀请码不会过期）', 
    example: 24,
    minimum: 1,
    maximum: 168,
    deprecated: true
  })
  @IsOptional()
  @IsInt()
  @Min(1, { message: '有效期最少1小时' })
  @Max(168, { message: '有效期最多168小时（7天）' })
  @Transform(({ value }) => parseInt(value))
  validHours?: number;
} 