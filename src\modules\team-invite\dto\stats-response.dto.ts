import { ApiProperty } from '@nestjs/swagger';

// ECharts基础数据格式
export class EChartsSeriesDto {
  @ApiProperty({ description: '系列名称' })
  name: string;

  @ApiProperty({ description: '图表类型' })
  type: string;

  @ApiProperty({ description: '数据数组' })
  data: any[];

  [key: string]: any;
}

export class EChartsDataDto {
  @ApiProperty({ description: 'X轴配置' })
  xAxis: {
    type: string;
    data: any[];
    [key: string]: any;
  };

  @ApiProperty({ description: 'Y轴配置' })
  yAxis: {
    type: string;
    data?: any[];
    [key: string]: any;
  };

  @ApiProperty({ description: '数据系列', type: [EChartsSeriesDto] })
  series: EChartsSeriesDto[];

  @ApiProperty({ description: '图例配置', required: false })
  legend?: {
    data: string[];
    [key: string]: any;
  };

  [key: string]: any;
}

// 统计概览响应
export class StatsOverviewDto {
  @ApiProperty({ description: '总邀请用户数' })
  totalInvitedUsers: number;

  @ApiProperty({ description: '活跃用户数' })
  activeUsers: number;

  @ApiProperty({ description: '平均使用次数' })
  avgUsagePerUser: number;

  @ApiProperty({ description: '活跃率（百分比）' })
  activeRate: number;

  @ApiProperty({ description: '总使用次数' })
  totalUsage: number;

  @ApiProperty({ description: '增长率（百分比）' })
  growthRate: number;

  @ApiProperty({ description: '时间范围' })
  timeRange: string;
}

// 用户活跃度数据
export class UserActivityDto {
  @ApiProperty({ description: '用户ID' })
  userId: number;

  @ApiProperty({ description: '用户名称' })
  userName: string;

  @ApiProperty({ description: '用户邮箱' })
  userEmail: string;

  @ApiProperty({ description: '加入时间' })
  joinDate: Date;

  @ApiProperty({ description: '最后活跃时间' })
  lastActiveDate: Date;

  @ApiProperty({ description: '总使用次数' })
  totalUsage: number;

  @ApiProperty({ description: '最近使用次数' })
  recentUsage: number;

  @ApiProperty({ description: '活跃天数' })
  activeDays: number;

  @ApiProperty({ description: '功能使用分布' })
  featureUsage: Record<string, number>;
}

// 用户排名数据
export class UserRankingDto {
  @ApiProperty({ description: '用户ID' })
  userId: number;

  @ApiProperty({ description: '用户名称' })
  userName: string;

  @ApiProperty({ description: '用户邮箱' })
  userEmail: string;

  @ApiProperty({ description: '排名' })
  rank: number;

  @ApiProperty({ description: '使用次数' })
  usageCount: number;

  @ApiProperty({ description: '最后活跃时间' })
  lastActiveDate: Date;

  @ApiProperty({ description: '加入时间' })
  joinDate: Date;
}

// 每日活跃度数据（热力图格式）
export class DailyActivityDto {
  @ApiProperty({ description: '日期' })
  date: string;

  @ApiProperty({ description: '活跃用户数' })
  value: number;

  @ApiProperty({ description: '使用次数' })
  usageCount: number;
}

// 功能使用分布数据（饼图格式）
export class FeatureUsageDto {
  @ApiProperty({ description: '功能名称' })
  name: string;

  @ApiProperty({ description: '使用次数' })
  value: number;

  @ApiProperty({ description: '占比（百分比）' })
  percentage: number;
}

// 趋势数据响应
export class TrendDataDto {
  @ApiProperty({ description: 'ECharts数据格式' })
  chartData: EChartsDataDto;

  @ApiProperty({ description: '统计概览' })
  overview: StatsOverviewDto;

  @ApiProperty({ description: '时间范围' })
  timeRange: string;
}

// 排名数据响应
export class RankingDataDto {
  @ApiProperty({ description: 'ECharts数据格式' })
  chartData: EChartsDataDto;

  @ApiProperty({ description: '用户排名列表', type: [UserRankingDto] })
  userList: UserRankingDto[];

  @ApiProperty({ description: '总用户数' })
  totalUsers: number;
}

// 活跃度数据响应
export class ActivityDataDto {
  @ApiProperty({ description: 'ECharts热力图数据格式' })
  chartData: EChartsDataDto;

  @ApiProperty({ description: '每日活跃度数据', type: [DailyActivityDto] })
  dailyData: DailyActivityDto[];

  @ApiProperty({ description: '时间范围' })
  timeRange: string;
}

// 功能使用数据响应
export class FeatureUsageDataDto {
  @ApiProperty({ description: 'ECharts饼图数据格式' })
  chartData: EChartsDataDto;

  @ApiProperty({ description: '功能使用分布', type: [FeatureUsageDto] })
  featureList: FeatureUsageDto[];

  @ApiProperty({ description: '总使用次数' })
  totalUsage: number;
} 