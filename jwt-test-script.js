/**
 * JWT接口测试脚本
 * 使用方法：node jwt-test-script.js
 */

const http = require('http');
const https = require('https');

// 配置
const config = {
  host: 'localhost',
  port: 3000,
  protocol: 'http' // 可以改为 'https'
};

// 工具函数：发送HTTP请求
function makeRequest(options, data = null) {
  return new Promise((resolve, reject) => {
    const client = options.protocol === 'https' ? https : http;
    
    const req = client.request(options, (res) => {
      let body = '';
      
      res.on('data', (chunk) => {
        body += chunk;
      });
      
      res.on('end', () => {
        try {
          const parsedBody = JSON.parse(body);
          resolve({
            statusCode: res.statusCode,
            headers: res.headers,
            body: parsedBody
          });
        } catch (error) {
          resolve({
            statusCode: res.statusCode,
            headers: res.headers,
            body: body
          });
        }
      });
    });
    
    req.on('error', (error) => {
      reject(error);
    });
    
    if (data) {
      req.write(JSON.stringify(data));
    }
    
    req.end();
  });
}

// 测试用例
async function testJWTInterfaces() {
  console.log('=== JWT接口测试开始 ===\n');
  
  let jwtToken = null;
  
  try {
    // 1. 测试生成测试token
    console.log('1. 测试生成测试token...');
    const generateTokenOptions = {
      hostname: config.host,
      port: config.port,
      path: '/jwt-test/generate-test-token',
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      }
    };
    
    const generateResult = await makeRequest(generateTokenOptions, {
      userId: "123",
      name: "测试用户",
      email: "<EMAIL>",
      role: "user"
    });
    
    if (generateResult.statusCode === 200) {
      jwtToken = generateResult.body.data.token;
      console.log('✅ 生成测试token成功');
      console.log('Token:', jwtToken.substring(0, 50) + '...');
    } else {
      console.log('❌ 生成测试token失败:', generateResult.body);
    }
    
    console.log('\n' + '='.repeat(50) + '\n');
    
    // 2. 测试token验证
    console.log('2. 测试token验证...');
    const verifyTokenOptions = {
      hostname: config.host,
      port: config.port,
      path: '/jwt-test/verify-token',
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'X-JWT-Token': `Bearer ${jwtToken}`
      }
    };
    
    const verifyResult = await makeRequest(verifyTokenOptions);
    
    if (verifyResult.statusCode === 200) {
      console.log('✅ Token验证成功');
      console.log('Token信息:', {
        valid: verifyResult.body.data.valid,
        timeToExpire: verifyResult.body.data.timeToExpire,
        user: verifyResult.body.data.payload.name
      });
    } else {
      console.log('❌ Token验证失败:', verifyResult.body);
    }
    
    console.log('\n' + '='.repeat(50) + '\n');
    
    // 3. 测试认证守卫
    console.log('3. 测试认证守卫...');
    const testAuthOptions = {
      hostname: config.host,
      port: config.port,
      path: '/jwt-test/test-auth',
      method: 'GET',
      headers: {
        'X-JWT-Token': `Bearer ${jwtToken}`
      }
    };
    
    const authResult = await makeRequest(testAuthOptions);
    
    if (authResult.statusCode === 200) {
      console.log('✅ 认证守卫测试成功');
      console.log('认证用户:', authResult.body.data.user.name);
    } else {
      console.log('❌ 认证守卫测试失败:', authResult.body);
    }
    
    console.log('\n' + '='.repeat(50) + '\n');
    
    // 4. 测试获取token详细信息
    console.log('4. 测试获取token详细信息...');
    const tokenInfoOptions = {
      hostname: config.host,
      port: config.port,
      path: '/jwt-test/token-info',
      method: 'GET',
      headers: {
        'X-JWT-Token': `Bearer ${jwtToken}`
      }
    };
    
    const tokenInfoResult = await makeRequest(tokenInfoOptions);
    
    if (tokenInfoResult.statusCode === 200) {
      console.log('✅ 获取token详细信息成功');
      console.log('Token信息:', {
        header: tokenInfoResult.body.data.header,
        issuedAt: tokenInfoResult.body.data.issuedAt,
        expiresAt: tokenInfoResult.body.data.expiresAt,
        timeToExpire: tokenInfoResult.body.data.timeToExpire
      });
    } else {
      console.log('❌ 获取token详细信息失败:', tokenInfoResult.body);
    }
    
    console.log('\n' + '='.repeat(50) + '\n');
    
    // 5. 测试权限检查
    console.log('5. 测试权限检查...');
    const permissionsOptions = {
      hostname: config.host,
      port: config.port,
      path: '/jwt-test/test-permissions',
      method: 'GET',
      headers: {
        'X-JWT-Token': `Bearer ${jwtToken}`
      }
    };
    
    const permissionsResult = await makeRequest(permissionsOptions);
    
    if (permissionsResult.statusCode === 200) {
      console.log('✅ 权限检查成功');
      console.log('权限信息:', permissionsResult.body.data.permissions);
    } else {
      console.log('❌ 权限检查失败:', permissionsResult.body);
    }
    
    console.log('\n' + '='.repeat(50) + '\n');
    
    // 6. 测试刷新token
    console.log('6. 测试刷新token...');
    const refreshTokenOptions = {
      hostname: config.host,
      port: config.port,
      path: '/jwt-test/refresh-token',
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'X-JWT-Token': `Bearer ${jwtToken}`
      }
    };
    
    const refreshResult = await makeRequest(refreshTokenOptions);
    
    if (refreshResult.statusCode === 200) {
      console.log('✅ 刷新token成功');
      console.log('新Token:', refreshResult.body.data.newToken.substring(0, 50) + '...');
      console.log('过期时间:', refreshResult.body.data.expiresAt);
    } else {
      console.log('❌ 刷新token失败:', refreshResult.body);
    }
    
    console.log('\n' + '='.repeat(50) + '\n');
    
    // 7. 测试无效token
    console.log('7. 测试无效token...');
    const invalidTokenOptions = {
      hostname: config.host,
      port: config.port,
      path: '/jwt-test/test-auth',
      method: 'GET',
      headers: {
        'X-JWT-Token': 'Bearer invalid-token'
      }
    };
    
    const invalidResult = await makeRequest(invalidTokenOptions);
    
    if (invalidResult.statusCode === 401) {
      console.log('✅ 无效token测试成功 (正确拒绝)');
      console.log('错误信息:', invalidResult.body.message);
    } else {
      console.log('❌ 无效token测试失败 (应该返回401):', invalidResult.body);
    }
    
  } catch (error) {
    console.error('测试过程中发生错误:', error.message);
  }
  
  console.log('\n=== JWT接口测试完成 ===');
}

// 主函数
async function main() {
  console.log(`开始测试JWT接口 - 服务器: ${config.protocol}://${config.host}:${config.port}`);
  console.log('确保你的NestJS应用正在运行!\n');
  
  await testJWTInterfaces();
}

// 运行测试
main().catch(console.error); 