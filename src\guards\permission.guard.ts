import {
  Injectable,
  CanActivate,
  ExecutionContext,
  ForbiddenException,
  SetMetadata,
  UnauthorizedException,
  createParamDecorator,
  BadRequestException,
  Logger,
} from '@nestjs/common';
import { Reflector } from '@nestjs/core';
import { UserRepository } from '../repositories/user.repository';
import { PermissionLevel, User } from '../modules/users/entities/user.entity';

// 权限装饰器
export const RequirePermission = (level: PermissionLevel) =>
  SetMetadata('permission', level);

// 权限检查装饰器
export const RequireTeamPermission = () =>
  SetMetadata('permission', PermissionLevel.Team);

export const RequireIndividualPermission = () =>
  SetMetadata('permission', PermissionLevel.Individual);

// 用户验证装饰器
export const ValidateUser = () =>
  SetMetadata('validateUser', true);

// 用户验证服务
@Injectable()
export class UserValidationService {
  constructor(private userRepository: UserRepository) { }

  async validateUser(request: any): Promise<User> {
    const user = request.user;

    if (!user) {
      throw new UnauthorizedException('用户未登录');
    }

    const userId = parseInt(user.sub, 10);
    if (isNaN(userId)) {
      throw new UnauthorizedException('无效的用户信息');
    }

    const userInfo = await this.userRepository.findById(userId);
    if (!userInfo) {
      Logger.error('用户不存在', userInfo);
      throw new UnauthorizedException('用户不存在');
    }

    return userInfo;
  }

  // 根据ID验证用户存在性
  async validateUserById(userId: number): Promise<User> {
    const user = await this.userRepository.findById(userId);
    if (!user) {
      Logger.error('ID验证用户不存在', user);
      throw new BadRequestException('用户不存在');
    }
    return user;
  }

  // 根据手机号验证用户存在性
  async validateUserByPhone(phoneNumber: string): Promise<User> {
    const user = await this.userRepository.findByPhoneNumber(phoneNumber);
    if (!user) {
      Logger.error('手机号验证用户不存在', user);
      throw new BadRequestException('用户不存在');
    }
    return user;
  }

  // 根据openid验证用户存在性
  async validateUserByOpenid(openid: string): Promise<User> {
    const user = await this.userRepository.findByOpenid(openid);
    if (!user) {
      Logger.error('openid验证用户不存在', user);
      throw new BadRequestException('用户不存在');
    }
    return user;
  }

  // 检查用户是否激活
  validateUserActive(user: User): void {
    if (!user.isActive) {
      throw new BadRequestException('账户已被禁用');
    }
  }

  // 检查用户权限级别
  validateUserPermission(user: User, requiredLevel: PermissionLevel): void {
    if (requiredLevel === PermissionLevel.Team) {
      if (user.permissionLevel !== PermissionLevel.Team) {
        throw new ForbiddenException('此功能需要团队权限，请先开通团队权限或接受邀请');
      }
    } else if (requiredLevel === PermissionLevel.Individual) {
      if (user.permissionLevel !== PermissionLevel.Individual &&
        user.permissionLevel !== PermissionLevel.Team) {
        throw new ForbiddenException('权限不足');
      }
    }
  }

  // 检查使用次数
  validateUsageLimit(user: User): void {
    if (user.permissionLevel === PermissionLevel.Team) {
      return; // 团队权限不受使用次数限制
    }

    const hasUsageLeft = user.freeAssessmentCount > 0 ||
      user.freeAIInterpretationCount > 0;

    if (!hasUsageLeft) {
      throw new ForbiddenException('使用次数已用完，请开通团队权限或购买更多次数');
    }
  }
}

@Injectable()
export class PermissionGuard implements CanActivate {
  constructor(
    private reflector: Reflector,
    private userValidationService: UserValidationService
  ) { }

  async canActivate(context: ExecutionContext): Promise<boolean> {
    const requiredPermission = this.reflector.getAllAndOverride<PermissionLevel>('permission', [
      context.getHandler(),
      context.getClass(),
    ]);

    if (!requiredPermission) {
      return true; // 如果没有设置权限要求，则允许通过
    }

    const request = context.switchToHttp().getRequest();

    // 使用用户验证服务
    const userInfo = await this.userValidationService.validateUser(request);
    request.userInfo = userInfo;

    // 检查用户权限级别
    this.userValidationService.validateUserPermission(userInfo, requiredPermission);

    return true;
  }
}

// 使用次数检查守卫
@Injectable()
export class UsageLimitGuard implements CanActivate {
  constructor(private userValidationService: UserValidationService) { }

  async canActivate(context: ExecutionContext): Promise<boolean> {
    const request = context.switchToHttp().getRequest();

    // 使用用户验证服务
    const userInfo = await this.userValidationService.validateUser(request);
    request.userInfo = userInfo;

    // 检查使用次数
    this.userValidationService.validateUsageLimit(userInfo);

    return true;
  }
}

// 当前用户装饰器
export const CurrentUser = createParamDecorator(
  (data: unknown, ctx: ExecutionContext) => {
    const request = ctx.switchToHttp().getRequest();
    return request.userInfo; // PermissionGuard 或 UsageLimitGuard 已经设置了这个
  },
);

// 用户ID装饰器（用于需要根据ID查询用户的场景）
export const UserId = createParamDecorator(
  (data: unknown, ctx: ExecutionContext) => {
    const request = ctx.switchToHttp().getRequest();
    const user = request.user;
    if (!user) {
      throw new UnauthorizedException('用户未登录');
    }
    const userId = parseInt(user.sub, 10);
    if (isNaN(userId)) {
      throw new UnauthorizedException('无效的用户信息');
    }
    return userId;
  },
); 