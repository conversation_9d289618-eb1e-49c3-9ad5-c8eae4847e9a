import { ApiProperty } from '@nestjs/swagger';
import { IsString, IsNotEmpty } from 'class-validator';

/**
 * 微信登录请求DTO
 */
export class WechatLoginDto {
  @ApiProperty({ 
    description: '微信小程序登录凭证code',
    example: '061FGqIV0K0XcX1wKfIF0X8w8y0FGqIt'
  })
  @IsString({ message: 'code必须是字符串' })
  @IsNotEmpty({ message: 'code不能为空' })
  code: string;
}

/**
 * 微信登录响应DTO
 */
export class WechatLoginResponseDto {
  @ApiProperty({ description: '用户信息' })
  user: {
    id: number;
    name: string;
    openidWx: string;
    role: string;
    userType: string;
    membershipType: string;
    permissionLevel: string;
    freeAssessmentCount: number;
    freeAIInterpretationCount: number;
    isActive: boolean;
    createdAt: Date;
  };

  @ApiProperty({ description: 'JWT令牌' })
  token: string;

  @ApiProperty({ description: '响应消息' })
  message: string;

  @ApiProperty({ description: '是否为新用户' })
  isNewUser: boolean;
} 