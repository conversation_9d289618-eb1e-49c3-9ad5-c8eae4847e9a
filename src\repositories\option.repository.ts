import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { Option } from '../modules/question/entities/option.entity';

@Injectable()
export class OptionRepository {
  constructor(
    @InjectRepository(Option)
    private optionRepository: Repository<Option>
  ) { }

  async findByQuestionId(questionId: number): Promise<Option[]> {
    return this.optionRepository.find({
      where: { question: { id: questionId } },
      order: { id: 'ASC' }
    });
  }

  async create(data: Partial<Option>): Promise<Option> {
    const option = this.optionRepository.create(data);
    return this.optionRepository.save(option);
  }

  async update(id: number, data: Partial<Option>): Promise<void> {
    await this.optionRepository.update(id, data);
  }

  async delete(id: number): Promise<void> {
    await this.optionRepository.delete(id);
  }

  async deleteByQuestionId(questionId: number): Promise<void> {
    await this.optionRepository.delete({ question: { id: questionId } });
  }
} 