-- 紧急修复MySQL连接字符集问题
-- 在MySQL中执行以下命令

-- 1. 检查当前字符集状态
SHOW VARIABLES LIKE 'character_set%';
SHOW VARIABLES LIKE 'collation%';

-- 2. 设置当前会话的字符集
SET NAMES utf8mb4 COLLATE utf8mb4_unicode_ci;
SET character_set_client = utf8mb4;
SET character_set_connection = utf8mb4;
SET character_set_results = utf8mb4;

-- 3. 验证修改
SHOW VARIABLES LIKE 'character_set%';

-- 4. 测试emoji插入
INSERT INTO message (id, conversationId, role, type, content, status) 
VALUES (
    UUID(), 
    'test-conversation', 
    'assistant', 
    'text', 
    '测试🌟emoji🚀内容😊', 
    'completed'
);

-- 5. 查看插入结果
SELECT content FROM message WHERE content LIKE '%🌟%' LIMIT 1;

-- 6. 清理测试数据
DELETE FROM message WHERE conversationId = 'test-conversation'; 