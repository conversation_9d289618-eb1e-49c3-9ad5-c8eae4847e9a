import { MiddlewareConsumer, Module, NestModule } from '@nestjs/common';
import { ConfigModule, ConfigService } from '@nestjs/config';
import { AppController } from './app.controller';
import { AppService } from './app.service';
import { CommonModule } from './common/common.module';
import { DatabaseModule } from './database/database.module';
import { GuardsModule } from './guards/guards.module';
import { UsersModule } from './modules/users/users.module';
import { CaptchaModule } from './modules/captcha/captcha.module';
import { CardsModule } from './modules/cards/cards.module';
import { RedisModule } from './modules/redis/redis.module';
import { RepositoriesModule } from './repositories/repositories.module';
import { CozeModule } from './modules/coze/coze.module';
import { TeamInviteModule } from './modules/team-invite/team-invite.module';
import { PaymentModule } from './modules/payment/payment.module';
import { QueryTemplateModule } from './modules/query-template/query-template.module';
import { QuestionModule } from './modules/question/question.module';
import { UploadModule } from './modules/upload/upload.module';
import appConfig from './config/app.config';
import databaseConfig from './config/database.config';
import redisConfig from './config/redis.config';
import cozeConfig from './config/coze.config';
import jwtConfig from './config/jwt.config';
import wechatConfig from './config/wechat.config';
import wechatpayConfig from './config/wechatpay.config';
import smsConfig from './config/sms.config';
import { LoggerMiddleware } from './middleware/logger.middleware';

let envFilePath = ['.env'];
export const IS_DEV = process.env.RUNNING_ENV !== 'prod';

if (IS_DEV) {
  envFilePath.unshift('.env.dev');
} else {
  envFilePath.unshift('.env.prod');
}
@Module({
  imports: [
    ConfigModule.forRoot({
      isGlobal: true,
      envFilePath,
      load: [
        appConfig,
        databaseConfig,
        jwtConfig,
        redisConfig,
        cozeConfig,
        wechatConfig,
        wechatpayConfig,
        smsConfig,
      ],
    }),
    CommonModule,
    DatabaseModule,
    GuardsModule,
    UsersModule,
    CaptchaModule,
    CardsModule,
    RedisModule,
    RepositoriesModule,
    CozeModule,
    TeamInviteModule,
    PaymentModule,
    QueryTemplateModule,
    QuestionModule,
    UploadModule, 
  ],
  controllers: [AppController],
  providers: [AppService],
  exports: [AppService, RedisModule],
})
export class AppModule implements NestModule {
  configure(consumer: MiddlewareConsumer) {
    consumer.apply(LoggerMiddleware).forRoutes('*');
  }
}