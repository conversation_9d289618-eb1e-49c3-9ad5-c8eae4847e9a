import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { ConfigModule, ConfigService } from '@nestjs/config';
import { User } from '../modules/users/entities/user.entity';
import { Card } from '../modules/cards/entities/card.entity';
import { Captcha } from '../modules/captcha/entities/captcha.entity';
import { Conversation } from '../modules/coze/entities/conversation.entity';
import { Message } from '../modules/coze/entities/message.entity';
import { TeamInvite } from '../modules/team-invite/entities/team-invite.entity';
import { Payment } from '../modules/payment/entities/payment.entity';
import { QueryTemplate } from '../modules/query-template/entities/query-template.entity';
import { Question } from '../modules/question/entities/question.entity';
import { Option } from '../modules/question/entities/option.entity';
import { UserAnswerRecord } from 'src/modules/question/entities/user-answer-record.entity';
import { PersonalityResult } from 'src/modules/question/entities/personality-result.entity';

@Module({
  imports: [
    TypeOrmModule.forRootAsync({
      imports: [ConfigModule],
      useFactory: (configService: ConfigService) => ({
        type: 'mysql',
        host: configService.get('DB_HOST', 'localhost'),
        port: configService.get('DB_PORT', 3306),
        username: configService.get('DB_USERNAME', 'root'),
        password: configService.get('DB_PASSWORD', ''),
        database: configService.get('DB_NAME', 'kanli'),
        entities: [User, Card, Captcha, Conversation, Message, TeamInvite, Payment, QueryTemplate, Question, Option, UserAnswerRecord, PersonalityResult],
        synchronize: configService.get('NODE_ENV') !== 'production',
        charset: 'utf8mb4',
        extra: {
          connectionLimit: 10,
          acquireTimeout: 60000,
          timeout: 60000,
          // 关键：强制连接层使用utf8mb4
          charset: 'utf8mb4',
          // 设置连接初始化SQL
          initSql: [
            "SET NAMES utf8mb4 COLLATE utf8mb4_unicode_ci",
            "SET character_set_client = utf8mb4",
            "SET character_set_connection = utf8mb4", 
            "SET character_set_results = utf8mb4"
          ]
        },
      }),
      inject: [ConfigService],
    }),
  ],
})
export class DatabaseModule { }