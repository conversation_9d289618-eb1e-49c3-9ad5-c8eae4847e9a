import { NestFactory } from "@nestjs/core";
import { AppModule, IS_DEV } from "./app.module";
import { ConfigService } from "@nestjs/config";
import { DocumentBuilder, SwaggerModule } from "@nestjs/swagger";
import { ResponseInterceptor } from "./common/interceptors/response.interceptor";
import { HttpExceptionFilter } from "./common/filters/http-exception.filter";
import { ValidationPipe, Logger } from "@nestjs/common";
import { HealthCheckService } from "./common/services/health-check.service";
import * as fs from 'fs';
import * as path from 'path';
import { NestExpressApplication } from "@nestjs/platform-express";

async function bootstrap() {
  const logger = new Logger("Bootstrap");

  try {
    logger.log("🚀 正在启动 KanLi 应用...");

    const app = await NestFactory.create<NestExpressApplication>(AppModule);

    // 获取配置服务 
    const configService = app.get(ConfigService);

    // 使用配置文件中的CORS设置
    // const corsConfig = configService.get('app.cors');
    // app.enableCors({
    //   origin: corsConfig.origin === '*' ? true : corsConfig.origin,
    //   methods: ['GET', 'POST', 'PUT', 'DELETE', 'PATCH', 'OPTIONS'],
    //   allowedHeaders: [
    //     'Content-Type',
    //     'Authorization',
    //     'X-JWT-Token',
    //     'X-Requested-With',
    //     'Accept',
    //     'Origin',
    //     'Access-Control-Request-Method',
    //     'Access-Control-Request-Headers',
    //     'platform',
    //   ],
    //   exposedHeaders: [
    //     'Content-Length',
    //     'Content-Type',
    //     'Authorization',
    //     'X-JWT-Token',
    //   ],
    //   credentials: corsConfig.credentials,  // 使用配置文件中的值
    //   preflightContinue: false,
    //   optionsSuccessStatus: 204,
    // }); 
    app.enableCors({
      origin: (origin, callback) => {
        // 允许所有来源，但必须显式返回origin，才能与 credentials: true 配合使用
        callback(null, origin || '*');
      },
      credentials: true, // 允许携带cookie等凭证
      methods: 'GET,HEAD,PUT,PATCH,POST,DELETE,OPTIONS',
      allowedHeaders: '*',
      exposedHeaders: '*',
      preflightContinue: false,
      optionsSuccessStatus: 204,
    });

    app.useStaticAssets(path.join(__dirname, '..', 'public'));

    // 🔍 启动前检查所有外部服务连接
    logger.log("🔗 开始连接检查...");
    const healthCheckService = app.get(HealthCheckService);
    const allConnectionsHealthy =
      await healthCheckService.checkAllConnections();

    if (!allConnectionsHealthy) {
      logger.error("💥 外部服务连接失败，应用启动中止！");
      logger.error("请检查以下服务配置和可用性：");
      logger.error("1. 数据库服务是否启动并可访问");
      logger.error("2. Redis服务是否启动并可访问");
      logger.error("3. 网络连接是否正常");
      logger.error("4. 配置文件中的连接参数是否正确");

      // 关闭应用
      await app.close();
      process.exit(1);
    }

    // Swagger配置
    const swaggerConfig = configService.get("app.swagger");
    if (swaggerConfig.enabled) {
      const config = new DocumentBuilder()
        .setTitle(swaggerConfig.title)
        .setDescription(swaggerConfig.description)
        .setVersion(swaggerConfig.version)
        .addTag("kanli")
        .build();
      const document = SwaggerModule.createDocument(app, config);

      // 写入json文件
      fs.writeFileSync(
        path.resolve(__dirname, '../swagger-spec.json'),
        JSON.stringify(document, null, 2),
        { encoding: 'utf-8' },
      );

      SwaggerModule.setup("api-docs", app, document);
    }

    // 全局响应拦截器
    app.useGlobalInterceptors(new ResponseInterceptor());

    // 全局异常过滤器
    app.useGlobalFilters(new HttpExceptionFilter());

    // 全局验证管道
    app.useGlobalPipes(
      new ValidationPipe({
        whitelist: true,
        forbidNonWhitelisted: true,
        transform: true,
        disableErrorMessages: false,
      })
    );

    // 启动应用
    const port = configService.get("app.port");
    const env = configService.get("app.env");

    await app.listen(port);

    logger.log("🎉 KanLi 应用启动成功！");
    logger.log(`🌍 运行环境: ${env}`);
    logger.log(`🔗 应用地址: http://localhost:${port}`);
    if (swaggerConfig.enabled) {
      logger.log(`📖 API文档: http://localhost:${port}/api-docs`);
    }
    logger.log("✅ 所有服务已就绪，可以开始处理请求");
  } catch (error) {
    logger.error("💥 应用启动失败:", error);
    process.exit(1);
  }
}

// 处理未捕获的异常
process.on("unhandledRejection", (reason, promise) => {
  const logger = new Logger("UnhandledRejection");
  logger.error("未处理的Promise拒绝:", reason);
  process.exit(1);
});

process.on("uncaughtException", (error) => {
  const logger = new Logger("UncaughtException");
  logger.error("未捕获的异常:", error);
  process.exit(1);
});

bootstrap();
