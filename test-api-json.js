// 测试脚本：验证 /api-json 端点是否返回原始 JSON 数据
const http = require('http');

function testApiJson() {
  const options = {
    hostname: 'localhost',
    port: 4000,
    path: '/api-json',
    method: 'GET',
    headers: {
      'Accept': 'application/json'
    }
  };

  const req = http.request(options, (res) => {
    console.log(`状态码: ${res.statusCode}`);
    console.log(`响应头: ${JSON.stringify(res.headers, null, 2)}`);
    
    let data = '';
    res.on('data', (chunk) => {
      data += chunk;
    });
    
    res.on('end', () => {
      try {
        const jsonData = JSON.parse(data);
        
        // 检查是否是原始 Swagger JSON（应该有 openapi 字段）
        if (jsonData.openapi) {
          console.log('✅ 成功！返回的是原始 Swagger JSON 数据');
          console.log(`OpenAPI 版本: ${jsonData.openapi}`);
          console.log(`API 标题: ${jsonData.info?.title}`);
          console.log(`路径数量: ${Object.keys(jsonData.paths || {}).length}`);
        } else if (jsonData.code && jsonData.message) {
          console.log('❌ 失败！仍然返回包装后的响应格式');
          console.log('响应格式:', jsonData);
        } else {
          console.log('⚠️  未知响应格式');
          console.log('响应数据:', jsonData);
        }
      } catch (error) {
        console.log('❌ 解析 JSON 失败:', error.message);
        console.log('原始响应:', data);
      }
    });
  });

  req.on('error', (error) => {
    console.log('❌ 请求失败:', error.message);
  });

  req.end();
}

// 等待服务器启动后测试
setTimeout(() => {
  console.log('测试 /api-json 端点...');
  testApiJson();
}, 2000);
