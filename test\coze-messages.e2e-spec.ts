import { Test, TestingModule } from '@nestjs/testing';
import { INestApplication } from '@nestjs/common';
import * as request from 'supertest';
import { AppModule } from '../src/app.module';

describe('Coze Messages API (e2e)', () => {
  let app: INestApplication;
  let authToken: string;
  let conversationId: string;

  beforeAll(async () => {
    const moduleFixture: TestingModule = await Test.createTestingModule({
      imports: [AppModule],
    }).compile();

    app = moduleFixture.createNestApplication();
    await app.init();

    // 这里应该添加实际的认证逻辑
    // authToken = await getAuthToken();
    authToken = 'mock-jwt-token';
  });

  afterAll(async () => {
    await app.close();
  });

  describe('/coze/conversations/:id/messages (GET)', () => {
    it('should return 401 without authentication', () => {
      return request(app.getHttpServer())
        .get('/coze/conversations/550e8400-e29b-41d4-a716-446655440000/messages')
        .expect(401);
    });

    it('should return 404 for non-existent conversation', () => {
      return request(app.getHttpServer())
        .get('/coze/conversations/00000000-0000-0000-0000-000000000000/messages')
        .set('Authorization', `Bearer ${authToken}`)
        .expect(404);
    });

    it('should return messages with correct format', async () => {
      // 首先创建一个对话用于测试
      const createResponse = await request(app.getHttpServer())
        .post('/coze/conversations')
        .set('Authorization', `Bearer ${authToken}`)
        .send({
          title: '测试对话',
          botType: 'default'
        });

      if (createResponse.status === 200) {
        conversationId = createResponse.body.data.id;

        const response = await request(app.getHttpServer())
          .get(`/coze/conversations/${conversationId}/messages`)
          .set('Authorization', `Bearer ${authToken}`)
          .query({ page: 1, pageSize: 50 });

        expect(response.status).toBe(200);
        expect(response.body).toHaveProperty('code', 200);
        expect(response.body).toHaveProperty('message');
        expect(response.body).toHaveProperty('data');
        expect(response.body).toHaveProperty('pagination');
        expect(response.body).toHaveProperty('timestamp');
        expect(Array.isArray(response.body.data)).toBe(true);
      }
    });
  });

  describe('/coze/proto/conversations/:id/messages (GET)', () => {
    it('should return 404 for wrong bot type', async () => {
      if (conversationId) {
        const response = await request(app.getHttpServer())
          .get(`/coze/proto/conversations/${conversationId}/messages`)
          .set('Authorization', `Bearer ${authToken}`)
          .query({ page: 1, pageSize: 50 });

        expect(response.status).toBe(400);
        expect(response.body.message).toContain('对话类型不匹配');
      }
    });

    it('should return messages for proto conversation', async () => {
      // 创建原型智能体对话
      const createResponse = await request(app.getHttpServer())
        .post('/coze/proto/conversations')
        .set('Authorization', `Bearer ${authToken}`)
        .send({
          title: '原型测试对话',
          botType: 'proto'
        });

      if (createResponse.status === 200) {
        const protoConversationId = createResponse.body.data.id;

        const response = await request(app.getHttpServer())
          .get(`/coze/proto/conversations/${protoConversationId}/messages`)
          .set('Authorization', `Bearer ${authToken}`)
          .query({ page: 1, pageSize: 50 });

        expect(response.status).toBe(200);
        expect(response.body).toHaveProperty('code', 200);
        expect(response.body.message).toBe('原型智能体消息列表获取成功');
        expect(response.body).toHaveProperty('data');
        expect(response.body).toHaveProperty('pagination');
        expect(response.body).toHaveProperty('timestamp');
      }
    });
  });

  describe('/coze/interpretation/conversations/:id/messages (GET)', () => {
    it('should return messages for interpretation conversation', async () => {
      // 创建解读智能体对话
      const createResponse = await request(app.getHttpServer())
        .post('/coze/interpretation/conversations')
        .set('Authorization', `Bearer ${authToken}`)
        .send({
          title: '解读测试对话',
          botType: 'interpretation'
        });

      if (createResponse.status === 200) {
        const interpretationConversationId = createResponse.body.data.id;

        const response = await request(app.getHttpServer())
          .get(`/coze/interpretation/conversations/${interpretationConversationId}/messages`)
          .set('Authorization', `Bearer ${authToken}`)
          .query({ page: 1, pageSize: 50 });

        expect(response.status).toBe(200);
        expect(response.body).toHaveProperty('code', 200);
        expect(response.body.message).toBe('解读智能体消息列表获取成功');
        expect(response.body).toHaveProperty('data');
        expect(response.body).toHaveProperty('pagination');
        expect(response.body).toHaveProperty('timestamp');
      }
    });
  });

  describe('Pagination tests', () => {
    it('should handle pagination parameters correctly', async () => {
      if (conversationId) {
        const response = await request(app.getHttpServer())
          .get(`/coze/conversations/${conversationId}/messages`)
          .set('Authorization', `Bearer ${authToken}`)
          .query({ page: 1, pageSize: 10 });

        expect(response.status).toBe(200);
        expect(response.body.pagination.page).toBe(1);
        expect(response.body.pagination.pageSize).toBe(10);
        expect(typeof response.body.pagination.total).toBe('number');
        expect(typeof response.body.pagination.totalPages).toBe('number');
        expect(typeof response.body.pagination.hasNext).toBe('boolean');
        expect(typeof response.body.pagination.hasPrev).toBe('boolean');
      }
    });

    it('should use default pagination when not specified', async () => {
      if (conversationId) {
        const response = await request(app.getHttpServer())
          .get(`/coze/conversations/${conversationId}/messages`)
          .set('Authorization', `Bearer ${authToken}`);

        expect(response.status).toBe(200);
        expect(response.body.pagination.page).toBe(1);
        expect(response.body.pagination.pageSize).toBe(50);
      }
    });
  });
});
