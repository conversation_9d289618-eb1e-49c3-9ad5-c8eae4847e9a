{"name": "kanliAI", "version": "0.0.1", "description": "", "author": "", "private": true, "license": "UNLICENSED", "scripts": {"prebuild": "<PERSON><PERSON><PERSON> dist", "build": "nest build", "build:prod": "cross-env RUNNING_ENV=prod nest build", "format": "prettier --write \"src/**/*.ts\" \"test/**/*.ts\"", "start": "cross-env RUNNING_ENV=dev nest start", "start:dev": "cross-env RUNNING_ENV=dev nest start --watch", "start:debug": "nest start --debug --watch", "start:prod": "cross-env RUNNING_ENV=prod node dist/main", "lint": "eslint \"{src,apps,libs,test}/**/*.ts\" --fix", "test": "jest", "test:watch": "jest --watch", "test:cov": "jest --coverage", "test:debug": "node --inspect-brk -r tsconfig-paths/register -r ts-node/register node_modules/.bin/jest --runInBand", "test:e2e": "jest --config ./test/jest-e2e.json", "commit": "git add . && npx cz", "release": "standard-version", "release:minor": "standard-version --release-as minor", "release:major": "standard-version --release-as major", "release:patch": "standard-version --release-as patch", "commitlint": "commitlint --edit", "prepare": "husky install", "setup:git": "node setup-git.js"}, "dependencies": {"@commitlint/cli": "^19.8.1", "@commitlint/config-conventional": "^19.8.1", "@coze/api": "latest", "@nestjs-modules/ioredis": "^2.0.2", "@nestjs/common": "^11.1.3", "@nestjs/config": "^4.0.2", "@nestjs/core": "^11.0.1", "@nestjs/jwt": "^11.0.0", "@nestjs/mapped-types": "*", "@nestjs/passport": "^11.0.5", "@nestjs/platform-express": "^11.1.5", "@nestjs/swagger": "^11.2.0", "@nestjs/typeorm": "^11.0.0", "@types/passport-jwt": "^4.0.1", "axios": "^1.7.7", "bcrypt": "^6.0.0", "class-transformer": "^0.5.1", "class-validator": "^0.14.2", "commitizen": "^4.3.1", "cos-nodejs-sdk-v5": "^2.16.0-beta.3", "cross-env": "^7.0.3", "cz-conventional-changelog": "^3.3.0", "husky": "^9.1.7", "ioredis": "^5.6.1", "jsonwebtoken": "^9.0.2", "multer": "^2.0.2", "mysql": "^2.18.1", "nest-wechatpay-node-v3": "^1.0.2", "passport": "^0.7.0", "passport-jwt": "^4.0.1", "pg": "^8.16.3", "redis": "^5.5.6", "reflect-metadata": "^0.2.2", "rxjs": "^7.8.1", "standard-version": "^9.5.0", "swagger-ui-express": "^5.0.1", "tencentcloud-sdk-nodejs": "^4.1.77", "typeorm": "^0.3.25", "uuid": "^11.1.0", "wechatpay-node-v3": "2.1.8", "wx-server-sdk": "^3.0.1"}, "devDependencies": {"@eslint/eslintrc": "^3.2.0", "@eslint/js": "^9.18.0", "@nestjs/cli": "^11.0.0", "@nestjs/schematics": "^11.0.0", "@nestjs/testing": "^11.0.1", "@swc/cli": "^0.6.0", "@swc/core": "^1.10.7", "@types/express": "^5.0.0", "@types/jest": "^29.5.14", "@types/multer": "^2.0.0", "@types/node": "^22.10.7", "@types/supertest": "^6.0.2", "eslint": "^9.18.0", "eslint-config-prettier": "^10.0.1", "eslint-plugin-prettier": "^5.2.2", "fork-ts-checker-webpack-plugin": "^9.1.0", "globals": "^16.0.0", "jest": "^29.7.0", "prettier": "^3.4.2", "source-map-support": "^0.5.21", "supertest": "^7.0.0", "ts-jest": "^29.2.5", "ts-loader": "^9.5.2", "ts-node": "^10.9.2", "tsconfig-paths": "^4.2.0", "typescript": "^5.7.3", "typescript-eslint": "^8.20.0"}, "jest": {"moduleFileExtensions": ["js", "json", "ts"], "rootDir": "src", "testRegex": ".*\\.spec\\.ts$", "transform": {"^.+\\.(t|j)s$": "ts-jest"}, "collectCoverageFrom": ["**/*.(t|j)s"], "coverageDirectory": "../coverage", "testEnvironment": "node"}, "config": {"commitizen": {"path": "cz-conventional-changelog"}}}