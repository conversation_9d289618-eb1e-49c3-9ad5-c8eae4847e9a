import {
  Entity,
  PrimaryGeneratedColumn,
  Column,
  ManyToOne,
  CreateDateColumn,
  UpdateDateColumn,
  JoinColumn,
  Index,
} from "typeorm";
import { ApiProperty } from "@nestjs/swagger";
import { User } from "../../users/entities/user.entity";

export enum InviteStatus {
  PENDING = "pending",
  ACCEPTED = "accepted",
  EXPIRED = "expired",
  CANCELLED = "cancelled",
}

export enum InviteType {
  DIRECT = "direct",      // 直接输入邀请码
  LINK = "link",          // 通过邀请链接
}
 
@Entity('team_invite') 
@Index(['inviterId', 'inviteeId'])
export class TeamInvite {
  @PrimaryGeneratedColumn()
  @ApiProperty({ description: '邀请记录唯一标识' })
  id: number;

  @Column({ unique: true, length: 32 })
  @ApiProperty({ description: '邀请码（32位随机字符串）' })
  inviteCode: string;

  @Column()
  @ApiProperty({ description: '邀请人ID' })
  inviterId: number;

  @Column({ nullable: true })
  @ApiProperty({ description: '被邀请人ID（接受邀请后填入）', required: false })
  inviteeId: number;

  @Column({ type: 'enum', enum: InviteStatus, default: InviteStatus.PENDING })
  @ApiProperty({ enum: InviteStatus, description: '邀请状态', default: InviteStatus.PENDING })
  status: InviteStatus;

  @Column({ type: 'enum', enum: InviteType, default: InviteType.DIRECT })
  @ApiProperty({ enum: InviteType, description: '邀请类型', default: InviteType.DIRECT })
  type: InviteType;

  @Column({ nullable: true })
  @ApiProperty({ description: '邀请链接（如果是链接邀请）', required: false })
  inviteLink: string;

  @Column({ nullable: true })
  @ApiProperty({ description: '邀请过期时间（null表示永不过期）', required: false })
  expiresAt: Date;

  @Column({ nullable: true })
  @ApiProperty({ description: '邀请接受时间', required: false })
  acceptedAt: Date;

  @Column({ nullable: true, type: 'text' })
  @ApiProperty({ description: '邀请备注', required: false })
  note: string;

  @Column({ nullable: true, type: 'json' })
  @ApiProperty({ description: '邀请扩展数据', required: false })
  metadata: Record<string, any>;

  @CreateDateColumn()
  @ApiProperty({ description: '创建时间' })
  createdAt: Date;

  @UpdateDateColumn()
  @ApiProperty({ description: '更新时间' })
  updatedAt: Date;

  // 关联关系
  @ManyToOne(() => User, user => user.sentInvites)
  @JoinColumn({ name: 'inviterId' })
  inviter: User;

  @ManyToOne(() => User, user => user.receivedInvites, { nullable: true })
  @JoinColumn({ name: 'inviteeId' })
  invitee: User;
} 