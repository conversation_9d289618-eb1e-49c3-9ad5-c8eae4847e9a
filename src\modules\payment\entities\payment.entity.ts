import {
  Entity,
  PrimaryGeneratedColumn,
  Column,
  ManyToOne,
  CreateDateColumn,
  UpdateDateColumn,
  JoinColumn,
  Index,
} from "typeorm";
import { ApiProperty } from "@nestjs/swagger";
import { User } from "../../users/entities/user.entity";
import { PaymentType, PaymentMethod } from "../dto/create-payment.dto";

export enum PaymentStatus {
  PENDING = "pending",    // 待支付
  PROCESSING = "processing",  // 支付中
  SUCCESS = "success",    // 支付成功
  FAILED = "failed",      // 支付失败
  CANCELLED = "cancelled", // 已取消
  REFUNDED = "refunded",  // 已退款
}

@Entity('payment')
@Index(['userId', 'status'])
@Index(['orderNo'])
@Index(['thirdPartyTransactionId'])
@Index(['status', 'createdAt'])
@Index(['type', 'method'])
export class Payment {
  @PrimaryGeneratedColumn()
  @ApiProperty({ description: '支付记录ID' })
  id: number;

  @Column({ unique: true, length: 64 })
  @ApiProperty({ description: '订单号' })
  orderNo: string;

  @Column()
  @ApiProperty({ description: '用户ID' })
  userId: number;

  @Column({ type: 'enum', enum: PaymentType })
  @ApiProperty({ enum: PaymentType, description: '支付类型' })
  type: PaymentType;

  @Column({ type: 'enum', enum: PaymentMethod })
  @ApiProperty({ enum: PaymentMethod, description: '支付方式' })
  method: PaymentMethod;

  @Column({ type: 'int' })
  @ApiProperty({ description: '支付金额（分）' })
  amount: number;

  @Column({ type: 'int', default: 1 })
  @ApiProperty({ description: '购买数量', default: 1 })
  quantity: number;

  @Column({ type: 'enum', enum: PaymentStatus, default: PaymentStatus.PENDING })
  @ApiProperty({ enum: PaymentStatus, description: '支付状态', default: PaymentStatus.PENDING })
  status: PaymentStatus;

  @Column({ nullable: true })
  @ApiProperty({ description: '支付描述', required: false })
  description: string;

  @Column({ nullable: true })
  @ApiProperty({ description: '第三方支付平台交易号', required: false })
  thirdPartyTransactionId: string;

  @Column({ nullable: true })
  @ApiProperty({ description: '支付完成时间', required: false })
  paidAt: Date;

  @Column({ nullable: true })
  @ApiProperty({ description: '退款时间', required: false })
  refundedAt: Date;

  @Column({ nullable: true, type: 'int' })
  @ApiProperty({ description: '退款金额（分）', required: false })
  refundAmount: number;

  @Column({ nullable: true, type: 'text' })
  @ApiProperty({ description: '失败原因', required: false })
  failureReason: string;

  @Column({ nullable: true, type: 'json' })
  @ApiProperty({ description: '第三方支付响应数据', required: false })
  thirdPartyResponse: Record<string, any>;

  @Column({ nullable: true, type: 'json' })
  @ApiProperty({ description: '支付扩展信息', required: false })
  metadata: Record<string, any>;

  @Column({ nullable: true })
  @ApiProperty({ description: '客户端IP地址', required: false })
  clientIp: string;

  @Column({ nullable: true })
  @ApiProperty({ description: '支付回调地址', required: false })
  notifyUrl: string;

  @Column({ nullable: true })
  @ApiProperty({ description: '支付成功后跳转地址', required: false })
  returnUrl: string;

  @Column({ nullable: true })
  @ApiProperty({ description: '支付过期时间', required: false })
  expiresAt: Date;

  @Column({ nullable: true })
  @ApiProperty({ description: '支付渠道（具体的支付渠道标识）', required: false })
  paymentChannel: string;

  @Column({ nullable: true, type: 'text' })
  @ApiProperty({ description: '支付备注', required: false })
  remark: string;

  @Column({ default: 0 })
  @ApiProperty({ description: '重试次数', default: 0 })
  retryCount: number;

  @Column({ nullable: true })
  @ApiProperty({ description: '最后重试时间', required: false })
  lastRetryAt: Date;

  @CreateDateColumn()
  @ApiProperty({ description: '创建时间' })
  createdAt: Date;

  @UpdateDateColumn()
  @ApiProperty({ description: '更新时间' })
  updatedAt: Date;

  // 关联关系
  @ManyToOne(() => User, user => user.payments)
  @JoinColumn({ name: 'userId' })
  user: User;
} 