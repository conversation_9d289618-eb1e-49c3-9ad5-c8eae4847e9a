import { ApiProperty } from '@nestjs/swagger';
import { IsArray, IsNumber, IsOptional, ArrayMinSize, ArrayMaxSize, IsString } from 'class-validator';

export class CalculateResultDto {
  @ApiProperty({ 
    description: '用户答案数组，索引对应题目ID，值为选项索引（0-3）',
    example: [0, 1, 2, 3, 0, 1, 2, 3, 0, 1, 2, 3, 0, 1, 2, 3, 0, 1, 2, 3, 0, 1, 2, 3, 0, 1, 2, 3, 0, 1, 2, 3],
    type: [Number]
  })
  @IsArray()
  @ArrayMinSize(32)
  @ArrayMaxSize(32)
  @IsNumber({}, { each: true })
  answers: number[];

  @ApiProperty({ 
    description: '用户ID，如果提供则会保存到用户历史记录',
    example: 1,
    required: false
  })
  @IsOptional()
  @IsNumber()
  userId?: number;

  @ApiProperty({ 
    description: '备注信息',
    example: '第一次测试',
    required: false
  })
  @IsOptional()
  @IsString()
  remark?: string;
}

export class MbtiResultDto {
  @ApiProperty({ description: '外向-内向维度', example: { E: 5, I: 3 } })
  E: number;
  @ApiProperty({ description: '内向-外向维度', example: { E: 5, I: 3 } })
  I: number;
  @ApiProperty({ description: '感觉-直觉维度', example: { S: 4, N: 4 } })
  S: number;
  @ApiProperty({ description: '直觉-感觉维度', example: { S: 4, N: 4 } })
  N: number;
  @ApiProperty({ description: '思考-情感维度', example: { T: 6, F: 2 } })
  T: number;
  @ApiProperty({ description: '情感-思考维度', example: { T: 6, F: 2 } })
  F: number;
  @ApiProperty({ description: '判断-知觉维度', example: { J: 3, P: 5 } })
  J: number;
  @ApiProperty({ description: '知觉-判断维度', example: { J: 3, P: 5 } })
  P: number;
}

export class DiscResultDto {
  @ApiProperty({ description: '支配型', example: 8 })
  D: number;
  @ApiProperty({ description: '影响型', example: 6 })
  I: number;
  @ApiProperty({ description: '稳健型', example: 10 })
  S: number;
  @ApiProperty({ description: '谨慎型', example: 8 })
  C: number;
}

export class CalculateResultResponseDto {
  @ApiProperty({ description: 'MBTI各维度得分', type: MbtiResultDto })
  mbti: MbtiResultDto;
  
  @ApiProperty({ description: 'DISC各维度得分', type: DiscResultDto })
  disc: DiscResultDto;
  
  @ApiProperty({ description: 'MBTI类型', example: 'ENFP' })
  mbtiType: string;
  
  @ApiProperty({ description: 'DISC主导风格', example: 'S' })
  discType: string;
  
  @ApiProperty({ description: '最终组合类型', example: 'ENFP-S' })
  finalType: string;
  
  @ApiProperty({ description: '各维度详细得分' })
  dimensionScores: {
    [key: string]: number;
  };
} 

// 新增：包含完整人格测试结果的响应DTO
export class CalculateResultWithPersonalityResponseDto {
  @ApiProperty({ description: 'MBTI各维度得分', type: MbtiResultDto })
  mbti: MbtiResultDto;
  
  @ApiProperty({ description: 'DISC各维度得分', type: DiscResultDto })
  disc: DiscResultDto;
  
  @ApiProperty({ description: 'MBTI类型', example: 'ISTJ' })
  mbtiType: string;
  
  @ApiProperty({ description: 'DISC主导风格', example: 'D' })
  discType: string;
  
  @ApiProperty({ description: '最终组合类型', example: 'ISTJ-D' })
  finalType: string;
  
  @ApiProperty({ description: '各维度详细得分' })
  dimensionScores: {
    [key: string]: number;
  };
  
  @ApiProperty({ 
    description: '人格测试结果详情',
    example: {
      typeCode: 'ISTJ-D',
      title: '规则凝聚者',
      coreTraits: '以身作则推进制度，强硬把控细节中凝聚团队忠诚',
      strengths: '极高目标达成率与团队向心力并重',
      sceneMatch: '危机管理/制度落地/军官/领导',
      blindSpots: '压制异议倾向，易陷入任人唯亲，追求虚名',
      suggestions: '主动倾听反馈，建立匿名吐槽机制',
      symbol: '师',
      lifePath: '怎样从带团队到做一名游刃有余的一把手',
      valueGuide: '⚠️ 团队因压制异议分崩离析？制度落地沦为虚名？<br>💎 以身作则锻造铁军，建立匿名吐槽机制 ，制定《忠诚团队构建蓝图》'
    }
  })
  personalityResult: {
    typeCode: string;
    title: string;
    coreTraits: string;
    strengths: string;
    sceneMatch: string;
    blindSpots: string;
    suggestions: string;
    symbol: string;
    lifePath: string;
    valueGuide: string;
  };

  @ApiProperty({ 
    description: '是否已保存到用户历史记录',
    example: true
  })
  savedToHistory: boolean;
} 