import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository, FindOptionsWhere } from 'typeorm';
import { Conversation, ConversationStatus, BotType } from '../modules/coze/entities/conversation.entity';
import { CreateConversationDto, UpdateConversationDto } from '../modules/coze/dto/conversation.dto';
import { MessageRepository } from './message.repository';

@Injectable()
export class ConversationRepository {
    constructor(
        @InjectRepository(Conversation)
        private readonly conversationRepository: Repository<Conversation>,
        private readonly messageRepository: MessageRepository,
    ) { }

    async create(userId: number, createDto: CreateConversationDto, cozeConversationId: string, botId: string, botType: BotType = BotType.DEFAULT): Promise<Conversation> {
        const conversation = this.conversationRepository.create({
            title: createDto.title,
            userId,
            cozeConversationId,
            botId,
            botType,
            region: createDto.region || 'zh',
            metadata: createDto.metadata,
            lastActiveAt: new Date(),
        });

        return await this.conversationRepository.save(conversation);
    }

    async findById(id: string): Promise<Conversation | null> {
        return await this.conversationRepository.findOne({
            where: { id },
            relations: ['user', 'messages'],
        });
    }

    async findByUserId(userId: number, status?: ConversationStatus): Promise<Conversation[]> {
        const where: FindOptionsWhere<Conversation> = { userId };
        if (status) {
            where.status = status;
        }

        return await this.conversationRepository.find({
            where,
            relations: ['messages'],
            order: { lastActiveAt: 'DESC' },
        });
    }

    async findByCozeConversationId(cozeConversationId: string): Promise<Conversation | null> {
        return await this.conversationRepository.findOne({
            where: { cozeConversationId },
            relations: ['user', 'messages'],
        });
    }

    async update(id: string, updateDto: UpdateConversationDto): Promise<Conversation | null> {
        await this.conversationRepository.update(id, updateDto);
        return await this.findById(id);
    }

    async updateLastActiveTime(id: string): Promise<void> {
        await this.conversationRepository.update(id, { lastActiveAt: new Date() });
    }

    async incrementMessageCount(id: string): Promise<void> {
        await this.conversationRepository.increment({ id }, 'messageCount', 1);
    }

    async delete(id: string): Promise<boolean> {
        const result = await this.conversationRepository.update(id, { status: ConversationStatus.DELETED });
        return (result.affected ?? 0) > 0;
    }

    async getUserConversationsPaginated(
        userId: number,
        page: number = 1,
        pageSize: number = 20,
        botType?: BotType,
    ): Promise<{ conversations: Conversation[]; total: number }> {
        const offset = (page - 1) * pageSize;

        const queryBuilder = this.conversationRepository
            .createQueryBuilder('conversation')
            .where('conversation.userId = :userId', { userId })
            .andWhere('conversation.status = :status', { status: ConversationStatus.ACTIVE });

        if (botType) {
            queryBuilder.andWhere('conversation.botType = :botType', { botType });
        }

        // 先获取总数
        const total = await queryBuilder.getCount();

        const conversations = await queryBuilder
            .orderBy('conversation.lastActiveAt', 'DESC')
            .skip(offset)
            .take(pageSize)
            .getMany();

        for (const conversation of conversations) {
            const messages = await this.messageRepository.findByConversationId(conversation.id);
            conversation.messages = messages;
        }

        return { conversations, total };
    }
}