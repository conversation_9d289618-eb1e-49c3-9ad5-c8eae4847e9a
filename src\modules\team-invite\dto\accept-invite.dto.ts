import { IsString, IsNotEmpty, Length, Matches } from 'class-validator';
import { ApiProperty } from '@nestjs/swagger';

export class AcceptInviteDto {
  @ApiProperty({ 
    description: '邀请码', 
    example: 'ABC12345',
    minLength: 8,
    maxLength: 32
  })
  @IsString({ message: '邀请码必须是字符串' })
  @IsNotEmpty({ message: '邀请码不能为空' })
  @Length(8, 32, { message: '邀请码长度必须在8-32个字符之间' })
  @Matches(/^[A-Z0-9]+$/, { message: '邀请码只能包含大写字母和数字' })
  inviteCode: string;
} 