import { Injectable } from "@nestjs/common";
import { InjectRepository } from "@nestjs/typeorm";
import { Repository } from "typeorm";
import { Captcha } from "../modules/captcha/entities/captcha.entity";

@Injectable()
export class CaptchaRepository {
  constructor(
    @InjectRepository(Captcha)
    private captchaRepository: Repository<Captcha>
  ) {}

  async findById(id: number): Promise<Captcha | null> {
    return await this.captchaRepository.query(
      `SELECT * FROM captchas WHERE id = ?`,
      [id]
    );
  }

  async findByPhoneNumber(phoneNumber: string): Promise<Captcha | null> {
    return await this.captchaRepository.findOne({
      where: { phoneNumber },
    });
  }

  async findLatestUnusedCaptcha(
    phoneNumber: string,
    code: string
  ): Promise<Captcha | null> {
    return await this.captchaRepository.findOne({
      where: {
        phoneNumber,
        code,
        isUsed: false,
      },
      order: { createdAt: "DESC" },
    });
  }

  async save(captcha: Captcha): Promise<Captcha> {
    return await this.captchaRepository.save(captcha);
  }

  async findAll(): Promise<Captcha[]> {
    return await this.captchaRepository.query(`
      SELECT * FROM captchas
    `);
  }

  async create(captcha: Partial<Captcha>): Promise<Captcha> {
    return await this.captchaRepository.save(captcha);
  }

  async update(id: number, captcha: Partial<Captcha>): Promise<void> {
    await this.captchaRepository.update(id, captcha);
  }

  async delete(id: number): Promise<void> {
    await this.captchaRepository.delete(id);
  }
}
