import { Entity, PrimaryGeneratedColumn, Column, CreateDateColumn, UpdateDateColumn } from 'typeorm';

@Entity('date_queries')
export class DateQuery {
    @PrimaryGeneratedColumn()
    id: number;

    @Column({ type: 'int', comment: '月份 (1-12)' })
    month: number;

    @Column({ type: 'int', comment: '日期 (1-31)' })
    day: number;

    @Column({ type: 'int', comment: '对应的数字值' })
    value: number;

    @Column({ type: 'varchar', length: 100, nullable: true, comment: '特殊规则说明' })
    specialRule?: string;

    @Column({ type: 'boolean', default: false, comment: '是否为闰年规则' })
    isLeapYear: boolean;

    @Column({ type: 'varchar', length: 50, default: 'active', comment: '状态' })
    status: string;

    @CreateDateColumn({ comment: '创建时间' })
    createdAt: Date;

    @UpdateDateColumn({ comment: '更新时间' })
    updatedAt: Date;
} 