import { Injectable } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import * as uuid from 'uuid';
import COS from 'cos-nodejs-sdk-v5';
import * as path from 'path';
import { CreateUploadDto } from './dto/create-upload.dto';
import { UpdateUploadDto } from './dto/update-upload.dto';

@Injectable()
export class UploadService {
  private cos: COS;

  constructor(private configService: ConfigService) {
    const secretId = this.configService.get('COS_SECRET_ID');
    const secretKey = this.configService.get('COS_SECRET_KEY');

    // 初始化 COS 实例
    this.cos = new COS({
      SecretId: secretId,
      SecretKey: secretKey,
    });
  }

  // 上传文件到腾讯云 COS
  async uploadFile(file: Express.Multer.File): Promise<any> {
    const bucket = this.configService.get('COS_BUCKET');
    const region = this.configService.get('COS_REGION');

    // 使用 uuid 生成唯一的文件名，并保持原文件扩展名
    const fileName = Date.now() + uuid.v4() + path.extname(file.originalname);

    // COS 上传参数
    const params = {
      Bucket: bucket,
      Region: region,
      Key: `uploads/${fileName}`,
      Body: file.buffer,
    };

    // 上传到 COS
    return new Promise((resolve, reject) => {
      this.cos.putObject(params, (err, data) => {
        if (err) {
          return reject({ message: 'File upload failed', error: err });
        }
        // 返回上传后的文件信息
        const fileInfo = {
          fileName: fileName,
          url: `https://${bucket}.cos.${region}.myqcloud.com/uploads/${fileName}`,
          size: file.size,
          originalName: file.originalname,
          mimeType: file.mimetype,
        };
        return resolve(fileInfo);
      });
    });
  }

  // 创建上传记录（可选，保存文件信息到数据库）
  async create(createUploadDto: CreateUploadDto, file: Express.Multer.File) {
    // 可以把文件信息保存到数据库，比如：
    const uploadData = {
      fileName: file.filename,
      originalName: file.originalname,
      fileSize: file.size,
      fileType: file.mimetype,
      fileUrl: `https://${this.configService.get('COS_BUCKET')}.cos.${this.configService.get('COS_REGION')}.myqcloud.com/uploads/${file.filename}`,
    };

    // 这里可以调用数据库服务保存上传的记录
    // 例如：return this.uploadRepository.create(uploadData);

    return { message: 'File uploaded successfully', data: uploadData };
  }

  // 查找所有上传记录
  findAll() {
    return `This action returns all uploads`;
  }

  // 查找单个上传记录
  findOne(id: number) {
    return `This action returns a #${id} upload`;
  }

  // 更新上传记录
  update(id: number, updateUploadDto: UpdateUploadDto) {
    return `This action updates a #${id} upload`;
  }
}
