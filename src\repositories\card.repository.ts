import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { Card } from '../modules/cards/entities/card.entity';

@Injectable()
export class CardRepository {
  constructor(
    @InjectRepository(Card)
    private cardRepository: Repository<Card>
  ) {}

  async findById(id: number): Promise<Card | null> {
    return await this.cardRepository.query(
      `SELECT * FROM cards WHERE id = ?`,
      [id]
    );
  }

  async findAll(): Promise<Card[]> {
    return await this.cardRepository.query(`
      SELECT * FROM cards
    `);
  }

  async create(card: Partial<Card>): Promise<Card> {
    return await this.cardRepository.save(card);
  }

  async update(id: number, card: Partial<Card>): Promise<void> {
    await this.cardRepository.update(id, card);
  }

  async delete(id: number): Promise<void> {
    await this.cardRepository.delete(id);
  }
} 