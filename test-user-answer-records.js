const axios = require("axios");

const BASE_URL = "http://localhost:4000";

async function testUserAnswerRecordsHistory() {
  try {
    console.log("测试获取用户答题记录历史（分页）...");

    // 测试不同的分页参数
    const testCases = [
      { userId: 3, page: 1, pageSize: 10 },
      { userId: 3, page: 1, pageSize: 5 },
      { userId: 3, page: 2, pageSize: 10 },
    ];

    for (const testCase of testCases) {
      console.log(`\n测试参数:`, testCase);

      const response = await axios.get(
        `${BASE_URL}/questions/user-answer-records/history`,
        {
          params: testCase,
        }
      );

      console.log("响应状态:", response.status);
      console.log("响应数据:", JSON.stringify(response.data, null, 2));
    }
  } catch (error) {
    console.error("测试失败:", error.response?.data || error.message);
  }
}

// 运行测试
testUserAnswerRecordsHistory();
