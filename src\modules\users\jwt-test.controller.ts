import {
    Controller,
    Get,
    Post,
    Body,
    Request,
    UseGuards,
    Headers,
    UnauthorizedException,
    BadRequestException,
    Logger,
} from "@nestjs/common";
import { JwtService } from "@nestjs/jwt";
import { ConfigService } from "@nestjs/config";
import { ApiTags, ApiOperation, ApiResponse, ApiHeader, ApiBearerAuth } from "@nestjs/swagger";
import { AuthGuard } from "../../guards/auth.guard";
import { UsersService } from "./users.service";
import { RedisService } from "../redis/redis.service";

@Controller("jwt-test")
@ApiTags("JWT测试")
export class JwtTestController {
    constructor(
        private jwtService: JwtService,
        private configService: ConfigService,
        private usersService: UsersService,
        private redisService: RedisService
    ) { }

    @Post("verify-token")
    @UseGuards(AuthGuard)
    @ApiBearerAuth()
    @ApiOperation({ summary: "验证JWT token是否有效" })
    @ApiResponse({
        status: 200,
        description: "Token验证成功",
        schema: {
            example: {
                code: 200,
                message: "请求成功",
                data: {
                    valid: true,
                    payload: {
                        sub: "1",
                        name: "张三",
                        email: "<EMAIL>",
                        role: "user",
                        iat: 1642248000,
                        exp: 1642251600
                    },
                    issuedAt: "2024-01-15T10:00:00.000Z",
                    expiresAt: "2024-01-15T11:00:00.000Z",
                    timeToExpire: "45分钟23秒"
                }
            }
        }
    })
    @ApiResponse({ status: 401, description: "Token无效或已过期" })
    async verifyToken(@Request() req: any) {
        const user = req.user;
        const token = req.token;

        const issuedAt = new Date(user.iat * 1000);
        const expiresAt = new Date(user.exp * 1000);
        const now = new Date();
        const timeToExpire = expiresAt.getTime() - now.getTime();

        const formatTimeToExpire = (ms: number) => {
            const minutes = Math.floor(ms / (1000 * 60));
            const seconds = Math.floor((ms % (1000 * 60)) / 1000);
            return `${minutes}分钟${seconds}秒`;
        };

        return {
            valid: true,
            payload: {
                sub: user.sub,
                name: user.name,
                email: user.email,
                role: user.role,
                iat: user.iat,
                exp: user.exp
            },
            issuedAt: issuedAt.toISOString(),
            expiresAt: expiresAt.toISOString(),
            timeToExpire: timeToExpire > 0 ? formatTimeToExpire(timeToExpire) : "已过期"
        };
    }

    @Get("test-auth")
    @UseGuards(AuthGuard)
    @ApiBearerAuth()
    @ApiOperation({ summary: "测试JWT认证守卫 (需要登录)" })
    @ApiResponse({
        status: 200,
        description: "认证成功",
        schema: {
            example: {
                code: 200,
                message: "请求成功",
                data: {
                    authenticated: true,
                    user: {
                        sub: "1",
                        name: "张三",
                        email: "<EMAIL>",
                        role: "user",
                        iat: 1642248000,
                        exp: 1642251600
                    },
                    message: "JWT认证守卫测试通过"
                }
            }
        }
    })
    @ApiResponse({ status: 401, description: "未授权，token无效或已过期" })
    async testAuth(@Request() req: any) {
        return {
            authenticated: true,
            user: req.user,
            message: "JWT认证守卫测试通过"
        };
    }

    @Post("refresh-token")
    @UseGuards(AuthGuard)
    @ApiBearerAuth()
    @ApiOperation({ summary: "刷新JWT token (需要登录)" })
    @ApiResponse({
        status: 200,
        description: "Token刷新成功",
        schema: {
            example: {
                code: 200,
                message: "请求成功",
                data: {
                    oldToken: "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
                    newToken: "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
                    expiresAt: "2024-01-15T12:00:00.000Z",
                    message: "Token刷新成功"
                }
            }
        }
    })
    @ApiResponse({ status: 401, description: "未授权" })
    async refreshToken(@Request() req: any) {
        const oldToken = req.token;
        const user = req.user;

        // 生成新的token
        const payload = {
            sub: user.sub,
            name: user.name,
            email: user.email,
            role: user.role
        };

        const newToken = this.jwtService.sign(payload);

        // 将旧token加入黑名单
        await this.blacklistToken(oldToken);

        // 计算新token的过期时间
        const decoded = this.jwtService.decode(newToken) as any;
        const expiresAt = new Date(decoded.exp * 1000);

        return {
            oldToken,
            newToken,
            expiresAt: expiresAt.toISOString(),
            message: "Token刷新成功"
        };
    }

    @Get("token-info")
    @UseGuards(AuthGuard)
    @ApiBearerAuth()
    @ApiOperation({ summary: "获取当前token详细信息 (需要登录)" })
    @ApiResponse({
        status: 200,
        description: "获取成功",
        schema: {
            example: {
                code: 200,
                message: "请求成功",
                data: {
                    token: "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
                    payload: {
                        sub: "1",
                        name: "张三",
                        email: "<EMAIL>",
                        role: "user",
                        iat: 1642248000,
                        exp: 1642251600
                    },
                    header: {
                        alg: "HS256",
                        typ: "JWT"
                    },
                    issuedAt: "2024-01-15T10:00:00.000Z",
                    expiresAt: "2024-01-15T11:00:00.000Z",
                    timeToExpire: "45分钟23秒",
                    isBlacklisted: false
                }
            }
        }
    })
    @ApiResponse({ status: 401, description: "未授权" })
    async getTokenInfo(@Request() req: any) {
        const token = req.token;
        const user = req.user;

        // 解码token获取header信息
        const decoded = this.jwtService.decode(token, { complete: true }) as any;

        const issuedAt = new Date(user.iat * 1000);
        const expiresAt = new Date(user.exp * 1000);
        const now = new Date();
        const timeToExpire = expiresAt.getTime() - now.getTime();

        const formatTimeToExpire = (ms: number) => {
            const minutes = Math.floor(ms / (1000 * 60));
            const seconds = Math.floor((ms % (1000 * 60)) / 1000);
            return `${minutes}分钟${seconds}秒`;
        };

        // 检查token是否在黑名单中
        const isBlacklisted = await this.usersService.isTokenBlacklisted(token);

        return {
            token,
            payload: user,
            header: decoded.header,
            issuedAt: issuedAt.toISOString(),
            expiresAt: expiresAt.toISOString(),
            timeToExpire: timeToExpire > 0 ? formatTimeToExpire(timeToExpire) : "已过期",
            isBlacklisted
        };
    }

    @Get("test-permissions")
    @UseGuards(AuthGuard)
    @ApiBearerAuth()
    @ApiOperation({ summary: "测试用户权限信息 (需要登录)" })
    @ApiResponse({
        status: 200,
        description: "权限测试成功",
        schema: {
            example: {
                code: 200,
                message: "请求成功",
                data: {
                    userId: 1,
                    role: "user",
                    permissions: {
                        canAccessProfile: true,
                        canCreateInvite: false,
                        canMakePayment: true,
                        hasTeamPermission: false,
                        hasIndividualPermission: true
                    },
                    userInfo: {
                        id: 1,
                        name: "张三",
                        email: "<EMAIL>",
                        membershipType: "free",
                        permissionLevel: "individual"
                    },
                    message: "权限测试完成"
                }
            }
        }
    })
    @ApiResponse({ status: 401, description: "未授权" })
    async testPermissions(@Request() req: any) {
        const userId = parseInt(req.user.sub, 10);
        const userResult = await this.usersService.findOne(userId);

        return {
            userId,
            role: req.user.role,
            permissions: {
                canAccessProfile: true,
                canCreateInvite: userResult.user.permissionLevel === "team",
                canMakePayment: true,
                hasTeamPermission: userResult.user.permissionLevel === "team",
                hasIndividualPermission: userResult.user.permissionLevel === "individual"
            },
            userInfo: {
                id: userResult.user.id,
                name: userResult.user.name,
                email: userResult.user.email,
                membershipType: userResult.user.membershipType,
                permissionLevel: userResult.user.permissionLevel
            },
            message: "权限测试完成"
        };
    }

    @Post("generate-test-token")
    @ApiOperation({ summary: "生成测试用的JWT token (仅用于开发测试)" })
    @ApiResponse({
        status: 200,
        description: "Token生成成功",
        schema: {
            example: {
                code: 200,
                message: "请求成功",
                data: {
                    token: "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
                    payload: {
                        sub: "999",
                        name: "测试用户",
                        email: "<EMAIL>",
                        role: "user"
                    },
                    expiresAt: "2024-01-15T11:00:00.000Z",
                    message: "测试Token生成成功"
                }
            }
        }
    })
    @ApiResponse({ status: 400, description: "参数错误" })
    async generateTestToken(@Body() body?: { userId?: string, name?: string, email?: string, role?: string }) {
        // 警告：这个接口仅用于开发测试，生产环境应该删除或禁用
        const isDevelopment = this.configService.get("NODE_ENV") !== "production";

        if (!isDevelopment) {
            throw new BadRequestException("此接口仅在开发环境可用");
        }

        const payload = {
            sub: body?.userId || "999",
            name: body?.name || "测试用户",
            email: body?.email || "<EMAIL>",
            role: body?.role || "user"
        };

        const token = this.jwtService.sign(payload);
        const decoded = this.jwtService.decode(token) as any;
        const expiresAt = new Date(decoded.exp * 1000);

        return {
            token,
            payload,
            expiresAt: expiresAt.toISOString(),
            message: "测试Token生成成功"
        };
    }

    /**
     * 将token加入黑名单
     * @param token JWT token
     */
    private async blacklistToken(token: string): Promise<void> {
        try {
            const decoded = this.jwtService.verify(token);
            const currentTime = Math.floor(Date.now() / 1000);
            const remainingTime = decoded.exp - currentTime;

            if (remainingTime > 0) {
                const blacklistKey = `blacklist:${token}`;
                await this.redisService.set(blacklistKey, "1", remainingTime);
            }
        } catch (error) {
            // Token已过期或无效，不需要加入黑名单
        }
    }
} 
