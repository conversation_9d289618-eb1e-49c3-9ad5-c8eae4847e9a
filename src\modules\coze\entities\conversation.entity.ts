import { ApiProperty } from "@nestjs/swagger";
import {
    Entity,
    PrimaryGeneratedColumn,
    Column,
    ManyToOne,
    OneToMany,
    CreateDateColumn,
    UpdateDateColumn,
    JoinColumn,
} from 'typeorm';

import { User } from '../../users/entities/user.entity';
import { Message } from './message.entity'
export enum ConversationStatus {
    ACTIVE = 'active',
    ARCHIVED = 'archived',
    DELETED = 'deleted',
}

export enum BotType {
    DEFAULT = 'default',
    INTERPRETATION = 'interpretation',
    PROTO = 'proto',
}

@Entity()
export class Conversation {
    @PrimaryGeneratedColumn('uuid')
    @ApiProperty({ description: "对话唯一标识" })
    id: string;

    @Column()
    @ApiProperty({ description: '对话标题' })
    title: string;

    @Column({ type: 'enum', enum: ConversationStatus, default: ConversationStatus.ACTIVE })
    @ApiProperty({ enum: ConversationStatus, description: '对话状态' })
    status: ConversationStatus;

    @Column({ nullable: true })
    @ApiProperty({ description: '关联的用户ID' })
    userId: number;

    @Column()
    @ApiProperty({ description: 'Coze对话ID' })
    cozeConversationId: string;

    @Column()
    @ApiProperty({ description: '使用的Bot ID' })
    botId: string;

    @Column({ type: 'enum', enum: BotType, default: BotType.DEFAULT })
    @ApiProperty({ enum: BotType, description: '智能体类型' })
    botType: BotType;

    @Column({ default: 'zh' })
    @ApiProperty({ description: '使用的区域' })
    region: string;

    @Column({ type: 'json', nullable: true })
    @ApiProperty({ description: '对话元数据' })
    metadata: Record<string, any>;

    @Column({ default: 0 })
    @ApiProperty({ description: '消息数量' })
    messageCount: number;

    @CreateDateColumn()
    @ApiProperty({ description: '创建时间' })
    createdAt: Date;

    @UpdateDateColumn()
    @ApiProperty({ description: '更新时间' })
    updatedAt: Date;

    @Column({ nullable: true })
    @ApiProperty({ description: '最后活跃时间' })
    lastActiveAt: Date;

    // 关系映射
    @ManyToOne(() => User, user => user.conversations)
    @JoinColumn({ name: 'userId' })
    user: User;

    @OneToMany(() => Message, message => message.conversation)
    messages: Message[];
}