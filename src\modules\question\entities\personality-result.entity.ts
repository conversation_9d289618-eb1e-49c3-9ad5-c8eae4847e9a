import { Entity, PrimaryGeneratedColumn, Column, CreateDateColumn, UpdateDateColumn } from 'typeorm';

@Entity('personality_results')
export class PersonalityResult {
  @PrimaryGeneratedColumn()
  id: number;

  @Column({ unique: true, comment: '人格类型代码，如ISTJ-D' })
  typeCode: string;

  @Column({ comment: '人格类型标题' })
  title: string;

  @Column({ type: 'text', comment: '核心特质描述' })
  coreTraits: string;

  @Column({ type: 'text', comment: '优势描述' })
  strengths: string;

  @Column({ type: 'text', comment: '适用场景' })
  sceneMatch: string;

  @Column({ type: 'text', comment: '盲点描述' })
  blindSpots: string;

  @Column({ type: 'text', comment: '建议' })
  suggestions: string;

  @Column({ comment: '象征符号' })
  symbol: string;

  @Column({ type: 'text', comment: '人生路径' })
  lifePath: string;

  @Column({ type: 'text', comment: '价值指导' })
  valueGuide: string;

  @CreateDateColumn({ comment: '创建时间' })
  createdAt: Date;

  @UpdateDateColumn({ comment: '更新时间' })
  updatedAt: Date;
} 