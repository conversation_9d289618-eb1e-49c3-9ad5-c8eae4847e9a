import { <PERSON><PERSON><PERSON> } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { TeamInviteController } from './team-invite.controller';
import { TeamInviteService } from './team-invite.service';
import { TeamInvite } from './entities/team-invite.entity';
import { TeamInviteRepository } from '../../repositories/team-invite.repository';
import { UserRepository } from '../../repositories/user.repository';
import { User } from '../users/entities/user.entity';
import { GuardsModule } from '../../guards/guards.module';
import { TeamInviteStatsService } from './team-invite-stats.service';
import { TeamInviteStatsController } from './team-invite-stats.controller';


@Module({
  imports: [
    TypeOrmModule.forFeature([TeamInvite, User]),
    GuardsModule
  ],
  controllers: [TeamInviteController, TeamInviteStatsController],
  providers: [
    TeamInviteService,
    TeamInviteRepository,
    UserRepository,
    TeamInviteStatsService
  ],
  exports: [TeamInviteService, TeamInviteRepository, TeamInviteStatsService]
})
export class TeamInviteModule { } 