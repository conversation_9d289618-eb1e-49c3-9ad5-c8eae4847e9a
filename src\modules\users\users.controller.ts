import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  Headers,
  UnauthorizedException,
  Request,
  UseGuards,
} from "@nestjs/common";
import { UsersService } from "./users.service";
import { CreateUserDto } from "./dto/create-user.dto";
import { LoginUserDto } from "./dto/login-user.dto";
import { UpdateUserDto } from "./dto/update-user.dto";
import { LogoutResponseDto } from "./dto/logout-user.dto";
import { UpdateMembershipDto, MembershipStatusDto } from "./dto/membership.dto";
import { UsageCheckDto, UsageCheckResponseDto } from "./dto/usage-check.dto";
import { UserResponseDto } from "./dto/user-response.dto";
import { WechatLoginDto, WechatLoginResponseDto } from "./dto/wechat-login.dto";
import { DecryptUserInfoDto, DecryptedUserInfoResponseDto } from "./dto/decrypt-user-info.dto";
import { AuthGuard } from "../../guards/auth.guard";
import { PermissionGuard, UsageLimitGuard, CurrentUser, RequireTeamPermission } from "../../guards/permission.guard";
import { User } from "./entities/user.entity";
import { ApiTags, ApiOperation, ApiResponse, ApiHeader, ApiBearerAuth } from "@nestjs/swagger";

@Controller("users")
@ApiTags("用户")
export class UsersController {
  constructor(private readonly usersService: UsersService) { }

  @Post("register")
  @ApiOperation({ summary: "注册用户" })
  @ApiResponse({
    status: 201,
    description: "注册成功",
    schema: {
      example: {
        code: 200,
        message: "请求成功",
        data: {
          user: {
            id: 1,
            name: "张三",
            email: "<EMAIL>",
            phoneNumber: "13800138000",
            role: "user",
            userType: "individual",
            isActive: true,
            membershipType: "free",
            freeAssessmentCount: 10,
            freeAIInterpretationCount: 10,
            createdAt: "2024-01-15T10:30:00.000Z"
          },
          message: "注册成功"
        }
      }
    }
  })
  @ApiResponse({ status: 400, description: "注册失败，参数错误或验证码无效" })
  async register(@Body() createUserDto: CreateUserDto) {
    return await this.usersService.register(createUserDto);
  }

  @Post("login")
  @ApiOperation({ summary: "用户登录" })
  @ApiResponse({
    status: 200,
    description: "登录成功",
    schema: {
      example: {
        code: 200,
        message: "请求成功",
        data: {
          user: {
            id: 1,
            name: "张三",
            email: "<EMAIL>",
            phoneNumber: "13800138000",
            role: "user",
            userType: "individual",
            membershipType: "free"
          },
          token: "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
          message: "登录成功"
        }
      }
    }
  })
  @ApiResponse({ status: 400, description: "登录失败，用户不存在或密码错误" })
  async login(@Body() loginUserDto: LoginUserDto) {
    return await this.usersService.login(loginUserDto);
  }

  @Post("wechat-login")
  @ApiOperation({ summary: "微信小程序一键登录" })
  @ApiResponse({
    status: 200,
    description: "微信登录成功",
    type: WechatLoginResponseDto,
    schema: {
      example: {
        code: 200,
        message: "请求成功",
        data: {
          user: {
            id: 1,
            name: "微信用户_abc123",
            openidWx: "oabc123456789",
            role: "user",
            userType: "individual",
            membershipType: "free",
            permissionLevel: "individual",
            freeAssessmentCount: 10,
            freeAIInterpretationCount: 10,
            isActive: true,
            createdAt: "2024-01-15T10:30:00.000Z"
          },
          token: "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
          message: "登录成功",
          isNewUser: false
        }
      }
    }
  })
  @ApiResponse({ status: 400, description: "微信登录失败，无法获取用户信息或验证失败" })
  async wechatLogin(@Body() wechatLoginDto: WechatLoginDto) {
    return await this.usersService.wechatLogin(wechatLoginDto);
  }

  @Post("decrypt-user-info")
  @ApiOperation({ summary: "解密微信用户信息" })
  @ApiResponse({
    status: 200,
    description: "解密成功",
    type: DecryptedUserInfoResponseDto,
    schema: {
      example: {
        code: 200,
        message: "请求成功",
        data: {
          phoneNumber: "13800138000",
          purePhoneNumber: "13800138000",
          countryCode: "86",
          watermark: {
            timestamp: 1477314187,
            appid: "wx4f4bc4dec97d474b"
          }
        }
      }
    }
  })
  @ApiResponse({ status: 400, description: "解密失败，参数错误或微信API调用失败" })
  async decryptUserInfo(@Body() decryptUserInfoDto: DecryptUserInfoDto) {
    return await this.usersService.decryptUserInfo(decryptUserInfoDto);
  }

  @Post("logout")
  @UseGuards(AuthGuard)
  @ApiBearerAuth()
  @ApiOperation({ summary: "退出登录" })
  @ApiHeader({
    name: "X-JWT-Token",
    description: "JWT Bearer token",
    required: true,
    example: "Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."
  })
  @ApiResponse({
    status: 200,
    description: "退出登录成功",
    type: LogoutResponseDto,
    schema: {
      example: {
        code: 200,
        message: "请求成功",
        data: {
          message: "退出登录成功",
          timestamp: "2024-01-15T10:30:00.000Z"
        }
      }
    }
  })
  @ApiResponse({ status: 401, description: "未授权，token无效或已过期" })
  @ApiResponse({ status: 400, description: "请求头缺少X-JWT-Token" })
  async logout(@Headers("X-JWT-Token") jwtToken: string): Promise<LogoutResponseDto> {
    if (!jwtToken) {
      throw new UnauthorizedException("请求头缺少X-JWT-Token");
    }

    // 提取token（移除"Bearer "前缀）
    const token = jwtToken.replace("Bearer ", "");
    if (!token) {
      throw new UnauthorizedException("Token格式错误");
    }

    return await this.usersService.logout(token);
  }

  @Get("profile")
  @UseGuards(AuthGuard, PermissionGuard)
  @ApiBearerAuth()
  @ApiOperation({ summary: "获取用户个人信息 (需要登录)" })
  @ApiResponse({
    status: 200,
    description: "获取成功",
    type: UserResponseDto,
    schema: {
      example: {
        code: 200,
        message: "请求成功",
        data: {
          user: {
            id: 1,
            name: "张三",
            email: "<EMAIL>",
            phoneNumber: "13800138000",
            role: "user",
            userType: "individual",
            membershipType: "free",
            freeAssessmentCount: 8,
            freeAIInterpretationCount: 5,
            isActive: true,
            createdAt: "2024-01-15T10:30:00.000Z"
          }
        }
      }
    }
  })
  @ApiResponse({ status: 401, description: "未授权，token无效、已过期或已退出" })
  async getProfile(@CurrentUser() user: User) {
    return await this.usersService.findOne(user.id);
  }

  @Post("check-usage")
  @UseGuards(AuthGuard, PermissionGuard)
  @ApiBearerAuth()
  @ApiOperation({ summary: "检查使用次数权限 (需要登录)" })
  @ApiResponse({
    status: 200,
    description: "检查成功",
    type: UsageCheckResponseDto
  })
  @ApiResponse({ status: 401, description: "未授权" })
  async checkUsage(@CurrentUser() user: User, @Body() usageCheckDto: UsageCheckDto) {
    return await this.usersService.checkUsageLimit(user.id, usageCheckDto.type);
  }

  @Post("consume-usage")
  @UseGuards(AuthGuard, UsageLimitGuard)
  @ApiBearerAuth()
  @ApiOperation({ summary: "消费使用次数 (需要登录)" })
  @ApiResponse({
    status: 200,
    description: "消费成功",
    schema: {
      example: {
        code: 200,
        message: "请求成功",
        data: {
          success: true,
          message: "使用成功",
          remainingCount: 9
        }
      }
    }
  })
  @ApiResponse({ status: 400, description: "次数不足或需要付费" })
  @ApiResponse({ status: 401, description: "未授权" })
  async consumeUsage(@CurrentUser() user: User, @Body() usageCheckDto: UsageCheckDto) {
    return await this.usersService.consumeUsage(user.id, usageCheckDto.type);
  }

  @Get("membership")
  @UseGuards(AuthGuard, PermissionGuard)
  @ApiBearerAuth()
  @ApiOperation({ summary: "获取会员状态 (需要登录)" })
  @ApiResponse({
    status: 200,
    description: "获取成功",
    type: MembershipStatusDto
  })
  @ApiResponse({ status: 401, description: "未授权" })
  async getMembershipStatus(@CurrentUser() user: User) {
    return await this.usersService.getMembershipStatus(user.id);
  }

  @Patch("membership/:id")
  @UseGuards(AuthGuard, PermissionGuard)
  @RequireTeamPermission()
  @ApiBearerAuth()
  @ApiOperation({ summary: "更新会员信息 (需要团队权限)" })
  @ApiResponse({
    status: 200,
    description: "更新成功"
  })
  @ApiResponse({ status: 401, description: "未授权" })
  @ApiResponse({ status: 403, description: "权限不足" })
  async updateMembership(
    @Param("id") id: string,
    @Body() updateMembershipDto: UpdateMembershipDto
  ) {
    return await this.usersService.updateMembership(+id, updateMembershipDto);
  }

  @Get()
  @UseGuards(AuthGuard, PermissionGuard)
  @RequireTeamPermission()
  @ApiBearerAuth()
  @ApiOperation({ summary: "获取所有用户 (需要团队权限)" })
  @ApiResponse({
    status: 200,
    description: "获取成功"
  })
  @ApiResponse({ status: 403, description: "权限不足" })
  findAll() {
    return this.usersService.findAll();
  }

  @Get(":id")
  @UseGuards(AuthGuard, PermissionGuard)
  @ApiBearerAuth()
  @ApiOperation({ summary: "根据ID获取用户 (需要登录)" })
  @ApiResponse({
    status: 200,
    description: "获取成功",
    type: UserResponseDto
  })
  @ApiResponse({ status: 400, description: "用户不存在" })
  @ApiResponse({ status: 401, description: "未授权" })
  findOne(@Param("id") id: string) {
    return this.usersService.findOne(+id);
  }

  @Patch(":id")
  @UseGuards(AuthGuard, PermissionGuard)
  @ApiBearerAuth()
  @ApiOperation({ summary: "更新用户信息 (需要登录)" })
  @ApiResponse({
    status: 200,
    description: "更新成功"
  })
  @ApiResponse({ status: 400, description: "用户不存在或出生日期已锁定" })
  @ApiResponse({ status: 401, description: "未授权" })
  update(@Param("id") id: string, @Body() updateUserDto: UpdateUserDto) {
    return this.usersService.update(+id, updateUserDto);
  }

  @Delete(":id")
  @UseGuards(AuthGuard, PermissionGuard)
  @RequireTeamPermission()
  @ApiBearerAuth()
  @ApiOperation({ summary: "删除用户 (需要团队权限)" })
  @ApiResponse({
    status: 200,
    description: "删除成功"
  })
  @ApiResponse({ status: 400, description: "用户不存在" })
  @ApiResponse({ status: 401, description: "未授权" })
  @ApiResponse({ status: 403, description: "权限不足" })
  remove(@Param("id") id: string) {
    return this.usersService.remove(+id);
  }
}
