import { PartialType } from '@nestjs/mapped-types';
import { IsEnum, IsOptional, IsString, IsNumber, IsDateString, IsObject } from 'class-validator';
import { ApiPropertyOptional } from '@nestjs/swagger';
import { CreatePaymentDto } from './create-payment.dto';
import { PaymentStatus } from '../entities/payment.entity';

/**
 * 更新支付记录DTO - 基于CreatePaymentDto的部分类型
 */
export class UpdatePaymentDto extends PartialType(CreatePaymentDto) {
  @ApiPropertyOptional({
    enum: PaymentStatus,
    description: '支付状态',
    example: PaymentStatus.SUCCESS
  })
  @IsOptional()
  @IsEnum(PaymentStatus, { message: '支付状态格式不正确' })
  status?: PaymentStatus;

  @ApiPropertyOptional({
    description: '第三方支付平台交易号',
    example: 'wx_transaction_123456789'
  })
  @IsOptional()
  @IsString()
  thirdPartyTransactionId?: string;

  @ApiPropertyOptional({
    description: '失败原因',
    example: '余额不足'
  })
  @IsOptional()
  @IsString()
  failureReason?: string;

  @ApiPropertyOptional({
    description: '第三方支付响应数据'
  })
  @IsOptional()
  @IsObject()
  thirdPartyResponse?: Record<string, any>;

  @ApiPropertyOptional({
    description: '支付完成时间（ISO 8601格式）',
    example: '2024-01-15T10:30:00Z'
  })
  @IsOptional()
  @IsDateString()
  paidAt?: string;

  @ApiPropertyOptional({
    description: '退款时间（ISO 8601格式）',
    example: '2024-01-16T14:20:00Z'
  })
  @IsOptional()
  @IsDateString()
  refundedAt?: string;

  @ApiPropertyOptional({
    description: '退款金额（分）',
    example: 9900,
    minimum: 0
  })
  @IsOptional()
  @IsNumber({}, { message: '退款金额必须是数字' })
  refundAmount?: number;

  @ApiPropertyOptional({
    description: '支付扩展信息'
  })
  @IsOptional()
  @IsObject()
  metadata?: Record<string, any>;
}
