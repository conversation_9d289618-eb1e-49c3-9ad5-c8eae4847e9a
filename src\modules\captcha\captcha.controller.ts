import { Controller, Post, Body, Get, Query } from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse } from '@nestjs/swagger';
import { CaptchaService } from './captcha.service';
import { SendCaptchaDto } from './dto/send-captcha.dto';

@Controller('captcha')
@ApiTags('验证码')
export class CaptchaController {
  constructor(private readonly captchaService: CaptchaService) { }

  @Post('send')
  @ApiOperation({ summary: '发送验证码' })
  @ApiResponse({
    status: 200,
    description: '验证码发送成功',
    schema: {
      example: {
        code: 200,
        message: '请求成功',
        data: {
          phoneNumber: '13800138000',
          message: '验证码已发送，请注意查收',
          expiresIn: 300
        }
      }
    }
  })
  @ApiResponse({ status: 400, description: '请求参数错误或发送过于频繁' })
  async sendCaptcha(@Body() sendCaptchaDto: SendCaptchaDto) {
    return await this.captchaService.sendCaptcha(sendCaptchaDto.phoneNumber);
  }

  @Get('check-rate-limit')
  @ApiOperation({ summary: '检查发送频率限制' })
  @ApiResponse({
    status: 200,
    description: '查询成功',
    schema: {
      example: {
        code: 200,
        message: '请求成功',
        data: {
          isLimited: true,
          remainingTime: 30
        }
      }
    }
  })
  async checkRateLimit(@Query('phoneNumber') phoneNumber: string) {
    return await this.captchaService.checkRateLimit(phoneNumber);
  }

  @Get('remaining-time')
  @ApiOperation({ summary: '获取验证码剩余有效时间' })
  @ApiResponse({
    status: 200,
    description: '查询成功',
    schema: {
      example: {
        code: 200,
        message: '请求成功',
        data: {
          remainingTime: 240
        }
      }
    }
  })
  async getRemainingTime(@Query('phoneNumber') phoneNumber: string) {
    const remainingTime = await this.captchaService.getCaptchaRemainingTime(phoneNumber);
    return {
      remainingTime: remainingTime > 0 ? remainingTime : 0,
      message: remainingTime > 0 ? '验证码仍然有效' : '验证码已过期或不存在'
    };
  }
}
