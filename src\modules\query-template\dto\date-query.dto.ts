import { IsInt, Min, Max, IsOptional } from 'class-validator';

export class DateQueryDto {
    @IsInt()
    @Min(1)
    @Max(12)
    month: number;

    @IsInt()
    @Min(1)
    @Max(31)
    day: number;

    @IsInt()
    @Min(1900)
    @Max(2100)
    year: number;
}

export class DateQueryResponseDto {
    value: number;
    month: number;
    day: number;
    year: number;
    specialRule?: string;
    isLeapYear: boolean;
} 