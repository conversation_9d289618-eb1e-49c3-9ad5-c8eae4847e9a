import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository, FindOptionsWhere } from 'typeorm';
import { Message, MessageRole, MessageType, MessageStatus } from '../modules/coze/entities/message.entity';

@Injectable()
export class MessageRepository {
    constructor(
        @InjectRepository(Message)
        private readonly messageRepository: Repository<Message>,
    ) { }

    async create(
        conversationId: string, 
        role: MessageRole,
        type: MessageType,
        content: string,
        cozeMessageId?: string,
        metadata?: Record<string, any>,
    ): Promise<Message> {
        const message = this.messageRepository.create({
            conversationId,
            role,
            type,
            content,
            cozeMessageId,
            metadata,
            status: MessageStatus.PENDING,
        });

        return await this.messageRepository.save(message);
    }

    async findById(id: string): Promise<Message | null> {
        return await this.messageRepository.findOne({
            where: { id },
            relations: ['conversation'],
        });
    }

    async findByConversationId(conversationId: string): Promise<Message[]> {
        return await this.messageRepository.find({
            where: { conversationId },
            order: { createdAt: 'ASC' },
        });
    }

    async findByConversationIdPaginated(
        conversationId: string,
        page: number = 1,
        pageSize: number = 50,
        role?: MessageRole,
    ): Promise<{ messages: Message[]; total: number }> {
        const where: FindOptionsWhere<Message> = { conversationId };
        if (role) {
            where.role = role;
        }

        const [messages, total] = await this.messageRepository.findAndCount({
            where,
            order: { createdAt: 'ASC' },
            skip: (page - 1) * pageSize,
            take: pageSize,
        });

        return { messages, total };
    }

    async updateStatus(id: string, status: MessageStatus, usage?: any): Promise<Message | null> {
        const updateData: any = { status };
        if (status === MessageStatus.COMPLETED) {
            updateData.completedAt = new Date();
        }
        if (usage) {
            updateData.usage = usage;
        }

        await this.messageRepository.update(id, updateData);
        return await this.findById(id);
    }

    async updateContent(id: string, content: string): Promise<Message | null> {
        await this.messageRepository.update(id, { content });
        return await this.findById(id);
    }

    async delete(id: string): Promise<boolean> {
        const result = await this.messageRepository.delete(id);
        return (result.affected ?? 0) > 0;
    }

    async getLatestUserMessage(conversationId: string): Promise<Message | null> {
        return await this.messageRepository.findOne({
            where: { conversationId, role: MessageRole.USER },
            order: { createdAt: 'DESC' },
        });
    }

    async getLatestAssistantMessage(conversationId: string): Promise<Message | null> {
        return await this.messageRepository.findOne({
            where: { conversationId, role: MessageRole.ASSISTANT },
            order: { createdAt: 'DESC' },
        });
    }
}