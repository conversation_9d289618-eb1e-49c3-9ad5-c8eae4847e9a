import { IsEnum, IsOptional, IsString, IsNumber, IsDateString, IsObject } from 'class-validator';
import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { PaymentStatus } from '../entities/payment.entity';

/**
 * 更新支付状态DTO
 */
export class UpdatePaymentStatusDto {
  @ApiProperty({ 
    enum: PaymentStatus, 
    description: '支付状态',
    example: PaymentStatus.SUCCESS 
  })
  @IsEnum(PaymentStatus, { message: '支付状态格式不正确' })
  status: PaymentStatus;

  @ApiPropertyOptional({ 
    description: '第三方支付平台交易号',
    example: 'wx_transaction_123456789'
  })
  @IsOptional()
  @IsString()
  thirdPartyTransactionId?: string;

  @ApiPropertyOptional({ 
    description: '失败原因（支付失败时必填）',
    example: '余额不足'
  })
  @IsOptional()
  @IsString()
  failureReason?: string;

  @ApiPropertyOptional({ 
    description: '第三方支付响应数据',
    example: { trade_state: 'SUCCESS', bank_type: 'CMC' }
  })
  @IsOptional()
  @IsObject()
  thirdPartyResponse?: Record<string, any>;

  @ApiPropertyOptional({ 
    description: '支付完成时间（ISO 8601格式）',
    example: '2024-01-15T10:30:00Z'
  })
  @IsOptional()
  @IsDateString()
  paidAt?: string;

  @ApiPropertyOptional({ 
    description: '退款时间（ISO 8601格式）',
    example: '2024-01-16T14:20:00Z'
  })
  @IsOptional()
  @IsDateString()
  refundedAt?: string;

  @ApiPropertyOptional({ 
    description: '退款金额（分）',
    example: 9900,
    minimum: 0
  })
  @IsOptional()
  @IsNumber({}, { message: '退款金额必须是数字' })
  refundAmount?: number;

  @ApiPropertyOptional({ 
    description: '支付扩展信息',
    example: { channel: 'wechat_mini_program', device: 'mobile' }
  })
  @IsOptional()
  @IsObject()
  metadata?: Record<string, any>;
}

/**
 * 支付回调数据DTO
 */
export class PaymentCallbackDto {
  @ApiProperty({ 
    description: '订单号',
    example: 'ORDER_20240115103000123456'
  })
  @IsString()
  orderNo: string;

  @ApiProperty({ 
    description: '第三方交易号',
    example: 'wx_transaction_123456789'
  })
  @IsString()
  thirdPartyTransactionId: string;

  @ApiProperty({ 
    enum: PaymentStatus, 
    description: '支付状态',
    example: PaymentStatus.SUCCESS 
  })
  @IsEnum(PaymentStatus)
  status: PaymentStatus;

  @ApiProperty({ 
    description: '支付金额（分）',
    example: 9900
  })
  @IsNumber()
  amount: number;

  @ApiPropertyOptional({ 
    description: '支付完成时间（ISO 8601格式）',
    example: '2024-01-15T10:30:00Z'
  })
  @IsOptional()
  @IsDateString()
  paidAt?: string;

  @ApiPropertyOptional({ 
    description: '失败原因',
    example: '余额不足'
  })
  @IsOptional()
  @IsString()
  failureReason?: string;

  @ApiPropertyOptional({ 
    description: '第三方支付响应数据'
  })
  @IsOptional()
  @IsObject()
  thirdPartyResponse?: Record<string, any>;
}

/**
 * 支付查询DTO
 */
export class PaymentQueryDto {
  @ApiPropertyOptional({ 
    enum: PaymentStatus, 
    description: '支付状态筛选',
    example: PaymentStatus.SUCCESS 
  })
  @IsOptional()
  @IsEnum(PaymentStatus)
  status?: PaymentStatus;

  @ApiPropertyOptional({ 
    description: '用户ID筛选',
    example: 123
  })
  @IsOptional()
  @IsNumber()
  userId?: number;

  @ApiPropertyOptional({ 
    description: '订单号筛选',
    example: 'ORDER_20240115103000123456'
  })
  @IsOptional()
  @IsString()
  orderNo?: string;

  @ApiPropertyOptional({ 
    description: '页码',
    example: 1,
    minimum: 1
  })
  @IsOptional()
  @IsNumber({}, { message: '页码必须是数字' })
  page?: number = 1;

  @ApiPropertyOptional({ 
    description: '每页数量',
    example: 20,
    minimum: 1,
    maximum: 100
  })
  @IsOptional()
  @IsNumber({}, { message: '每页数量必须是数字' })
  limit?: number = 20;
}

/**
 * 支付响应DTO
 */
export class PaymentResponseDto {
  @ApiProperty({ description: '支付记录ID' })
  id: number;

  @ApiProperty({ description: '订单号' })
  orderNo: string;

  @ApiProperty({ description: '用户ID' })
  userId: number;

  @ApiProperty({ enum: PaymentStatus, description: '支付状态' })
  status: PaymentStatus;

  @ApiProperty({ description: '支付金额（分）' })
  amount: number;

  @ApiPropertyOptional({ description: '支付描述' })
  description?: string;

  @ApiPropertyOptional({ description: '第三方支付平台交易号' })
  thirdPartyTransactionId?: string;

  @ApiPropertyOptional({ description: '支付完成时间' })
  paidAt?: Date;

  @ApiPropertyOptional({ description: '失败原因' })
  failureReason?: string;

  @ApiProperty({ description: '创建时间' })
  createdAt: Date;

  @ApiProperty({ description: '更新时间' })
  updatedAt: Date;
}

/**
 * 支付统计响应DTO
 */
export class PaymentStatsResponseDto {
  @ApiProperty({ description: '总支付次数' })
  totalPayments: number;

  @ApiProperty({ description: '成功支付次数' })
  successfulPayments: number;

  @ApiProperty({ description: '失败支付次数' })
  failedPayments: number;

  @ApiProperty({ description: '总支付金额（分）' })
  totalAmount: number;

  @ApiProperty({ description: '成功支付金额（分）' })
  successfulAmount: number;

  @ApiProperty({ description: '成功率（百分比）' })
  successRate: number;
}
