import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { DateQuery } from './entities/date-query.entity';
import { DateQueryDto, DateQueryResponseDto } from './dto/date-query.dto';

@Injectable()
export class DateQueryService {
  constructor(
    @InjectRepository(DateQuery)
    private dateQueryRepository: Repository<DateQuery>,
  ) {}

  // 初始化数据
  async initializeData(): Promise<void> {
    // 检查是否已经初始化
    const count = await this.dateQueryRepository.count();
    if (count > 0) {
      return; // 数据已存在，跳过初始化
    }

    // 基础数据表（平年数据）
    const baseData = [
      // 一月
      { month: 1, day: 1, value: 24 }, { month: 1, day: 2, value: 24 }, { month: 1, day: 3, value: 24 },
      { month: 1, day: 4, value: 57 }, { month: 1, day: 5, value: 57 }, { month: 1, day: 6, value: 57 },
      { month: 1, day: 7, value: 57 }, { month: 1, day: 8, value: 57 }, { month: 1, day: 9, value: 57 },
      { month: 1, day: 10, value: 59 }, { month: 1, day: 11, value: 59 }, { month: 1, day: 12, value: 6 },
      { month: 1, day: 13, value: 6 }, { month: 1, day: 14, value: 6 }, { month: 1, day: 15, value: 6 },
      { month: 1, day: 16, value: 56 }, { month: 1, day: 17, value: 56 }, { month: 1, day: 18, value: 56 },
      { month: 1, day: 19, value: 56 }, { month: 1, day: 20, value: 56 }, { month: 1, day: 21, value: 56 },
      { month: 1, day: 22, value: 8 }, { month: 1, day: 23, value: 8 }, { month: 1, day: 24, value: 8 },
      { month: 1, day: 25, value: 8 }, { month: 1, day: 26, value: 8 }, { month: 1, day: 27, value: 8 },
      { month: 1, day: 28, value: 30 }, { month: 1, day: 29, value: 30 }, { month: 1, day: 30, value: 30 },
      { month: 1, day: 31, value: 30 },

      // 二月
      { month: 2, day: 1, value: 30 }, { month: 2, day: 2, value: 30 }, { month: 2, day: 3, value: 28 },
      { month: 2, day: 4, value: 28 }, { month: 2, day: 5, value: 28 }, { month: 2, day: 6, value: 28 },
      { month: 2, day: 7, value: 28 }, { month: 2, day: 8, value: 28 }, { month: 2, day: 9, value: 20 },
      { month: 2, day: 10, value: 20 }, { month: 2, day: 11, value: 20 }, { month: 2, day: 12, value: 20 },
      { month: 2, day: 13, value: 20 }, { month: 2, day: 14, value: 20 }, { month: 2, day: 15, value: 14 },
      { month: 2, day: 16, value: 14 }, { month: 2, day: 17, value: 14 }, { month: 2, day: 18, value: 14 },
      { month: 2, day: 19, value: 14 }, { month: 2, day: 20, value: 14 }, { month: 2, day: 21, value: 1 },
      { month: 2, day: 22, value: 1 }, { month: 2, day: 23, value: 1 }, { month: 2, day: 24, value: 1 },
      { month: 2, day: 25, value: 1 }, { month: 2, day: 26, value: 1 }, { month: 2, day: 27, value: 15 },
      { month: 2, day: 28, value: 15 }, { month: 2, day: 29, value: 15 }, { month: 2, day: 30, value: null },
      { month: 2, day: 31, value: null },

      // 三月
      { month: 3, day: 1, value: 15 }, { month: 3, day: 2, value: 15 }, { month: 3, day: 3, value: 15 },
      { month: 3, day: 4, value: 15 }, { month: 3, day: 5, value: 54 }, { month: 3, day: 6, value: 54 },
      { month: 3, day: 7, value: 54 }, { month: 3, day: 8, value: 54 }, { month: 3, day: 9, value: 54 },
      { month: 3, day: 10, value: 54 }, { month: 3, day: 11, value: 47 }, { month: 3, day: 12, value: 47 },
      { month: 3, day: 13, value: 47 }, { month: 3, day: 14, value: 47 }, { month: 3, day: 15, value: 47 },
      { month: 3, day: 16, value: 47 }, { month: 3, day: 17, value: 37 }, { month: 3, day: 18, value: 37 },
      { month: 3, day: 19, value: 37 }, { month: 3, day: 20, value: 37 }, { month: 3, day: 21, value: 36 },
      { month: 3, day: 22, value: 37 }, { month: 3, day: 23, value: 37 }, { month: 3, day: 24, value: 53 },
      { month: 3, day: 25, value: 53 }, { month: 3, day: 26, value: 53 }, { month: 3, day: 27, value: 53 },
      { month: 3, day: 28, value: 53 }, { month: 3, day: 29, value: 53 }, { month: 3, day: 30, value: 24 },
      { month: 3, day: 31, value: 24 },

      // 四月
      { month: 4, day: 1, value: 24 }, { month: 4, day: 2, value: 24 }, { month: 4, day: 3, value: 24 },
      { month: 4, day: 4, value: 24 }, { month: 4, day: 5, value: 46 }, { month: 4, day: 6, value: 46 },
      { month: 4, day: 7, value: 46 }, { month: 4, day: 8, value: 46 }, { month: 4, day: 9, value: 46 },
      { month: 4, day: 10, value: 46 }, { month: 4, day: 11, value: 63 }, { month: 4, day: 12, value: 63 },
      { month: 4, day: 13, value: 63 }, { month: 4, day: 14, value: 63 }, { month: 4, day: 15, value: 63 },
      { month: 4, day: 16, value: 63 }, { month: 4, day: 17, value: 1 }, { month: 4, day: 18, value: 1 },
      { month: 4, day: 19, value: 1 }, { month: 4, day: 20, value: 1 }, { month: 4, day: 21, value: 1 },
      { month: 4, day: 22, value: 1 }, { month: 4, day: 23, value: 2 }, { month: 4, day: 24, value: 2 },
      { month: 4, day: 25, value: 2 }, { month: 4, day: 26, value: 2 }, { month: 4, day: 27, value: 2 },
      { month: 4, day: 28, value: 2 }, { month: 4, day: 29, value: 16 }, { month: 4, day: 30, value: 16 },
      { month: 4, day: 31, value: null },

      // 五月
      { month: 5, day: 1, value: 16 }, { month: 5, day: 2, value: 16 }, { month: 5, day: 3, value: 16 },
      { month: 5, day: 4, value: 16 }, { month: 5, day: 5, value: 40 }, { month: 5, day: 6, value: 40 },
      { month: 5, day: 7, value: 40 }, { month: 5, day: 8, value: 40 }, { month: 5, day: 9, value: 40 },
      { month: 5, day: 10, value: 40 }, { month: 5, day: 11, value: 16 }, { month: 5, day: 12, value: 16 },
      { month: 5, day: 13, value: 16 }, { month: 5, day: 14, value: 16 }, { month: 5, day: 15, value: 16 },
      { month: 5, day: 16, value: 16 }, { month: 5, day: 17, value: 18 }, { month: 5, day: 18, value: 18 },
      { month: 5, day: 19, value: 18 }, { month: 5, day: 20, value: 18 }, { month: 5, day: 21, value: 18 },
      { month: 5, day: 22, value: 18 }, { month: 5, day: 23, value: 22 }, { month: 5, day: 24, value: 22 },
      { month: 5, day: 25, value: 22 }, { month: 5, day: 26, value: 22 }, { month: 5, day: 27, value: 22 },
      { month: 5, day: 28, value: 22 }, { month: 5, day: 29, value: 34 }, { month: 5, day: 30, value: 34 },
      { month: 5, day: 31, value: 34 },

      // 六月
      { month: 6, day: 1, value: 34 }, { month: 6, day: 2, value: 34 }, { month: 6, day: 3, value: 34 },
      { month: 6, day: 4, value: 44 }, { month: 6, day: 5, value: 44 }, { month: 6, day: 6, value: 44 },
      { month: 6, day: 7, value: 44 }, { month: 6, day: 8, value: 44 }, { month: 6, day: 9, value: 44 },
      { month: 6, day: 10, value: 49 }, { month: 6, day: 11, value: 49 }, { month: 6, day: 12, value: 49 },
      { month: 6, day: 13, value: 49 }, { month: 6, day: 14, value: 49 }, { month: 6, day: 15, value: 49 },
      { month: 6, day: 16, value: 23 }, { month: 6, day: 17, value: 23 }, { month: 6, day: 18, value: 23 },
      { month: 6, day: 19, value: 23 }, { month: 6, day: 20, value: 23 }, { month: 6, day: 21, value: 52 },
      { month: 6, day: 22, value: 23 }, { month: 6, day: 23, value: 41 }, { month: 6, day: 24, value: 41 },
      { month: 6, day: 25, value: 41 }, { month: 6, day: 26, value: 41 }, { month: 6, day: 27, value: 41 },
      { month: 6, day: 28, value: 41 }, { month: 6, day: 29, value: 41 }, { month: 6, day: 30, value: 29 },
      { month: 6, day: 31, value: null },

      // 七月
      { month: 7, day: 1, value: 29 }, { month: 7, day: 2, value: 29 }, { month: 7, day: 3, value: 29 },
      { month: 7, day: 4, value: 29 }, { month: 7, day: 5, value: 17 }, { month: 7, day: 6, value: 17 },
      { month: 7, day: 7, value: 17 }, { month: 7, day: 8, value: 17 }, { month: 7, day: 9, value: 17 },
      { month: 7, day: 10, value: 17 }, { month: 7, day: 11, value: 16 }, { month: 7, day: 12, value: 16 },
      { month: 7, day: 13, value: 16 }, { month: 7, day: 14, value: 16 }, { month: 7, day: 15, value: 16 },
      { month: 7, day: 16, value: 16 }, { month: 7, day: 17, value: 53 }, { month: 7, day: 18, value: 53 },
      { month: 7, day: 19, value: 53 }, { month: 7, day: 20, value: 53 }, { month: 7, day: 21, value: 53 },
      { month: 7, day: 22, value: 53 }, { month: 7, day: 23, value: 21 }, { month: 7, day: 24, value: 21 },
      { month: 7, day: 25, value: 21 }, { month: 7, day: 26, value: 21 }, { month: 7, day: 27, value: 21 },
      { month: 7, day: 28, value: 21 }, { month: 7, day: 29, value: 11 }, { month: 7, day: 30, value: 11 },
      { month: 7, day: 31, value: 11 },

      // 八月
      { month: 8, day: 1, value: 11 }, { month: 8, day: 2, value: 11 }, { month: 8, day: 3, value: 11 },
      { month: 8, day: 4, value: 42 }, { month: 8, day: 5, value: 42 }, { month: 8, day: 6, value: 42 },
      { month: 8, day: 7, value: 42 }, { month: 8, day: 8, value: 42 }, { month: 8, day: 9, value: 42 },
      { month: 8, day: 10, value: 59 }, { month: 8, day: 11, value: 59 }, { month: 8, day: 12, value: 59 },
      { month: 8, day: 13, value: 59 }, { month: 8, day: 14, value: 59 }, { month: 8, day: 15, value: 59 },
      { month: 8, day: 16, value: 44 }, { month: 8, day: 17, value: 44 }, { month: 8, day: 18, value: 44 },
      { month: 8, day: 19, value: 44 }, { month: 8, day: 20, value: 44 }, { month: 8, day: 21, value: 44 },
      { month: 8, day: 22, value: 6 }, { month: 8, day: 23, value: 6 }, { month: 8, day: 24, value: 6 },
      { month: 8, day: 25, value: 6 }, { month: 8, day: 26, value: 6 }, { month: 8, day: 27, value: 6 },
      { month: 8, day: 28, value: 50 }, { month: 8, day: 29, value: 50 }, { month: 8, day: 30, value: 50 },
      { month: 8, day: 31, value: 50 },

      // 九月
      { month: 9, day: 1, value: 50 }, { month: 9, day: 2, value: 50 }, { month: 9, day: 3, value: 50 },
      { month: 9, day: 4, value: 50 }, { month: 9, day: 5, value: 50 }, { month: 9, day: 6, value: 50 },
      { month: 9, day: 7, value: 50 }, { month: 9, day: 8, value: 50 }, { month: 9, day: 9, value: 32 },
      { month: 9, day: 10, value: 32 }, { month: 9, day: 11, value: 32 }, { month: 9, day: 12, value: 32 },
      { month: 9, day: 13, value: 32 }, { month: 9, day: 14, value: 32 }, { month: 9, day: 15, value: 10 },
      { month: 9, day: 16, value: 10 }, { month: 9, day: 17, value: 10 }, { month: 9, day: 18, value: 10 },
      { month: 9, day: 19, value: 10 }, { month: 9, day: 20, value: 10 }, { month: 9, day: 21, value: 60 },
      { month: 9, day: 22, value: 60 }, { month: 9, day: 23, value: 62 }, { month: 9, day: 24, value: 60 },
      { month: 9, day: 25, value: 60 }, { month: 9, day: 26, value: 60 }, { month: 9, day: 27, value: 60 },
      { month: 9, day: 28, value: 27 }, { month: 9, day: 29, value: 27 }, { month: 9, day: 30, value: 27 },
      { month: 9, day: 31, value: null },

      // 十月
      { month: 10, day: 1, value: 27 }, { month: 10, day: 2, value: 27 }, { month: 10, day: 3, value: 27 },
      { month: 10, day: 4, value: 27 }, { month: 10, day: 5, value: 27 }, { month: 10, day: 6, value: 27 },
      { month: 10, day: 7, value: 27 }, { month: 10, day: 8, value: 27 }, { month: 10, day: 9, value: 24 },
      { month: 10, day: 10, value: 24 }, { month: 10, day: 11, value: 24 }, { month: 10, day: 12, value: 24 },
      { month: 10, day: 13, value: 24 }, { month: 10, day: 14, value: 24 }, { month: 10, day: 15, value: 24 },
      { month: 10, day: 16, value: 4 }, { month: 10, day: 17, value: 4 }, { month: 10, day: 18, value: 4 },
      { month: 10, day: 19, value: 4 }, { month: 10, day: 20, value: 4 }, { month: 10, day: 21, value: 4 },
      { month: 10, day: 22, value: 61 }, { month: 10, day: 23, value: 61 }, { month: 10, day: 24, value: 61 },
      { month: 10, day: 25, value: 61 }, { month: 10, day: 26, value: 61 }, { month: 10, day: 27, value: 43 },
      { month: 10, day: 28, value: 43 }, { month: 10, day: 29, value: 43 }, { month: 10, day: 30, value: 43 },
      { month: 10, day: 31, value: 43 },

      // 十一月
      { month: 11, day: 1, value: 43 }, { month: 11, day: 2, value: 43 }, { month: 11, day: 3, value: 13 },
      { month: 11, day: 4, value: 13 }, { month: 11, day: 5, value: 13 }, { month: 11, day: 6, value: 13 },
      { month: 11, day: 7, value: 13 }, { month: 11, day: 8, value: 13 }, { month: 11, day: 9, value: 42 },
      { month: 11, day: 10, value: 42 }, { month: 11, day: 11, value: 42 }, { month: 11, day: 12, value: 42 },
      { month: 11, day: 13, value: 42 }, { month: 11, day: 14, value: 42 }, { month: 11, day: 15, value: 61 },
      { month: 11, day: 16, value: 61 }, { month: 11, day: 17, value: 61 }, { month: 11, day: 18, value: 61 },
      { month: 11, day: 19, value: 61 }, { month: 11, day: 20, value: 61 }, { month: 11, day: 21, value: 32 },
      { month: 11, day: 22, value: 32 }, { month: 11, day: 23, value: 32 }, { month: 11, day: 24, value: 32 },
      { month: 11, day: 25, value: 32 }, { month: 11, day: 26, value: 32 }, { month: 11, day: 27, value: 14 },
      { month: 11, day: 28, value: 14 }, { month: 11, day: 29, value: 14 }, { month: 11, day: 30, value: 14 },
      { month: 11, day: 31, value: null },

      // 十二月
      { month: 12, day: 1, value: 14 }, { month: 12, day: 2, value: 14 }, { month: 12, day: 3, value: 37 },
      { month: 12, day: 4, value: 37 }, { month: 12, day: 5, value: 37 }, { month: 12, day: 6, value: 37 },
      { month: 12, day: 7, value: 37 }, { month: 12, day: 8, value: 37 }, { month: 12, day: 9, value: 55 },
      { month: 12, day: 10, value: 55 }, { month: 12, day: 11, value: 55 }, { month: 12, day: 12, value: 55 },
      { month: 12, day: 13, value: 55 }, { month: 12, day: 14, value: 55 }, { month: 12, day: 15, value: 4 },
      { month: 12, day: 16, value: 4 }, { month: 12, day: 17, value: 4 }, { month: 12, day: 18, value: 4 },
      { month: 12, day: 19, value: 4 }, { month: 12, day: 20, value: 64 }, { month: 12, day: 21, value: 16 },
      { month: 12, day: 22, value: 16 }, { month: 12, day: 23, value: 16 }, { month: 12, day: 24, value: 16 },
      { month: 12, day: 25, value: 16 }, { month: 12, day: 26, value: 16 }, { month: 12, day: 27, value: 16 },
      { month: 12, day: 28, value: 16 }, { month: 12, day: 29, value: 24 }, { month: 12, day: 30, value: 24 },
      { month: 12, day: 31, value: 24 },
    ];

    // 特殊规则数据
    const specialRules = [
      // 闰年特殊规则
      { month: 3, day: 21, value: 36, specialRule: '3月21日特殊规则', isLeapYear: true },
      { month: 6, day: 21, value: 52, specialRule: '6月21日特殊规则', isLeapYear: true },
      { month: 9, day: 23, value: 62, specialRule: '9月23日特殊规则', isLeapYear: true },
      { month: 6, day: 20, value: 52, specialRule: '6月20日特殊规则', isLeapYear: true },
      { month: 12, day: 20, value: 52, specialRule: '12月20日特殊规则', isLeapYear: true },
      { month: 7, day: 5, value: 17, specialRule: '7月5日特殊规则', isLeapYear: true },

      // 平年特殊规则
      { month: 3, day: 21, value: 36, specialRule: '3月21日特殊规则', isLeapYear: false },
      { month: 6, day: 21, value: 52, specialRule: '6月21日特殊规则', isLeapYear: false },
      { month: 9, day: 23, value: 62, specialRule: '9月23日特殊规则', isLeapYear: false },
      { month: 6, day: 20, value: 23, specialRule: '6月20日特殊规则', isLeapYear: false },
      { month: 12, day: 20, value: 64, specialRule: '12月20日特殊规则', isLeapYear: false },
      { month: 7, day: 5, value: 17, specialRule: '7月5日特殊规则', isLeapYear: false },
    ];

    // 保存基础数据
    for (const item of baseData) {
      if (item.value !== null) {
        const dateQuery = this.dateQueryRepository.create({
          ...item,
          isLeapYear: false,
          status: 'active'
        });
        await this.dateQueryRepository.save(dateQuery);
      }
    }

    // 保存特殊规则数据
    for (const item of specialRules) {
      const dateQuery = this.dateQueryRepository.create({
        ...item,
        status: 'active'
      });
      await this.dateQueryRepository.save(dateQuery);
    }
  }

  // 判断是否为闰年
  private isLeapYear(year: number): boolean {
    return (year % 4 === 0 && year % 100 !== 0) || (year % 400 === 0);
  }

  // 原型接口：根据年月日查询对应的数字
  async queryByDate(dateQueryDto: DateQueryDto): Promise<DateQueryResponseDto> {
    const { year, month, day } = dateQueryDto;
    const isLeapYear = this.isLeapYear(year);

    // 首先查找特殊规则
    let result = await this.dateQueryRepository.findOne({
      where: {
        month,
        day,
        isLeapYear,
        status: 'active'
      }
    });

    // 如果没有找到特殊规则，查找基础数据
    if (!result) {
      result = await this.dateQueryRepository.findOne({
        where: {
          month,
          day,
          isLeapYear: false,
          status: 'active'
        }
      });
    }

    if (!result) {
      throw new Error(`未找到 ${year}年${month}月${day}日 对应的数据`);
    }

    return {
      value: result.value,
      month: result.month,
      day: result.day,
      year,
      specialRule: result.specialRule,
      isLeapYear: result.isLeapYear
    };
  }

  // 获取所有数据
  async findAll(): Promise<DateQuery[]> {
    return await this.dateQueryRepository.find({
      where: { status: 'active' },
      order: { month: 'ASC', day: 'ASC' }
    });
  }
} 