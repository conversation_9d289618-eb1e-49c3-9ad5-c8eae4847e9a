import { Injectable, Logger, NotFoundException, BadRequestException } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import WxPay from 'wechatpay-node-v3';
import * as fs from 'fs';
import { PaymentRepository } from '../../repositories/payment.repository';
import { Payment, PaymentStatus } from './entities/payment.entity';
import { CreatePaymentDto, PaymentType, PaymentMethod } from './dto/create-payment.dto';
import { CreatePaymentRequestDto, CreateWechatPaymentDto, CreatePaymentResponseDto } from './dto/create-payment-request.dto';
import { UpdatePaymentStatusDto, PaymentCallbackDto, PaymentQueryDto, PaymentResponseDto, PaymentStatsResponseDto } from './dto/update-payment-status.dto';
import { PaymentVerificationDto, PaymentRefundDto } from './dto/create-payment-request.dto';

@Injectable()
export class PaymentService {
  private readonly logger = new Logger(PaymentService.name);
  private pay: any;

  constructor(
    private configService: ConfigService,
    private paymentRepository: PaymentRepository,
  ) {
    this.initWxPay();
  }

  private initWxPay() {
    const wechatPayConfig = this.configService.get('wechatpay');

    this.pay = new WxPay({
      appid: wechatPayConfig.appid,
      mchid: wechatPayConfig.mchid,
      publicKey: fs.readFileSync(wechatPayConfig.publicKeyPath),
      privateKey: fs.readFileSync(wechatPayConfig.privateKeyPath),
    });
  }

  // 生成订单号
  private generateOrderNumber(): string {
    const now = new Date();
    let month: string | number = now.getMonth() + 1;
    let day: string | number = now.getDate();
    let hour: string | number = now.getHours();
    let minutes: string | number = now.getMinutes();
    let seconds: string | number = now.getSeconds();

    month = month < 10 ? "0" + month : month.toString();
    day = day < 10 ? "0" + day : day.toString();
    hour = hour < 10 ? "0" + hour : hour.toString();
    minutes = minutes < 10 ? "0" + minutes : minutes.toString();
    seconds = seconds < 10 ? "0" + seconds : seconds.toString();

    let orderCode =
      now.getFullYear().toString() +
      month +
      day +
      hour +
      minutes +
      seconds +
      Math.round(Math.random() * 1000000).toString();

    return orderCode;
  }

  /**
   * 创建支付订单（新版本 - 包含数据库持久化）
   * @param createPaymentDto 支付创建数据
   * @returns 支付创建响应
   */
  async createPaymentOrder(createPaymentDto: CreatePaymentRequestDto): Promise<CreatePaymentResponseDto> {
    try {
      // 生成订单号
      const orderNo = this.generateOrderNumber();

      // 计算过期时间（默认30分钟）
      const expiresAt = new Date();
      expiresAt.setMinutes(expiresAt.getMinutes() + 30);

      // 创建支付记录
      const paymentData: Partial<Payment> = {
        orderNo,
        userId: createPaymentDto.userId,
        type: createPaymentDto.type,
        method: createPaymentDto.method,
        amount: createPaymentDto.amount,
        quantity: createPaymentDto.quantity || 1,
        status: PaymentStatus.PENDING,
        description: createPaymentDto.description,
        clientIp: createPaymentDto.clientIp,
        notifyUrl: createPaymentDto.notifyUrl,
        returnUrl: createPaymentDto.returnUrl,
        expiresAt,
        metadata: createPaymentDto.metadata,
      };

      const payment = await this.paymentRepository.create(paymentData);
      this.logger.log(`支付记录创建成功: ID=${payment.id}, OrderNo=${orderNo}`);

      // 根据支付方式调用相应的支付接口
      let paymentParams: Record<string, any>;

      if (createPaymentDto.method === PaymentMethod.WECHAT_PAY) {
        paymentParams = await this.createWechatPayment(payment, createPaymentDto as CreateWechatPaymentDto);
      } else if (createPaymentDto.method === PaymentMethod.ALIPAY) {
        paymentParams = await this.createAlipayPayment(payment, createPaymentDto);
      } else {
        throw new BadRequestException('不支持的支付方式');
      }

      // 更新支付记录状态为处理中
      await this.paymentRepository.updateStatus(payment.id, PaymentStatus.PROCESSING);

      return {
        paymentId: payment.id,
        orderNo: payment.orderNo,
        paymentParams,
        expiresAt: payment.expiresAt,
        createdAt: payment.createdAt,
      };
    } catch (error) {
      this.logger.error(`创建支付订单失败: ${error.message}`);
      throw error;
    }
  }

  /**
   * 创建微信支付订单
   * @param payment 支付记录
   * @param createWechatPaymentDto 微信支付数据
   * @returns 微信支付参数
   */
  private async createWechatPayment(payment: Payment, createWechatPaymentDto: CreateWechatPaymentDto): Promise<Record<string, any>> {
    try {
      const wechatPayConfig = this.configService.get('wechatPay');

      const params = {
        description: payment.description || "支付订单",
        out_trade_no: payment.orderNo,
        notify_url: payment.notifyUrl || wechatPayConfig.notifyUrl,
        amount: {
          total: payment.amount,
        },
        payer: {
          openid: createWechatPaymentDto.openid,
        },
        scene_info: {
          payer_client_ip: payment.clientIp || "127.0.0.1",
        },
      };

      const result = await this.pay.transactions_jsapi(params);
      this.logger.log(`微信支付参数生成成功: OrderNo=${payment.orderNo}`);

      // 更新支付记录的支付渠道信息
      await this.paymentRepository.update(payment.id, {
        paymentChannel: 'wechat_jsapi',
        metadata: {
          ...payment.metadata,
          openid: createWechatPaymentDto.openid,
          appid: createWechatPaymentDto.appid || wechatPayConfig.appid,
        },
      });

      return result;
    } catch (error) {
      this.logger.error(`创建微信支付失败: ${error.message}`);

      // 更新支付状态为失败
      await this.paymentRepository.updateStatus(payment.id, PaymentStatus.FAILED, {
        failureReason: `微信支付创建失败: ${error.message}`,
      });

      throw error;
    }
  }

  /**
   * 创建支付宝支付订单
   * @param payment 支付记录
   * @param createPaymentDto 支付数据
   * @returns 支付宝支付参数
   */
  private async createAlipayPayment(payment: Payment, createPaymentDto: CreatePaymentRequestDto): Promise<Record<string, any>> {
    try {
      // TODO: 实现支付宝支付逻辑
      this.logger.log(`创建支付宝支付: OrderNo=${payment.orderNo}`);

      // 更新支付记录的支付渠道信息
      await this.paymentRepository.update(payment.id, {
        paymentChannel: 'alipay_app',
      });

      // 这里应该调用支付宝SDK创建支付订单
      // 暂时返回模拟数据
      return {
        alipay_trade_app_pay_response: {
          code: "10000",
          msg: "Success",
          app_id: "your_app_id",
          out_trade_no: payment.orderNo,
          trade_no: "alipay_trade_no_example",
        }
      };
    } catch (error) {
      this.logger.error(`创建支付宝支付失败: ${error.message}`);

      // 更新支付状态为失败
      await this.paymentRepository.updateStatus(payment.id, PaymentStatus.FAILED, {
        failureReason: `支付宝支付创建失败: ${error.message}`,
      });

      throw error;
    }
  }

  // 保留原有的创建支付方法以保持向后兼容
  async createPayment(openid: string, amount: number = 1, description: string = "Asnull的支付测试") {
    try {
      const wechatPayConfig = this.configService.get('wechatPay');

      const params = {
        description: description,
        out_trade_no: this.generateOrderNumber(),
        notify_url: wechatPayConfig.notifyUrl,
        amount: {
          total: amount,
        },
        payer: {
          openid: openid,
        },
        scene_info: {
          payer_client_ip: "127.0.0.1",
        },
      };

      const result = await this.pay.transactions_jsapi(params);
      this.logger.log(`支付参数生成成功: ${JSON.stringify(result)}`);

      return result;
    } catch (error) {
      this.logger.error(`创建支付订单失败: ${error.message}`);
      throw error;
    }
  }

  /**
   * 处理支付回调（增强版本）
   * @param callbackData 回调数据
   * @returns 处理结果
   */
  async handlePaymentCallback(callbackData: any): Promise<{ success: boolean; message: string }> {
    try {
      const wechatPayConfig = this.configService.get('wechatPay');
      const { ciphertext, associated_data, nonce } = callbackData.resource;

      // 解密回调信息
      const result = this.pay.decipher_gcm(
        ciphertext,
        associated_data,
        nonce,
        wechatPayConfig.apiV3Key
      );

      this.logger.log(`支付回调解密结果: OrderNo=${result.out_trade_no}, State=${result.trade_state}`);

      // 根据订单号查找支付记录
      const payment = await this.paymentRepository.findByOrderNo(result.out_trade_no);
      if (!payment) {
        this.logger.error(`支付记录不存在: OrderNo=${result.out_trade_no}`);
        return { success: false, message: '支付记录不存在' };
      }

      // 处理不同的支付状态
      if (result.trade_state === 'SUCCESS') {
        await this.handlePaymentSuccess(payment, result);
        return { success: true, message: '支付成功' };
      } else if (result.trade_state === 'PAYERROR') {
        await this.handlePaymentFailure(payment, result);
        return { success: false, message: '支付失败' };
      } else if (result.trade_state === 'CLOSED') {
        await this.handlePaymentCancelled(payment, result);
        return { success: false, message: '支付已关闭' };
      } else {
        this.logger.warn(`未处理的支付状态: ${result.trade_state}`);

        // 更新支付记录的第三方响应数据
        await this.paymentRepository.update(payment.id, {
          thirdPartyResponse: result,
        });

        return { success: false, message: `支付状态: ${result.trade_state}` };
      }
    } catch (error) {
      this.logger.error(`处理支付回调失败: ${error.message}`);
      throw error;
    }
  }

  /**
   * 处理支付成功
   * @param payment 支付记录
   * @param paymentResult 第三方支付结果
   */
  private async handlePaymentSuccess(payment: Payment, paymentResult: any): Promise<void> {
    try {
      const { out_trade_no, transaction_id, amount, payer, success_time } = paymentResult;

      this.logger.log(`支付成功 - 订单号: ${out_trade_no}, 交易号: ${transaction_id}`);
      this.logger.log(`支付金额: ${amount.total}分, 用户openid: ${payer.openid}`);

      // 更新支付状态为成功
      await this.paymentRepository.updateStatus(payment.id, PaymentStatus.SUCCESS, {
        thirdPartyTransactionId: transaction_id,
        paidAt: new Date(success_time),
        thirdPartyResponse: paymentResult,
      });

      // 执行业务逻辑
      await this.executePaymentSuccessBusinessLogic(payment, paymentResult);

      this.logger.log(`支付成功处理完成: PaymentId=${payment.id}`);
    } catch (error) {
      this.logger.error(`处理支付成功失败: ${error.message}`);
      throw error;
    }
  }

  /**
   * 处理支付失败
   * @param payment 支付记录
   * @param paymentResult 第三方支付结果
   */
  private async handlePaymentFailure(payment: Payment, paymentResult: any): Promise<void> {
    try {
      const { out_trade_no, trade_state_desc } = paymentResult;

      this.logger.warn(`支付失败 - 订单号: ${out_trade_no}, 失败原因: ${trade_state_desc}`);

      // 更新支付状态为失败
      await this.paymentRepository.updateStatus(payment.id, PaymentStatus.FAILED, {
        failureReason: trade_state_desc || '支付失败',
        thirdPartyResponse: paymentResult,
      });

      this.logger.log(`支付失败处理完成: PaymentId=${payment.id}`);
    } catch (error) {
      this.logger.error(`处理支付失败失败: ${error.message}`);
      throw error;
    }
  }

  /**
   * 处理支付取消
   * @param payment 支付记录
   * @param paymentResult 第三方支付结果
   */
  private async handlePaymentCancelled(payment: Payment, paymentResult: any): Promise<void> {
    try {
      const { out_trade_no, trade_state_desc } = paymentResult;

      this.logger.log(`支付取消 - 订单号: ${out_trade_no}, 原因: ${trade_state_desc}`);

      // 更新支付状态为已取消
      await this.paymentRepository.updateStatus(payment.id, PaymentStatus.CANCELLED, {
        failureReason: trade_state_desc || '支付已取消',
        thirdPartyResponse: paymentResult,
      });

      this.logger.log(`支付取消处理完成: PaymentId=${payment.id}`);
    } catch (error) {
      this.logger.error(`处理支付取消失败: ${error.message}`);
      throw error;
    }
  }

  /**
   * 执行支付成功后的业务逻辑
   * @param payment 支付记录
   * @param paymentResult 第三方支付结果
   */
  private async executePaymentSuccessBusinessLogic(payment: Payment, paymentResult: any): Promise<void> {
    try {
      // 根据支付类型执行不同的业务逻辑
      switch (payment.type) {
        case 'team_permission':
          await this.handleTeamPermissionPayment(payment);
          break;
        case 'assessment_credits':
          await this.handleAssessmentCreditsPayment(payment);
          break;
        case 'ai_interpretation_credits':
          await this.handleAIInterpretationCreditsPayment(payment);
          break;
        default:
          this.logger.warn(`未知的支付类型: ${payment.type}`);
      }
    } catch (error) {
      this.logger.error(`执行支付成功业务逻辑失败: ${error.message}`);
      // 注意：这里不抛出异常，避免影响支付状态更新
    }
  }

  /**
   * 处理团队权限支付
   * @param payment 支付记录
   */
  private async handleTeamPermissionPayment(payment: Payment): Promise<void> {
    this.logger.log(`处理团队权限支付: PaymentId=${payment.id}, UserId=${payment.userId}`);
    // TODO: 实现团队权限开通逻辑
    // 例如：更新用户的团队权限状态、设置过期时间等
  }

  /**
   * 处理评估次数购买
   * @param payment 支付记录
   */
  private async handleAssessmentCreditsPayment(payment: Payment): Promise<void> {
    this.logger.log(`处理评估次数购买: PaymentId=${payment.id}, UserId=${payment.userId}, Quantity=${payment.quantity}`);
    // TODO: 实现评估次数增加逻辑
    // 例如：增加用户的评估次数
  }

  /**
   * 处理AI解读次数购买
   * @param payment 支付记录
   */
  private async handleAIInterpretationCreditsPayment(payment: Payment): Promise<void> {
    this.logger.log(`处理AI解读次数购买: PaymentId=${payment.id}, UserId=${payment.userId}, Quantity=${payment.quantity}`);
    // TODO: 实现AI解读次数增加逻辑
    // 例如：增加用户的AI解读次数
  }

  /**
   * 查询支付记录
   * @param queryDto 查询条件
   * @returns 支付记录列表
   */
  async queryPayments(queryDto: PaymentQueryDto): Promise<{ payments: PaymentResponseDto[]; total: number; page: number; limit: number }> {
    try {
      const { page = 1, limit = 20, ...filters } = queryDto;
      const offset = (page - 1) * limit;

      const { payments, total } = await this.paymentRepository.findAll({
        ...filters,
        limit,
        offset,
      });

      const paymentResponses: PaymentResponseDto[] = payments.map(payment => ({
        id: payment.id,
        orderNo: payment.orderNo,
        userId: payment.userId,
        status: payment.status,
        amount: payment.amount,
        description: payment.description,
        thirdPartyTransactionId: payment.thirdPartyTransactionId,
        paidAt: payment.paidAt,
        failureReason: payment.failureReason,
        createdAt: payment.createdAt,
        updatedAt: payment.updatedAt,
      }));

      return {
        payments: paymentResponses,
        total,
        page,
        limit,
      };
    } catch (error) {
      this.logger.error(`查询支付记录失败: ${error.message}`);
      throw error;
    }
  }

  /**
   * 根据ID获取支付记录
   * @param id 支付记录ID
   * @returns 支付记录
   */
  async getPaymentById(id: number): Promise<PaymentResponseDto> {
    try {
      const payment = await this.paymentRepository.findById(id);
      if (!payment) {
        throw new NotFoundException(`支付记录不存在: ID=${id}`);
      }

      return {
        id: payment.id,
        orderNo: payment.orderNo,
        userId: payment.userId,
        status: payment.status,
        amount: payment.amount,
        description: payment.description,
        thirdPartyTransactionId: payment.thirdPartyTransactionId,
        paidAt: payment.paidAt,
        failureReason: payment.failureReason,
        createdAt: payment.createdAt,
        updatedAt: payment.updatedAt,
      };
    } catch (error) {
      this.logger.error(`获取支付记录失败: ${error.message}`);
      throw error;
    }
  }

  /**
   * 根据订单号获取支付记录
   * @param orderNo 订单号
   * @returns 支付记录
   */
  async getPaymentByOrderNo(orderNo: string): Promise<PaymentResponseDto> {
    try {
      const payment = await this.paymentRepository.findByOrderNo(orderNo);
      if (!payment) {
        throw new NotFoundException(`支付记录不存在: OrderNo=${orderNo}`);
      }

      return {
        id: payment.id,
        orderNo: payment.orderNo,
        userId: payment.userId,
        status: payment.status,
        amount: payment.amount,
        description: payment.description,
        thirdPartyTransactionId: payment.thirdPartyTransactionId,
        paidAt: payment.paidAt,
        failureReason: payment.failureReason,
        createdAt: payment.createdAt,
        updatedAt: payment.updatedAt,
      };
    } catch (error) {
      this.logger.error(`获取支付记录失败: ${error.message}`);
      throw error;
    }
  }

  /**
   * 更新支付状态
   * @param id 支付记录ID
   * @param updateStatusDto 状态更新数据
   */
  async updatePaymentStatus(id: number, updateStatusDto: UpdatePaymentStatusDto): Promise<void> {
    try {
      const payment = await this.paymentRepository.findById(id);
      if (!payment) {
        throw new NotFoundException(`支付记录不存在: ID=${id}`);
      }

      const additionalData: any = {};

      if (updateStatusDto.thirdPartyTransactionId) {
        additionalData.thirdPartyTransactionId = updateStatusDto.thirdPartyTransactionId;
      }
      if (updateStatusDto.failureReason) {
        additionalData.failureReason = updateStatusDto.failureReason;
      }
      if (updateStatusDto.thirdPartyResponse) {
        additionalData.thirdPartyResponse = updateStatusDto.thirdPartyResponse;
      }
      if (updateStatusDto.paidAt) {
        additionalData.paidAt = new Date(updateStatusDto.paidAt);
      }
      if (updateStatusDto.refundedAt) {
        additionalData.refundedAt = new Date(updateStatusDto.refundedAt);
      }
      if (updateStatusDto.refundAmount !== undefined) {
        additionalData.refundAmount = updateStatusDto.refundAmount;
      }

      await this.paymentRepository.updateStatus(id, updateStatusDto.status, additionalData);

      this.logger.log(`支付状态更新成功: ID=${id}, Status=${updateStatusDto.status}`);
    } catch (error) {
      this.logger.error(`更新支付状态失败: ${error.message}`);
      throw error;
    }
  }

  /**
   * 获取用户支付统计信息
   * @param userId 用户ID
   * @returns 支付统计信息
   */
  async getUserPaymentStats(userId: number): Promise<PaymentStatsResponseDto> {
    try {
      const stats = await this.paymentRepository.getPaymentStats(userId);

      const successRate = stats.totalPayments > 0
        ? Math.round((stats.successfulPayments / stats.totalPayments) * 100)
        : 0;

      return {
        ...stats,
        successRate,
      };
    } catch (error) {
      this.logger.error(`获取用户支付统计失败: ${error.message}`);
      throw error;
    }
  }

  /**
   * 验证支付状态
   * @param verificationDto 验证数据
   * @returns 支付状态
   */
  async verifyPaymentStatus(verificationDto: PaymentVerificationDto): Promise<PaymentResponseDto> {
    try {
      let payment: Payment | null = null;

      if (verificationDto.orderNo) {
        payment = await this.paymentRepository.findByOrderNo(verificationDto.orderNo);
      } else if (verificationDto.thirdPartyTransactionId) {
        payment = await this.paymentRepository.findByThirdPartyTransactionId(verificationDto.thirdPartyTransactionId);
      }

      if (!payment) {
        throw new NotFoundException('支付记录不存在');
      }

      // 如果支付状态是处理中，可以主动查询第三方支付状态
      if (payment.status === PaymentStatus.PROCESSING) {
        await this.queryThirdPartyPaymentStatus(payment);
        // 重新获取更新后的支付记录
        const updatedPayment = await this.paymentRepository.findById(payment.id);
        if (updatedPayment) {
          payment = updatedPayment;
        }
      }

      return {
        id: payment.id,
        orderNo: payment.orderNo,
        userId: payment.userId,
        status: payment.status,
        amount: payment.amount,
        description: payment.description,
        thirdPartyTransactionId: payment.thirdPartyTransactionId,
        paidAt: payment.paidAt,
        failureReason: payment.failureReason,
        createdAt: payment.createdAt,
        updatedAt: payment.updatedAt,
      };
    } catch (error) {
      this.logger.error(`验证支付状态失败: ${error.message}`);
      throw error;
    }
  }

  /**
   * 查询第三方支付状态
   * @param payment 支付记录
   */
  private async queryThirdPartyPaymentStatus(payment: Payment): Promise<void> {
    try {
      if (payment.method === PaymentMethod.WECHAT_PAY) {
        await this.queryWechatPaymentStatus(payment);
      } else if (payment.method === PaymentMethod.ALIPAY) {
        await this.queryAlipayPaymentStatus(payment);
      }
    } catch (error) {
      this.logger.error(`查询第三方支付状态失败: ${error.message}`);
      // 不抛出异常，避免影响主流程
    }
  }

  /**
   * 查询微信支付状态
   * @param payment 支付记录
   */
  private async queryWechatPaymentStatus(payment: Payment): Promise<void> {
    try {
      // TODO: 实现微信支付状态查询
      this.logger.log(`查询微信支付状态: OrderNo=${payment.orderNo}`);

      // 这里应该调用微信支付查询接口
      // const result = await this.pay.query({ out_trade_no: payment.orderNo });
      // 根据查询结果更新支付状态
    } catch (error) {
      this.logger.error(`查询微信支付状态失败: ${error.message}`);
    }
  }

  /**
   * 查询支付宝支付状态
   * @param payment 支付记录
   */
  private async queryAlipayPaymentStatus(payment: Payment): Promise<void> {
    try {
      // TODO: 实现支付宝支付状态查询
      this.logger.log(`查询支付宝支付状态: OrderNo=${payment.orderNo}`);

      // 这里应该调用支付宝查询接口
    } catch (error) {
      this.logger.error(`查询支付宝支付状态失败: ${error.message}`);
    }
  }
}