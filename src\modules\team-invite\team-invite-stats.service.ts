import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository, Between, MoreThanOrEqual, LessThanOrEqual } from 'typeorm';
import { User } from '../users/entities/user.entity';
import { TeamInvite } from './entities/team-invite.entity';
import { 
  StatsQueryDto, 
  TrendQueryDto, 
  RankingQueryDto, 
  ActivityQueryDto, 
  FeatureQueryDto 
} from './dto/stats-query.dto';
import {
  EChartsDataDto,
  StatsOverviewDto,
  UserActivityDto,
  UserRankingDto,
  DailyActivityDto,
  FeatureUsageDto,
  TrendDataDto,
  RankingDataDto,
  ActivityDataDto,
  FeatureUsageDataDto
} from './dto/stats-response.dto';

@Injectable()
export class TeamInviteStatsService {
  constructor(
    @InjectRepository(User)
    private userRepository: Repository<User>,
    @InjectRepository(TeamInvite)
    private teamInviteRepository: Repository<TeamInvite>,
  ) {}

  /**
   * 计算时间范围
   */
  private calculateDateRange(timeRange: string = '30d', startDate?: string, endDate?: string) {
    const now = new Date();
    let start: Date;
    let end: Date = now;

    if (timeRange === 'custom' && startDate && endDate) {
      start = new Date(startDate);
      end = new Date(endDate);
    } else {
      const days = {
        '7d': 7,
        '30d': 30,
        '90d': 90
      }[timeRange] || 30;
      
      start = new Date(now.getTime() - days * 24 * 60 * 60 * 1000);
    }

    return { start, end };
  }

  /**
   * 获取统计概览
   */
  async getOverview(userId: number, query: StatsQueryDto): Promise<StatsOverviewDto> {
    const { start, end } = this.calculateDateRange(query.timeRange || '30d', query.startDate, query.endDate);
    
    // 获取总邀请用户数
    const totalInvitedUsers = await this.userRepository.count({
      where: { invitedById: userId }
    });

    // 获取活跃用户数（在指定时间范围内有活动的用户）
    const activeUsers = await this.userRepository
      .createQueryBuilder('user')
      .where('user.invitedById = :userId', { userId })
      .andWhere('user.lastActiveAt >= :start', { start })
      .getCount();

    // 获取总使用次数（这里需要根据实际的使用记录表来查询）
    const totalUsage = await this.getTotalUsage(userId, start, end);

    // 计算平均使用次数
    const avgUsagePerUser = totalInvitedUsers > 0 ? totalUsage / totalInvitedUsers : 0;

    // 计算活跃率
    const activeRate = totalInvitedUsers > 0 ? (activeUsers / totalInvitedUsers) * 100 : 0;

    // 计算增长率（与上一个时间段比较）
    const previousStart = new Date(start.getTime() - (end.getTime() - start.getTime()));
    const previousUsage = await this.getTotalUsage(userId, previousStart, start);
    const growthRate = previousUsage > 0 ? ((totalUsage - previousUsage) / previousUsage) * 100 : 0;

    return {
      totalInvitedUsers,
      activeUsers,
      avgUsagePerUser: Math.round(avgUsagePerUser * 100) / 100,
      activeRate: Math.round(activeRate * 100) / 100,
      totalUsage,
      growthRate: Math.round(growthRate * 100) / 100,
      timeRange: query.timeRange || '30d'
    };
  }

  /**
   * 获取使用趋势数据（ECharts格式）
   */
  async getUsageTrend(userId: number, query: TrendQueryDto): Promise<TrendDataDto> {
    const { start, end } = this.calculateDateRange(query.timeRange || '30d');
    
    // 生成日期序列
    const dateArray = this.generateDateArray(start, end);
    
    // 获取每日使用数据
    const dailyData = await this.getDailyUsageData(userId, start, end);
    
    // 构建ECharts数据格式
    const chartData: EChartsDataDto = {
      xAxis: {
        type: 'category',
        data: dateArray.map(date => this.formatDate(date)),
        axisLabel: {
          rotate: 45
        }
      },
      yAxis: {
        type: 'value',
        name: '使用次数'
      },
      series: [
        {
          name: '总使用次数',
          type: query.chartType || 'line',
          data: dateArray.map(date => {
            const dayData = dailyData.find(d => 
              this.formatDate(new Date(d.date)) === this.formatDate(date)
            );
            return dayData ? dayData.totalUsage : 0;
          }),
          smooth: true,
          itemStyle: {
            color: '#5470c6'
          }
        },
        {
          name: '活跃用户数',
          type: query.chartType || 'line',
          data: dateArray.map(date => {
            const dayData = dailyData.find(d => 
              this.formatDate(new Date(d.date)) === this.formatDate(date)
            );
            return dayData ? dayData.activeUsers : 0;
          }),
          smooth: true,
          itemStyle: {
            color: '#91cc75'
          }
        }
      ],
      legend: {
        data: ['总使用次数', '活跃用户数']
      },
      tooltip: {
        trigger: 'axis'
      }
    };

    const overview = await this.getOverview(userId, { timeRange: query.timeRange || '30d' } as StatsQueryDto);

    return {
      chartData,
      overview,
      timeRange: query.timeRange || '30d'
    };
  }

  /**
   * 获取用户活跃度排名（ECharts格式）
   */
  async getUserRanking(userId: number, query: RankingQueryDto): Promise<RankingDataDto> {
    const { start, end } = this.calculateDateRange('30d'); // 排名使用30天数据
    
    // 获取用户排名数据
    const userRanking = await this.getUserRankingData(userId, start, end, query.sortBy || 'usage', query.limit || 10);
    
    // 构建ECharts数据格式
    const chartData: EChartsDataDto = {
      xAxis: {
        type: 'category',
        data: userRanking.map(user => user.userName),
        axisLabel: {
          rotate: 45
        }
      },
      yAxis: {
        type: 'value',
        name: '使用次数'
      },
      series: [
        {
          name: '使用次数',
          type: 'bar',
          data: userRanking.map(user => user.usageCount),
          itemStyle: {
            color: function(params: any) {
              const colors = ['#5470c6', '#91cc75', '#fac858', '#ee6666', '#73c0de'];
              return colors[params.dataIndex % colors.length];
            }
          }
        }
      ],
      tooltip: {
        trigger: 'axis',
        formatter: function(params: any) {
          const data = params[0];
          const user = userRanking[data.dataIndex];
          return `
            <div>
              <p><strong>${user.userName}</strong></p>
              <p>排名: ${user.rank}</p>
              <p>使用次数: ${data.value}</p>
              <p>最后活跃: ${new Date(user.lastActiveDate).toLocaleDateString()}</p>
            </div>
          `;
        }
      }
    };

    return {
      chartData,
      userList: userRanking,
      totalUsers: userRanking.length
    };
  }

  /**
   * 获取每日活跃度热力图数据（ECharts格式）
   */
  async getDailyActivity(userId: number, query: ActivityQueryDto): Promise<ActivityDataDto> {
    const { start, end } = this.calculateDateRange(query.timeRange || '30d');
    
    // 获取每日活跃度数据
    const dailyActivity = await this.getDailyActivityData(userId, start, end);
    
    // 构建ECharts热力图数据格式
    const chartData: EChartsDataDto = {
      tooltip: {
        position: 'top'
      },
      grid: {
        height: '50%',
        top: '10%'
      },
      xAxis: {
        type: 'category',
        data: this.generateWeekDays(),
        splitArea: {
          show: true
        }
      },
      yAxis: {
        type: 'category',
        data: this.generateWeekLabels(start, end),
        splitArea: {
          show: true
        }
      },
      visualMap: {
        min: 0,
        max: Math.max(...dailyActivity.map(d => d.value)),
        calculable: true,
        orient: 'horizontal',
        left: 'center',
        bottom: '15%'
      },
      series: [{
        name: '活跃度',
        type: 'heatmap',
        data: this.formatHeatmapData(dailyActivity),
        label: {
          show: true
        },
        emphasis: {
          itemStyle: {
            shadowBlur: 10,
            shadowColor: 'rgba(0, 0, 0, 0.5)'
          }
        }
      }]
    };

    return {
      chartData,
      dailyData: dailyActivity,
      timeRange: query.timeRange || '30d'
    };
  }

  /**
   * 获取功能使用分布数据（ECharts格式）
   */
  async getFeatureUsage(userId: number, query: FeatureQueryDto): Promise<FeatureUsageDataDto> {
    const { start, end } = this.calculateDateRange(query.timeRange || '30d');
    
    // 获取功能使用数据
    const featureUsage = await this.getFeatureUsageData(userId, start, end);
    
    // 构建ECharts饼图数据格式
    const chartData: EChartsDataDto = {
      xAxis: {
        type: 'category',
        data: []
      },
      yAxis: {
        type: 'value',
        data: []
      },
      tooltip: {
        trigger: 'item',
        formatter: '{a} <br/>{b}: {c} ({d}%)'
      },
      legend: {
        orient: 'vertical',
        left: 'left',
        data: featureUsage.map(f => f.name)
      },
      series: [
        {
          name: '功能使用',
          type: 'pie',
          radius: '50%',
          data: featureUsage.map(f => ({
            name: f.name,
            value: f.value
          })),
          emphasis: {
            itemStyle: {
              shadowBlur: 10,
              shadowOffsetX: 0,
              shadowColor: 'rgba(0, 0, 0, 0.5)'
            }
          }
        }
      ]
    };

    const totalUsage = featureUsage.reduce((sum, f) => sum + f.value, 0);

    return {
      chartData,
      featureList: featureUsage,
      totalUsage
    };
  }

  // 辅助方法
  private async getTotalUsage(userId: number, start: Date, end: Date): Promise<number> {
    // 这里需要根据实际的使用记录表来查询
    // 暂时返回模拟数据
    return Math.floor(Math.random() * 1000) + 100;
  }

  private generateDateArray(start: Date, end: Date): Date[] {
    const dates: Date[] = [];
    const current = new Date(start);
    
    while (current <= end) {
      dates.push(new Date(current));
      current.setDate(current.getDate() + 1);
    }
    
    return dates;
  }

  private formatDate(date: Date): string {
    return date.toISOString().split('T')[0];
  }

  private async getDailyUsageData(userId: number, start: Date, end: Date): Promise<any[]> {
    // 这里需要根据实际的使用记录表来查询
    // 返回模拟数据
    const days = Math.ceil((end.getTime() - start.getTime()) / (1000 * 60 * 60 * 24));
    return Array.from({ length: days }, (_, i) => ({
      date: new Date(start.getTime() + i * 24 * 60 * 60 * 1000),
      totalUsage: Math.floor(Math.random() * 50) + 10,
      activeUsers: Math.floor(Math.random() * 10) + 1
    }));
  }

  private async getUserRankingData(userId: number, start: Date, end: Date, sortBy: string, limit: number): Promise<UserRankingDto[]> {
    // 这里需要根据实际的使用记录表来查询
    // 返回模拟数据
    return Array.from({ length: limit }, (_, i) => ({
      userId: i + 1,
      userName: `用户${i + 1}`,
      userEmail: `user${i + 1}@example.com`,
      rank: i + 1,
      usageCount: Math.floor(Math.random() * 100) + 10,
      lastActiveDate: new Date(),
      joinDate: new Date(Date.now() - Math.random() * 30 * 24 * 60 * 60 * 1000)
    }));
  }

  private generateWeekDays(): string[] {
    return ['周一', '周二', '周三', '周四', '周五', '周六', '周日'];
  }

  private generateWeekLabels(start: Date, end: Date): string[] {
    const weeks: string[] = [];
    const current = new Date(start);
    
    while (current <= end) {
      const weekStart = new Date(current);
      weekStart.setDate(current.getDate() - current.getDay() + 1);
      const weekEnd = new Date(weekStart);
      weekEnd.setDate(weekStart.getDate() + 6);
      
      weeks.push(`${this.formatDate(weekStart)} ~ ${this.formatDate(weekEnd)}`);
      current.setDate(current.getDate() + 7);
    }
    
    return weeks;
  }

  private async getDailyActivityData(userId: number, start: Date, end: Date): Promise<DailyActivityDto[]> {
    // 这里需要根据实际的使用记录表来查询
    // 返回模拟数据
    const days = Math.ceil((end.getTime() - start.getTime()) / (1000 * 60 * 60 * 24));
    return Array.from({ length: days }, (_, i) => ({
      date: this.formatDate(new Date(start.getTime() + i * 24 * 60 * 60 * 1000)),
      value: Math.floor(Math.random() * 20) + 1,
      usageCount: Math.floor(Math.random() * 50) + 10
    }));
  }

  private formatHeatmapData(dailyActivity: DailyActivityDto[]): any[] {
    const weekDays = this.generateWeekDays();
    const result: any[] = [];
    
    dailyActivity.forEach((activity, index) => {
      const date = new Date(activity.date);
      const dayOfWeek = date.getDay() || 7; // 转换为1-7
      const weekIndex = Math.floor(index / 7);
      
      result.push([dayOfWeek - 1, weekIndex, activity.value]);
    });
    
    return result;
  }

  private async getFeatureUsageData(userId: number, start: Date, end: Date): Promise<FeatureUsageDto[]> {
    // 这里需要根据实际的使用记录表来查询
    // 返回模拟数据
    const features = [
      { name: 'AI解读', value: 45 },
      { name: '评估查询', value: 35 },
      { name: '数据导出', value: 20 },
      { name: '报告生成', value: 15 },
      { name: '其他功能', value: 10 }
    ];
    
    const total = features.reduce((sum, f) => sum + f.value, 0);
    
    return features.map(f => ({
      ...f,
      percentage: Math.round((f.value / total) * 100)
    }));
  }
} 