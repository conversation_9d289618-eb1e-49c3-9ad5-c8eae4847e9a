import { Controller, Get, Post, Body, Param, Delete, Query } from '@nestjs/common';
import { QuestionService } from './question.service';
import { Question } from './entities/question.entity';
import { ApiTags, ApiOperation, ApiResponse, ApiQuery } from '@nestjs/swagger';
import { CalculateResultDto, CalculateResultResponseDto, CalculateResultWithPersonalityResponseDto } from './dto/calculate-result.dto';
import { CreatePersonalityResultDto, GetPersonalityResultDto, PersonalityResultResponseDto } from './dto/personality-result.dto';
import { CreateUserAnswerRecordDto, GetUserAnswerRecordsDto, GetUserCurrentTypeDto, UserAnswerRecordResponseDto, UserCurrentTypeResponseDto, PaginatedUserAnswerRecordsResponseDto } from './dto/user-answer-record.dto';

@ApiTags('题目管理')
@Controller('questions')
export class QuestionController {
  constructor(private readonly questionService: QuestionService) { }

  @Get()
  async findAll(): Promise<Question[]> {
    return this.questionService.findAll();
  }

  @Post()
  async create(@Body() createQuestionDto: any): Promise<Question> {
    return this.questionService.create(createQuestionDto);
  } 
  @Get(':id')
  async getQuestionById(@Param('id') id: number): Promise<Question> {
    return this.questionService.findOne(id);
  }


  @Delete(':id')
  async remove(@Param('id') id: number): Promise<void> {
    return this.questionService.remove(id);
  } 

  @Post('calculate-results')
  @ApiOperation({ summary: '计算MBTI和DISC结果' })
  @ApiResponse({
    status: 200,
    description: '计算结果成功',
    type: CalculateResultResponseDto,
    schema: {
      example: {
        mbti: { E: 5, I: 3, S: 4, N: 4, T: 6, F: 2, J: 3, P: 5 },
        disc: { D: 8, I: 6, S: 10, C: 8 },
        mbtiType: 'ENFP',
        discType: 'S',
        finalType: 'ENFP-S',
        dimensionScores: {
          '能量互动': 5,
          '信息感知': 4,
          '决策形成': 6,
          '行动实施': 5
        }
      }
    }
  })
  @ApiResponse({ status: 400, description: '答案数组长度不正确或题目数量不正确' })
  async calculateResults(@Body() calculateResultDto: CalculateResultDto): Promise<CalculateResultResponseDto> {
    return this.questionService.calculateResults(calculateResultDto);
  }

  // 人格测试结果相关接口
  @Post('personality-results')
  @ApiOperation({ summary: '创建人格测试结果' })
  @ApiResponse({ status: 201, description: '创建成功', type: PersonalityResultResponseDto })
  @ApiResponse({ status: 400, description: '该人格类型已存在' })
  async createPersonalityResult(@Body() createPersonalityResultDto: CreatePersonalityResultDto): Promise<PersonalityResultResponseDto> {
    return this.questionService.createPersonalityResult(createPersonalityResultDto);
  }

  @Get('personality-results')
  @ApiOperation({ summary: '获取所有人格测试结果' })
  @ApiResponse({ status: 200, description: '获取成功', type: [PersonalityResultResponseDto] })
  async getAllPersonalityResults(): Promise<PersonalityResultResponseDto[]> {
    return this.questionService.getAllPersonalityResults();
  }

  @Post('personality-results/get')
  @ApiOperation({ summary: '根据类型代码获取人格测试结果' })
  @ApiResponse({ status: 200, description: '获取成功', type: PersonalityResultResponseDto })
  @ApiResponse({ status: 404, description: '未找到该人格类型的结果' })
  async getPersonalityResult(@Body() getPersonalityResultDto: GetPersonalityResultDto): Promise<PersonalityResultResponseDto> {
    return this.questionService.getPersonalityResult(getPersonalityResultDto);
  }

  @Post('personality-results/seed')
  @ApiOperation({ summary: '批量创建人格测试结果数据' })
  @ApiResponse({ status: 200, description: '批量创建成功' })
  async seedPersonalityResults(): Promise<{ message: string }> {
    await this.questionService.seedPersonalityResults();
    return { message: '人格测试结果数据创建成功' };
  }

  @Post('personality-results/calculate')
  @ApiOperation({ summary: '根据答题结果获取对应的人格测试结果' })
  @ApiResponse({ status: 200, description: '获取成功', type: PersonalityResultResponseDto })
  @ApiResponse({ status: 400, description: '答案数组长度不正确或题目数量不正确' })
  @ApiResponse({ status: 404, description: '未找到对应的人格类型结果' })
  async getPersonalityResultByCalculation(@Body() calculateResultDto: CalculateResultDto): Promise<PersonalityResultResponseDto> {
    return this.questionService.getPersonalityResultByCalculation(calculateResultDto);
  }

  @Post('calculate-results-with-personality')
  @ApiOperation({ summary: '计算MBTI和DISC结果并返回完整的人格测试结果信息' })
  @ApiResponse({
    status: 200,
    description: '计算成功并返回完整结果',
    type: CalculateResultWithPersonalityResponseDto,
    schema: {
      example: {
        mbti: { E: 5, I: 3, S: 4, N: 4, T: 6, F: 2, J: 3, P: 5 },
        disc: { D: 8, I: 6, S: 10, C: 8 },
        mbtiType: 'ISTJ',
        discType: 'D',
        finalType: 'ISTJ-D',
        dimensionScores: {
          '能量互动': 5,
          '信息感知': 4,
          '决策形成': 6,
          '行动实施': 5
        },
        personalityResult: {
          typeCode: 'ISTJ-D',
          title: '规则凝聚者',
          coreTraits: '以身作则推进制度，强硬把控细节中凝聚团队忠诚',
          strengths: '极高目标达成率与团队向心力并重',
          sceneMatch: '危机管理/制度落地/军官/领导',
          blindSpots: '压制异议倾向，易陷入任人唯亲，追求虚名',
          suggestions: '主动倾听反馈，建立匿名吐槽机制',
          symbol: '师',
          lifePath: '怎样从带团队到做一名游刃有余的一把手',
          valueGuide: '⚠️ 团队因压制异议分崩离析？制度落地沦为虚名？<br>💎 以身作则锻造铁军，建立匿名吐槽机制 ，制定《忠诚团队构建蓝图》'
        },
        savedToHistory: true
      }
    }
  })
  @ApiResponse({ status: 400, description: '答案数组长度不正确或题目数量不正确' })
  @ApiResponse({ status: 404, description: '未找到对应的人格类型结果' })
  async calculateResultsWithPersonality(@Body() calculateResultDto: CalculateResultDto): Promise<CalculateResultWithPersonalityResponseDto> {
    return this.questionService.calculateResultsWithPersonality(calculateResultDto);
  }

  // 用户答题记录相关接口
  @Post('user-answer-records')
  @ApiOperation({ summary: '保存用户答题记录' })
  @ApiResponse({ status: 201, description: '保存成功', type: UserAnswerRecordResponseDto })
  @ApiResponse({ status: 400, description: '答案数组长度不正确或题目数量不正确' })
  async saveUserAnswerRecord(@Body() createUserAnswerRecordDto: CreateUserAnswerRecordDto): Promise<UserAnswerRecordResponseDto> {
    return this.questionService.saveUserAnswerRecord(createUserAnswerRecordDto);
  }

  @Get('user-answer-records/history')
  @ApiOperation({ summary: '获取用户的答题记录历史' })
  @ApiResponse({ status: 200, description: '获取成功', type: PaginatedUserAnswerRecordsResponseDto })
  @ApiQuery({ name: 'userId', required: true, type: Number, description: '用户ID' })
  @ApiQuery({ name: 'page', required: false, type: Number, description: '页码，默认为1' })
  @ApiQuery({ name: 'pageSize', required: false, type: Number, description: '每页数量，默认为10，最大100' })
  async getUserAnswerRecords(@Query() query: GetUserAnswerRecordsDto): Promise<PaginatedUserAnswerRecordsResponseDto> {
    return this.questionService.getUserAnswerRecords(query);
  }

  @Post('user-answer-records/current-type')
  @ApiOperation({ summary: '获取用户的当前人格类型' })
  @ApiResponse({ status: 200, description: '获取成功', type: UserCurrentTypeResponseDto })
  @ApiResponse({ status: 404, description: '用户暂无答题记录' })
  async getUserCurrentType(@Body() getUserCurrentTypeDto: GetUserCurrentTypeDto): Promise<UserCurrentTypeResponseDto> {
    return this.questionService.getUserCurrentType(getUserCurrentTypeDto);
  }

  @Get('user-answer-records/proto/:userId')
  @ApiOperation({ summary: '获取用户的proto类型（当前类型）' })
  @ApiResponse({ status: 200, description: '获取成功', schema: { example: { proto: 'ISTJ-D' } } })
  @ApiResponse({ status: 404, description: '用户暂无答题记录' })
  async getUserProtoType(@Param('userId') userId: number): Promise<{ proto: string }> {
    return this.questionService.getUserProtoType(userId);
  }
}
