import { Module } from "@nestjs/common";
import { ConfigModule, ConfigService } from "@nestjs/config";
import { RedisModule as NestRedisModule } from "@nestjs-modules/ioredis";
import { RedisService } from "./redis.service";
@Module({
  imports: [
    NestRedisModule.forRootAsync({
      imports: [ConfigModule],
      useFactory: (configService: ConfigService) => ({
        type: "single",
        url: `redis://${configService.get("redis.host")}:${configService.get("redis.port")}`,
        options: {
          host: configService.get("redis.host"),
          port: configService.get("redis.port"),
          password: configService.get("redis.password"),
          db: configService.get("redis.db"),
          retryDelayOnFailover: configService.get("redis.retryDelayOnFailover"),
          retryAttempts: configService.get("redis.retryAttempts"),
          maxRetriesPerRequest: configService.get("redis.maxRetriesPerRequest"),
          lazyConnect: configService.get("redis.lazyConnect"),
          connectTimeout: configService.get("redis.connectTimeout"),
          commandTimeout: configService.get("redis.commandTimeout"),
          family: configService.get("redis.family"),
          keepAlive: configService.get("redis.keepAlive"),
        },
      }),
      inject: [ConfigService],
    }),
  ],
  providers: [RedisService],
  exports: [RedisService],
})
export class RedisModule {}
