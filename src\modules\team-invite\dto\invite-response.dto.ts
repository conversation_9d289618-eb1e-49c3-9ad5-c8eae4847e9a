import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { InviteStatus, InviteType } from '../entities/team-invite.entity';

export class InviteResponseDto {
  @ApiProperty({ description: '邀请记录ID' })
  id: number;

  @ApiProperty({ description: '邀请码' })
  inviteCode: string;

  @ApiProperty({ description: '邀请人ID' })
  inviterId: number;

  @ApiProperty({ description: '邀请人姓名' })
  inviterName: string;

  @ApiPropertyOptional({ description: '被邀请人ID' })
  inviteeId?: number;

  @ApiPropertyOptional({ description: '被邀请人姓名' })
  inviteeName?: string;

  @ApiProperty({ enum: InviteStatus, description: '邀请状态' })
  status: InviteStatus;

  @ApiProperty({ enum: InviteType, description: '邀请类型' })
  type: InviteType;

  @ApiPropertyOptional({ description: '邀请链接' })
  inviteLink?: string;

  @ApiPropertyOptional({ description: '过期时间' })
  expiresAt?: Date;

  @ApiPropertyOptional({ description: '接受时间' })
  acceptedAt?: Date;

  @ApiPropertyOptional({ description: '备注' })
  note?: string;

  @ApiProperty({ description: '创建时间' })
  createdAt: Date;

  @ApiProperty({ description: '更新时间' })
  updatedAt: Date;
}

export class InviteStatsDto {
  @ApiProperty({ description: '总邀请数' })
  totalInvites: number;

  @ApiProperty({ description: '成功邀请数' })
  successfulInvites: number;

  @ApiProperty({ description: '待接受邀请数' })
  pendingInvites: number;

  @ApiProperty({ description: '已过期邀请数' })
  expiredInvites: number;

  @ApiProperty({ description: '邀请成功率（百分比）' })
  successRate: number;
}

export class MyInviteInfoDto {
  @ApiProperty({ description: '我的邀请码' })
  myInviteCode: string;

  @ApiProperty({ description: '邀请人数' })
  inviteCount: number;

  @ApiProperty({ description: '邀请统计' })
  stats: InviteStatsDto;

  @ApiProperty({ description: '是否可以邀请他人' })
  canInviteOthers: boolean;

  @ApiProperty({ description: '我发送的邀请列表' })
  sentInvites: InviteResponseDto[];
}

export class InvitedUserDto {
  @ApiProperty({ description: '用户ID' })
  id: number;

  @ApiProperty({ description: '用户名' })
  name: string;

  @ApiProperty({ description: '邮箱' }) 
  email: string | null;

  @ApiProperty({ description: '手机号' })
  phoneNumber: string | null;

  @ApiProperty({ description: '加入时间' })
  joinedAt: Date;

  @ApiProperty({ description: '权限级别' })
  permissionLevel: string;

  @ApiProperty({ description: '用户类型' })
  userType: string;

  @ApiProperty({ description: '会员类型' })
  membershipType: string;
}

export class MyInvitedUsersDto {
  @ApiProperty({ description: '我的邀请码' })
  myInviteCode: string;

  @ApiProperty({ description: '总邀请人数' })
  totalCount: number;

  @ApiProperty({ description: '使用我邀请码的用户列表' })
  invitedUsers: InvitedUserDto[];

  @ApiProperty({ description: '统计信息' })
  summary: {
    totalUsers: number;
    activeUsers: number;
    teamUsers: number;
    individualUsers: number;
  };
} 