#!/usr/bin/env node

const { execSync } = require("child_process");
const fs = require("fs");
const path = require("path");

console.log("🚀 正在设置Git提交规范和版本管理...\n");

function runCommand(command, description) {
  try {
    console.log(`📦 ${description}...`);
    execSync(command, { stdio: "pipe" });
    console.log(`✅ ${description} 完成\n`);
  } catch (error) {
    console.log(`❌ ${description} 失败: ${error.message}\n`);
    return false;
  }
  return true;
}

function createDirectory(dir) {
  if (!fs.existsSync(dir)) {
    fs.mkdirSync(dir, { recursive: true });
    console.log(`📁 创建目录: ${dir}`);
  }
}

// 1. 安装依赖
const success1 = runCommand(
  "npm install --save-dev @commitlint/cli @commitlint/config-conventional husky commitizen cz-conventional-changelog standard-version",
  "安装Git工具依赖包"
);

if (!success1) {
  console.log("❌ 依赖安装失败，请手动运行: npm install");
  process.exit(1);
}

// 2. 初始化husky
runCommand("npm run prepare", "初始化Husky Git钩子");

// 3. 创建.husky目录（如果不存在）
createDirectory(".husky");

// 4. 添加commit-msg钩子
runCommand(
  'npx husky add .husky/commit-msg "npx --no-install commitlint --edit $1"',
  "添加提交信息验证钩子"
);

// 5. 设置Git提交模板
runCommand("git config commit.template .gitmessage", "设置Git提交模板");

// 6. 创建版本配置文件（如果不存在）
const versionrcPath = ".versionrc.json";
if (!fs.existsSync(versionrcPath)) {
  const versionrcConfig = {
    types: [
      { type: "feat", section: "Features" },
      { type: "fix", section: "Bug Fixes" },
      { type: "chore", hidden: true },
      { type: "docs", section: "Documentation" },
      { type: "style", hidden: true },
      { type: "refactor", section: "Code Refactoring" },
      { type: "perf", section: "Performance Improvements" },
      { type: "test", hidden: true },
    ],
    commitUrlFormat: "{{host}}/{{owner}}/{{repository}}/commit/{{hash}}",
    compareUrlFormat:
      "{{host}}/{{owner}}/{{repository}}/compare/{{previousTag}}...{{currentTag}}",
  };

  fs.writeFileSync(versionrcPath, JSON.stringify(versionrcConfig, null, 2));
  console.log("📝 创建版本配置文件 (.versionrc.json)");
}

console.log("🎉 Git提交规范设置完成！\n");
console.log("📋 下一步操作:");
console.log('1. 使用 "npm run commit" 进行规范提交');
console.log('2. 使用 "npm run release" 自动生成版本和CHANGELOG');
console.log("3. 查看 GIT_SETUP_GUIDE.md 了解详细使用方法");
console.log("4. 查看 COMMIT_CONVENTION.md 了解完整规范\n");

// 显示示例
console.log("💡 提交示例:");
console.log("git add .");
console.log("npm run commit");
console.log("# 或");
console.log('git commit -m "feat(api): 添加用户登录接口"\n');

console.log("🚀 开始享受规范化的Git工作流吧！");
