import { Module } from '@nestjs/common';
import { ConfigModule } from '@nestjs/config';
import { TypeOrmModule } from '@nestjs/typeorm';
import { PaymentService } from './payment.service';
import { PaymentController } from './payment.controller';
import { Payment } from './entities/payment.entity';
import { PaymentRepository } from '../../repositories/payment.repository';
import { RepositoriesModule } from '../../repositories/repositories.module';
import { PaymentStatusService } from './services/payment-status.service';
import { PaymentValidationService } from './services/payment-validation.service';
import { PaymentErrorService } from './services/payment-error.service';

@Module({
  imports: [
    ConfigModule,
    TypeOrmModule.forFeature([Payment]),
    RepositoriesModule,
  ],
  controllers: [PaymentController],
  providers: [
    PaymentService,
    PaymentRepository,
    PaymentStatusService,
    PaymentValidationService,
    PaymentErrorService
  ],
  exports: [
    PaymentService,
    PaymentRepository,
    PaymentStatusService,
    PaymentValidationService,
    PaymentErrorService
  ],
})
export class PaymentModule {}