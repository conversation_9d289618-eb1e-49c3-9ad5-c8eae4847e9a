# <type>(<scope>): <subject>
# |<----  Using a Maximum Of 50 Characters  ---->|

# Explain why this change is being made
# |<----   Try To Limit Each Line to a Maximum Of 72 Characters   ---->|

# Provide links or keys to any relevant tickets, issues or other resources
# Example: Closes #1234 or fixes #1234

# --- COMMIT END ---
# Type可以是:
#    feat     (新功能)
#    fix      (修复bug)
#    docs     (文档变更)
#    style    (代码格式，不影响功能)
#    refactor (代码重构)
#    perf     (性能优化)
#    test     (增加测试)
#    chore    (构建过程或辅助工具的变动)
#    revert   (回退)
#    build    (打包构建)
#    ci       (CI配置)
# --------------------
# 记住:
#    使用祈使句式: "修改"而不是"修改了"
#    首字母不要大写
#    结尾不要加句号(.) 