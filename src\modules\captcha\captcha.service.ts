import { Injectable, BadRequestException, Logger } from "@nestjs/common";
import { ConfigService } from "@nestjs/config";
import { RedisService } from "../redis/redis.service";
import * as tencentcloud from "tencentcloud-sdk-nodejs";
const SmsClient = tencentcloud.sms.v20210111.Client;

@Injectable()
export class CaptchaService {
  private readonly logger = new Logger(CaptchaService.name);

  // 验证码有效期（秒）
  private readonly CAPTCHA_TTL = 5 * 60; // 5分钟
  // 发送频率限制（秒）
  private readonly RATE_LIMIT_TTL = 60; // 1分钟

  private readonly smsClient: any;

  constructor(
    private readonly redisService: RedisService,
    private readonly configService: ConfigService,
  ) {
    // 初始化腾讯云短信客户端
    const smsConfig = this.configService.get("sms.tencent");

    if (!smsConfig.secretId || !smsConfig.secretKey) {
      this.logger.warn("腾讯云短信配置不完整，将使用模拟发送");
      this.smsClient = null;
    } else {
      const clientConfig = {
        credential: {
          secretId: smsConfig.secretId,
          secretKey: smsConfig.secretKey,
        },
        region: smsConfig.region,
        profile: {
          httpProfile: {
            endpoint: smsConfig.endpoint,
          },
        },
      };
      this.smsClient = new SmsClient(clientConfig);
    }
  }

  // 生成6位数字验证码
  generateCode(): string {
    return Math.floor(100000 + Math.random() * 900000).toString();
  }

  // 发送短信验证码
  private async sendSmsCode(phoneNumber: string, code: string): Promise<void> {
    if (!this.smsClient) {
      // 如果没有配置短信服务，使用模拟发送
      this.logger.warn(`模拟发送短信验证码到 ${phoneNumber}: ${code}`);
      return;
    }

    try {
      const smsConfig = this.configService.get("sms.tencent");

      const params = {
        PhoneNumberSet: [phoneNumber],
        SmsSdkAppId: smsConfig.appId,
        TemplateId: smsConfig.templateId,
        SignName: smsConfig.signName,
        TemplateParamSet: [code, "5"], // 验证码和有效期（5分钟）
        ExtendCode: "",
        SessionContext: "",
        SenderId: "",
      };

      const response = await this.smsClient.SendSms(params);

      if (response.SendStatusSet && response.SendStatusSet.length > 0) { 
        const sendStatus = response.SendStatusSet[0];
        if (sendStatus.Code === "Ok") {
          this.logger.log(`短信发送成功: ${phoneNumber}`);
        } else {
          this.logger.error(`短信发送失败: ${sendStatus.Code} - ${sendStatus.Message}`);
          throw new BadRequestException(`短信发送失败: ${sendStatus.Message}`);
        }
      }
    } catch (error) {
      this.logger.error(`短信发送异常: ${error.message}`, error.stack);
      throw new BadRequestException("短信发送失败，请稍后重试");
    }
  }

  // 发送验证码
  async sendCaptcha(phoneNumber: string) {
    // 1. 检查发送频率限制
    const isRateLimited = await this.redisService.isRateLimited(phoneNumber);
    if (isRateLimited) {
      throw new BadRequestException("发送过于频繁，请稍后再试");
    }

    // 2. 生成验证码
    const code = this.generateCode();

    // 3. 存储验证码到Redis，设置5分钟过期
    await this.redisService.setCaptcha(phoneNumber, code, this.CAPTCHA_TTL);

    // 4. 设置发送频率限制（1分钟内不能重复发送）
    await this.redisService.setRateLimit(phoneNumber, this.RATE_LIMIT_TTL);

    // 5. 调用腾讯云短信服务发送验证码
    await this.sendSmsCode(phoneNumber, code);

    return {
      phoneNumber,
      message: "验证码已发送，请注意查收",
      expiresIn: this.CAPTCHA_TTL, // 返回过期时间供前端使用
    };
  }

  // 验证验证码
  async verifyCaptcha(phoneNumber: string, code: string): Promise<boolean> {
    // 1. 从Redis获取验证码
    const storedCode = await this.redisService.getCaptcha(phoneNumber);

    if (!storedCode) {
      throw new BadRequestException("验证码无效或已过期");
    }

    // 2. 验证验证码是否匹配
    if (storedCode !== code) {
      throw new BadRequestException("验证码错误");
    }

    // 3. 验证成功后删除验证码（防止重复使用）
    await this.redisService.deleteCaptcha(phoneNumber);

    return true;
  }

  // 检查验证码是否存在（用于调试或其他需求）
  async checkCaptchaExists(phoneNumber: string): Promise<boolean> {
    return await this.redisService.captchaExists(phoneNumber);
  }

  // 获取验证码剩余有效时间
  async getCaptchaRemainingTime(phoneNumber: string): Promise<number> {
    return await this.redisService.getCaptchaTTL(phoneNumber);
  }

  // 清除验证码（管理员功能或特殊情况）
  async clearCaptcha(phoneNumber: string): Promise<void> {
    await this.redisService.deleteCaptcha(phoneNumber);
  }

  // 检查是否在发送频率限制内
  async checkRateLimit(phoneNumber: string): Promise<{
    isLimited: boolean;
    remainingTime?: number;
  }> {
    const isLimited = await this.redisService.isRateLimited(phoneNumber);

    if (isLimited) {
      // 如果被限制，获取剩余限制时间
      const key = `rate_limit:${phoneNumber}`;
      const remainingTime = await this.redisService.ttl(key);
      return {
        isLimited: true,
        remainingTime: remainingTime > 0 ? remainingTime : 0,
      };
    }

    return { isLimited: false };
  }
}
