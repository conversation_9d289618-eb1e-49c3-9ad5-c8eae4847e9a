import { Modu<PERSON> } from "@nestjs/common";
import { TypeOrmModule } from "@nestjs/typeorm";
import { UsersService } from "./users.service";
import { UsersController } from "./users.controller";
import { JwtTestController } from "./jwt-test.controller";
import { CaptchaModule } from "../captcha/captcha.module";
import { RedisModule } from "../redis/redis.module";
import { RepositoriesModule } from "../../repositories/repositories.module";
import { GuardsModule } from "../../guards/guards.module";
import { User } from "./entities/user.entity";
import { JwtModule } from "@nestjs/jwt";
import { ConfigModule, ConfigService } from "@nestjs/config";

@Module({
  imports: [
    TypeOrmModule.forFeature([User]),
    CaptchaModule,
    RedisModule,
    RepositoriesModule,
    GuardsModule,
    ConfigModule,
  ],
  controllers: [UsersController, JwtTestController],
  providers: [UsersService],
  exports: [UsersService],
})
export class UsersModule {}
