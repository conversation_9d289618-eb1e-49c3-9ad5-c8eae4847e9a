import { PartialType } from "@nestjs/mapped-types";
import { CreateUserDto } from "./create-user.dto";
import { ApiProperty } from "@nestjs/swagger";
import { IsOptional, IsEnum, IsDateString, IsString, IsBoolean } from "class-validator";
import { CalendarType, MembershipType } from "../entities/user.entity";

export class UpdateUserDto extends PartialType(CreateUserDto) {
  @ApiProperty({
    enum: MembershipType,
    description: "会员类型（可选）",
    required: false
  })
  @IsOptional()
  @IsEnum(MembershipType, { message: "会员类型格式不正确" })
  membershipType?: MembershipType;

  @ApiProperty({
    description: "会员过期时间（可选）",
    type: String,
    format: "date-time",
    required: false
  })
  @IsOptional()
  @IsDateString({}, { message: "会员过期时间格式不正确" })
  membershipExpireDate?: string;

  @ApiProperty({ description: "是否拥有团队权限（可选）", required: false })
  @IsOptional()
  @IsBoolean()
  hasTeamPermission?: boolean;

  @ApiProperty({ description: "是否激活账号（可选）", required: false })
  @IsOptional()
  @IsBoolean()
  isActive?: boolean;

  @ApiProperty({ description: "是否锁定出生日期（可选）", required: false })
  @IsOptional()
  @IsBoolean()
  birthDateLocked?: boolean;

  // 添加一个字段用于用户的生日是公历还是阴历
  @ApiProperty({ description: "生日是公历还是阴历（可选）", required: false })
  @IsOptional()
  @IsEnum(CalendarType, { message: "生日类型格式不正确" })
  birthCalendarType?: CalendarType;
}
