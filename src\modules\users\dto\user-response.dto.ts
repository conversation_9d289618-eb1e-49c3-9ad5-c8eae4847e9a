import { ApiProperty } from "@nestjs/swagger";
import { Role, UserType, Gender, MembershipType } from "../entities/user.entity";

export class UserResponseDto {
  @ApiProperty({ description: '用户唯一标识' })
  id: number;

  @ApiProperty({ description: '用户名（昵称）' })
  name: string;

  @ApiProperty({ description: '用户邮箱（唯一）' })
  email: string;

  @ApiProperty({ enum: Role, description: '用户角色' })
  role: Role;

  @ApiProperty({ enum: UserType, description: '用户类型：个人或机构' })
  userType: UserType;

  @ApiProperty({ description: '真实姓名（可选）', required: false })
  fullName?: string;

  @ApiProperty({ description: '手机号（唯一）' })
  phoneNumber: string;

  @ApiProperty({ description: '地址（可选）', required: false })
  address?: string;

  @ApiProperty({ enum: Gender, description: '性别（可选）', required: false })
  gender?: Gender;

  @ApiProperty({ description: '出生日期（可选）', type: String, format: 'date', required: false })
  birthDate?: Date;

  @ApiProperty({ description: '账号是否激活' })
  isActive: boolean;

  @ApiProperty({ description: '头像链接（可选）', required: false })
  profilePicture?: string;

  @ApiProperty({ description: '背景图链接（可选）', required: false })
  profileBackgroundPicture?: string;

  @ApiProperty({ enum: MembershipType, description: '会员类型' })
  membershipType: MembershipType;

  @ApiProperty({ description: '会员过期时间（可选）', type: String, format: 'date-time', required: false })
  membershipExpireDate?: Date;

  @ApiProperty({ description: '是否拥有团队权限' })
  hasTeamPermission: boolean;

  @ApiProperty({ description: '免费评估次数' })
  freeAssessmentCount: number;

  @ApiProperty({ description: '免费 AI 解读次数' })
  freeAIInterpretationCount: number;

  @ApiProperty({ description: '总评估次数' })
  totalAssessmentCount: number;

  @ApiProperty({ description: '总 AI 解读次数' })
  totalAIInterpretationCount: number;

  @ApiProperty({ description: '上次登录时间', type: String, format: 'date-time', required: false })
  lastLoginAt?: Date;

  @ApiProperty({ description: '登录次数' })
  loginCount: number;

  @ApiProperty({ description: '是否锁定出生日期' })
  birthDateLocked: boolean;

  @ApiProperty({ description: '创建时间', type: String, format: 'date-time' })
  createdAt: Date;

  @ApiProperty({ description: '更新时间', type: String, format: 'date-time' })
  updatedAt: Date;
} 