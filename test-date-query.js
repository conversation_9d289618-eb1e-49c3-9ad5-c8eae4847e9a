const axios = require('axios');

const BASE_URL = 'http://localhost:3000';

async function testDateQuery() {
  try {
    console.log('🚀 开始测试日期查询功能...\n');

    // 1. 初始化数据
    console.log('1. 初始化数据...');
    const initResponse = await axios.post(`${BASE_URL}/date-query/initialize`);
    console.log('✅ 初始化结果:', initResponse.data);
    console.log('');

    // 2. 测试原型接口 - 普通日期
    console.log('2. 测试原型接口 - 普通日期 (2024年1月15日)...');
    const query1Response = await axios.post(`${BASE_URL}/date-query/prototype`, {
      year: 2024,
      month: 1,
      day: 15
    });
    console.log('✅ 查询结果:', query1Response.data);
    console.log('');

    // 3. 测试原型接口 - 闰年特殊规则
    console.log('3. 测试原型接口 - 闰年特殊规则 (2024年3月21日)...');
    const query2Response = await axios.post(`${BASE_URL}/date-query/prototype`, {
      year: 2024,
      month: 3,
      day: 21
    });
    console.log('✅ 查询结果:', query2Response.data);
    console.log('');

    // 4. 测试原型接口 - 平年特殊规则
    console.log('4. 测试原型接口 - 平年特殊规则 (2023年6月20日)...');
    const query3Response = await axios.post(`${BASE_URL}/date-query/prototype`, {
      year: 2023,
      month: 6,
      day: 20
    });
    console.log('✅ 查询结果:', query3Response.data);
    console.log('');

    // 5. 测试原型接口 - 闰年特殊规则
    console.log('5. 测试原型接口 - 闰年特殊规则 (2024年6月20日)...');
    const query4Response = await axios.post(`${BASE_URL}/date-query/prototype`, {
      year: 2024,
      month: 6,
      day: 20
    });
    console.log('✅ 查询结果:', query4Response.data);
    console.log('');

    // 6. 获取所有数据
    console.log('6. 获取所有数据...');
    const allDataResponse = await axios.get(`${BASE_URL}/date-query/all`);
    console.log(`✅ 获取到 ${allDataResponse.data.data.length} 条数据`);
    console.log('');

    console.log('🎉 所有测试完成！');

  } catch (error) {
    console.error('❌ 测试失败:', error.response?.data || error.message);
  }
}

// 运行测试
testDateQuery(); 