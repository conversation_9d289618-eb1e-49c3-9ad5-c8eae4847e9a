import { IsOptional, IsEnum, IsDateString, IsInt, Min, Max, IsString } from 'class-validator';
import { ApiPropertyOptional } from '@nestjs/swagger';
import { Transform } from 'class-transformer';

export class StatsQueryDto {
  @ApiPropertyOptional({ 
    description: '时间范围', 
    enum: ['7d', '30d', '90d', 'custom'],
    default: '30d'
  })
  @IsOptional()
  @IsEnum(['7d', '30d', '90d', 'custom'])
  timeRange?: string = '30d';

  @ApiPropertyOptional({ description: '开始日期（自定义时间范围时使用）' })
  @IsOptional()
  @IsDateString()
  startDate?: string;

  @ApiPropertyOptional({ description: '结束日期（自定义时间范围时使用）' })
  @IsOptional()
  @IsDateString()
  endDate?: string;

  @ApiPropertyOptional({ 
    description: '排序方式', 
    enum: ['usage', 'lastActive', 'joinDate'],
    default: 'usage'
  })
  @IsOptional()
  @IsEnum(['usage', 'lastActive', 'joinDate'])
  sortBy?: string = 'usage';

  @ApiPropertyOptional({ description: '限制返回数量', minimum: 1, maximum: 100 })
  @IsOptional()
  @IsInt()
  @Min(1)
  @Max(100)
  @Transform(({ value }) => parseInt(value))
  limit?: number = 10;
}

export class TrendQueryDto {
  @ApiPropertyOptional({ 
    description: '时间范围', 
    enum: ['7d', '30d', '90d'],
    default: '30d'
  })
  @IsOptional()
  @IsEnum(['7d', '30d', '90d'])
  timeRange?: string = '30d';

  @ApiPropertyOptional({ description: '图表类型', enum: ['line', 'bar'] })
  @IsOptional()
  @IsEnum(['line', 'bar'])
  chartType?: string = 'line';
}

export class RankingQueryDto {
  @ApiPropertyOptional({ description: '限制返回数量', minimum: 1, maximum: 50 })
  @IsOptional()
  @IsInt()
  @Min(1)
  @Max(50)
  @Transform(({ value }) => parseInt(value))
  limit?: number = 10;

  @ApiPropertyOptional({ 
    description: '排序方式', 
    enum: ['usage', 'lastActive', 'joinDate'],
    default: 'usage'
  })
  @IsOptional()
  @IsEnum(['usage', 'lastActive', 'joinDate'])
  sortBy?: string = 'usage';
}

export class ActivityQueryDto {
  @ApiPropertyOptional({ 
    description: '时间范围', 
    enum: ['30d', '90d'],
    default: '30d'
  })
  @IsOptional()
  @IsEnum(['30d', '90d'])
  timeRange?: string = '30d';
}

export class FeatureQueryDto {
  @ApiPropertyOptional({ 
    description: '时间范围', 
    enum: ['7d', '30d', '90d'],
    default: '30d'
  })
  @IsOptional()
  @IsEnum(['7d', '30d', '90d'])
  timeRange?: string = '30d';
}

export class ExportQueryDto {
  @ApiPropertyOptional({ description: '导出格式', enum: ['excel', 'pdf'] })
  @IsOptional()
  @IsEnum(['excel', 'pdf'])
  format?: string = 'excel';

  @ApiPropertyOptional({ 
    description: '时间范围', 
    enum: ['7d', '30d', '90d', 'custom'],
    default: '30d'
  })
  @IsOptional()
  @IsEnum(['7d', '30d', '90d', 'custom'])
  timeRange?: string = '30d';
} 