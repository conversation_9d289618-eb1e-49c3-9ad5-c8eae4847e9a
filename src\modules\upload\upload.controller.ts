import { Controller, Post, UseInterceptors, UploadedFile, Body } from '@nestjs/common';
import { FileInterceptor } from '@nestjs/platform-express';
import { UploadService } from './upload.service';
import { CreateUploadDto } from './dto/create-upload.dto';
import { ApiOperation, ApiTags, ApiConsumes, ApiBody, ApiResponse } from '@nestjs/swagger';

@ApiTags('上传')
@Controller('uploads')
export class UploadController {
  constructor(private readonly uploadService: UploadService) { }

  @Post('upload')
  @ApiOperation({ summary: '上传文件' })
  @ApiConsumes('multipart/form-data')
  @ApiBody({
    schema: {
      type: 'object',
      properties: {
        file: {
          type: 'string',
          format: 'binary',
          description: '要上传的文件'
        }
      }
    }
  })
  @ApiResponse({ status: 200, description: '上传成功' })
  @ApiResponse({ status: 400, description: '上传失败' })
  @UseInterceptors(FileInterceptor('file'))
  async uploadFile(@UploadedFile() file: Express.Multer.File, @Body() createUploadDto: CreateUploadDto) {
    try { 
      const fileInfo = await this.uploadService.uploadFile(file);
      const uploadRecord = await this.uploadService.create(createUploadDto, file);
      return { message: 'Upload successful', data: fileInfo };
    } catch (error) {
      return { message: 'Upload failed', error: error.message };
    }
  }
}
