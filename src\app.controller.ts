import { Controller, Get, Header } from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse } from '@nestjs/swagger';
import { AppService } from './app.service';

@ApiTags('App')
@Controller()
export class AppController {
  constructor(private readonly appService: AppService) { }

  @Get()
  getHello(): string {
    return this.appService.getHello();
  }

  @Get("api-json")
  @Header('Content-Type', 'application/json')
  @ApiOperation({
    summary: '获取 Swagger JSON 规范',
    description: '返回完整的 OpenAPI 3.0 JSON 规范文档，不包含响应包装格式'
  })
  @ApiResponse({
    status: 200,
    description: '返回原始的 OpenAPI JSON 规范',
    schema: {
      type: 'object',
      properties: {
        openapi: { type: 'string', example: '3.0.0' },
        info: {
          type: 'object',
          properties: {
            title: { type: 'string', example: 'KanLi API' },
            version: { type: 'string', example: '1.0' }
          }
        },
        paths: { type: 'object' }
      }
    }
  })
  getSwaggerJson() {
    return this.appService.getSwaggerJson();
  }
}
 