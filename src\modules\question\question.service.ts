import { Injectable, NotFoundException, BadRequestException } from '@nestjs/common';
import { CalculateResultDto, CalculateResultResponseDto, CalculateResultWithPersonalityResponseDto } from './dto/calculate-result.dto';
import { CreatePersonalityResultDto, GetPersonalityResultDto, PersonalityResultResponseDto } from './dto/personality-result.dto';
import { CreateUserAnswerRecordDto, GetUserAnswerRecordsDto, GetUserCurrentTypeDto, UserAnswerRecordResponseDto, UserCurrentTypeResponseDto, PaginatedUserAnswerRecordsResponseDto } from './dto/user-answer-record.dto';
import { QuestionRepository } from '../../repositories/question.repository';
import { OptionRepository } from '../../repositories/option.repository';
import { PersonalityResultRepository } from '../../repositories/personality-result.repository';
import { UserAnswerRecordRepository } from '../../repositories/user-answer-record.repository';

@Injectable()
export class QuestionService {
  constructor(
    private questionRepository: QuestionRepository,
    private optionRepository: OptionRepository,
    private personalityResultRepository: PersonalityResultRepository,
    private userAnswerRecordRepository: UserAnswerRecordRepository,
  ) { }

  async findAll(): Promise<any[]> {
    return this.questionRepository.findAll();
  }

  async create(data: Partial<any>): Promise<any> {
    return this.questionRepository.create(data);
  }

  async findOne(id: number): Promise<any> {
    const question = await this.questionRepository.findById(id);
    if (!question) {
      throw new NotFoundException('问题不存在');
    }
    return question;
  }

  async update(id: number, data: Partial<any>): Promise<any> {
    await this.questionRepository.update(id, data);
    const question = await this.questionRepository.findById(id);
    if (!question) {
      throw new NotFoundException('问题不存在');
    }
    return question;
  }

  async remove(id: number): Promise<void> {
    await this.questionRepository.delete(id);
  }

  /**
   * 计算MBTI和DISC结果
   */
  async calculateResults(calculateResultDto: CalculateResultDto): Promise<CalculateResultResponseDto> {
    const { answers } = calculateResultDto;

    // 验证答案数组长度
    if (answers.length !== 32) {
      throw new BadRequestException('答案数组长度必须为32');
    }

    // 获取所有题目和选项
    const questions = await this.findAll();
    if (questions.length !== 32) {
      throw new BadRequestException('题目数量不正确，请确保已初始化题目数据');
    }

    // 初始化计数
    const mbti = { E: 0, I: 0, S: 0, N: 0, T: 0, F: 0, J: 0, P: 0 };
    const disc = { D: 0, I: 0, S: 0, C: 0 };
    const dimensionScores: { [key: string]: number } = {};

    // 统计DISC结果
    answers.forEach((answer, index) => {
      if (answer !== null && answer >= 0 && answer < 4) {
        const optionLetter = questions[index].options[answer].letter;

        switch (optionLetter) {
          case 'A': disc.D++; break;
          case 'B': disc.I++; break;
          case 'C': disc.S++; break;
          case 'D': disc.C++; break;
        }
      }
    });

    // 统计MBTI维度
    // E/I维度 (1-8题) - 能量互动
    for (let i = 0; i < 8; i++) {
      if (answers[i] !== null && answers[i] >= 0 && answers[i] < 4) {
        const optionLetter = questions[i].options[answers[i]].letter;
        if (optionLetter === 'A' || optionLetter === 'B') {
          mbti.E++;
        } else {
          mbti.I++;
        }
      }
    }

    // S/N维度 (9-16题) - 信息感知
    for (let i = 8; i < 16; i++) {
      if (answers[i] !== null && answers[i] >= 0 && answers[i] < 4) {
        const optionLetter = questions[i].options[answers[i]].letter;
        if (optionLetter === 'A' || optionLetter === 'B') {
          mbti.S++;
        } else {
          mbti.N++;
        }
      }
    }

    // T/F维度 (17-24题) - 决策形成
    for (let i = 16; i < 24; i++) {
      if (answers[i] !== null && answers[i] >= 0 && answers[i] < 4) {
        const optionLetter = questions[i].options[answers[i]].letter;
        if (optionLetter === 'A' || optionLetter === 'B') {
          mbti.T++;
        } else {
          mbti.F++;
        }
      }
    }

    // J/P维度 (25-32题) - 行动实施
    for (let i = 24; i < 32; i++) {
      if (answers[i] !== null && answers[i] >= 0 && answers[i] < 4) {
        const optionLetter = questions[i].options[answers[i]].letter;
        if (optionLetter === 'A' || optionLetter === 'B') {
          mbti.J++;
        } else {
          mbti.P++;
        }
      }
    }

    // 确定MBTI类型
    const mbtiType =
      (mbti.E >= mbti.I ? 'E' : 'I') +
      (mbti.S >= mbti.N ? 'S' : 'N') +
      (mbti.T >= mbti.F ? 'T' : 'F') +
      (mbti.J >= mbti.P ? 'J' : 'P');

    // 确定DISC主导风格
    let discType = 'D';
    let maxCount = disc.D;

    if (disc.I > maxCount) {
      discType = 'I';
      maxCount = disc.I;
    }
    if (disc.S > maxCount) {
      discType = 'S';
      maxCount = disc.S;
    }
    if (disc.C > maxCount) {
      discType = 'C';
    }

    // 计算各维度得分
    dimensionScores['能量互动'] = Math.max(mbti.E, mbti.I);
    dimensionScores['信息感知'] = Math.max(mbti.S, mbti.N);
    dimensionScores['决策形成'] = Math.max(mbti.T, mbti.F);
    dimensionScores['行动实施'] = Math.max(mbti.J, mbti.P);

    // 组合结果
    const finalType = `${mbtiType}-${discType}`;

    return {
      mbti,
      disc,
      mbtiType,
      discType,
      finalType,
      dimensionScores
    };
  }

  /**
   * 创建人格测试结果
   */
  async createPersonalityResult(createPersonalityResultDto: CreatePersonalityResultDto): Promise<any> {
    const existingResult = await this.personalityResultRepository.findByTypeCode(createPersonalityResultDto.typeCode);

    if (existingResult) {
      throw new BadRequestException('该人格类型已存在');
    }

    return this.personalityResultRepository.create(createPersonalityResultDto);
  }

  /**
   * 根据类型代码获取人格测试结果
   */
  async getPersonalityResult(getPersonalityResultDto: GetPersonalityResultDto): Promise<PersonalityResultResponseDto> {
    const result = await this.personalityResultRepository.findByTypeCode(getPersonalityResultDto.typeCode);

    if (!result) {
      throw new NotFoundException('未找到该人格类型的结果');
    }

    return result;
  }

  /**
   * 获取所有人格测试结果
   */
  async getAllPersonalityResults(): Promise<PersonalityResultResponseDto[]> {
    return this.personalityResultRepository.findAll();
  }

  /**
   * 批量创建人格测试结果
   */
  async seedPersonalityResults(): Promise<void> {
    const resultTypes = {
      "ISTJ-D": { title: "规则凝聚者", coreTraits: "以身作则推进制度，强硬把控细节中凝聚团队忠诚", strengths: "极高目标达成率与团队向心力并重", sceneMatch: "危机管理/制度落地/军官/领导", blindSpots: "压制异议倾向，易陷入任人唯亲，追求虚名", suggestions: "主动倾听反馈，建立匿名吐槽机制", symbol: "师", lifePath: "怎样从带团队到做一名游刃有余的一把手", valueGuide: "⚠️ 团队因压制异议分崩离析？制度落地沦为虚名？<br>💎 以身作则锻造铁军，建立匿名吐槽机制 ，制定《忠诚团队构建蓝图》" },
      "ISTJ-I": { title: "责任传承者", coreTraits: "通过责任传承激发共同使命，建立家庭式团队生态", strengths: "稳定领导力与情感凝聚力双优", sceneMatch: "客户服务/人力资源/行政管理", blindSpots: "过度说教导致虚假无私氛围", suggestions: "情感激励结合团队互助仪式", symbol: "家人", lifePath: "怎样从内部管理、后勤、创业到大管家的人生", valueGuide: "⚠️ 虚假无私蔓延致团队冷漠？<br>💎 家庭式生态激活共同使命，每周互助仪式 ，赠送情感凝聚力工具包" },
      "ISTJ-S": { title: "系统制动者", coreTraits: "守护传统流程中低调阻止灾难，四两拨千斤", strengths: "组织稳定性与危机制动能力兼备", sceneMatch: "技术运维/长期系统维护/咨询顾问", blindSpots: "抗拒变革且易小题大做", suggestions: "渐进创新配合三级响应机制", symbol: "艮", lifePath: "怎样从技术人员、咨询、创业到科学家、富豪的人生", valueGuide: "⚠️ 技术维稳遇变革冲击？小题大做制造混乱？<br>💎 四两拨千斤守护传统， 渐进创新+三级响应 ，制定《系统抗衰指南》" },
      "ISTJ-C": { title: "精密止损官", coreTraits: "数据驱动决策终止错误发展，实现零风险容错", strengths: "趋零错误率与系统崩溃预防力", sceneMatch: "高精尖技术/审计风控", blindSpots: "分析瘫痪伴随人才误判", suggestions: "决策时限机制结合数据验证法", symbol: "节", lifePath: "怎样从高精尖、文化的打工、创业到专家教授的人生", valueGuide: "⚠️ 分析瘫痪致人才误判？<br>💎 数据驱动实现零风险容错， 决策时限机制，建立《容错率测算表》" },
      "ISFJ-D": { title: "痛点勘探师", coreTraits: "强硬维护秩序时精准发现症结，提出根治方案", strengths: "高效服务与系统疗愈力双重优势", sceneMatch: "社区治理/医疗管理/心理咨询", blindSpots: "操控倾向与过度计划并行", suggestions: "尊重个体自主性，设置方案验证期", symbol: "临", lifePath: "怎样从打工、事物管理、自由职业到情报家的人生", valueGuide: "⚠️ 过度计划错过根治时机？<br>💎 强硬秩序中精准揪出症结， 设两周方案验证期， 安装《症结定位雷达》" },
      "ISFJ-I": { title: "社群纽带师", coreTraits: "温暖联结群体中灵活变换形象，快速融入环境", strengths: "关系网络核心与跨界适应力融合", sceneMatch: "员工关怀/团队建设/企业运营", blindSpots: "自我牺牲导致平庸化倾向", suggestions: "建立个人边界，每月形象突破计划", symbol: "比", lifePath: "怎样从与人竞争行业的打工、创业到董事长的人生", valueGuide: "⚠️ 自我牺牲沦为平庸？<br>💎 灵活形象破圈跨界，每月形象突破计划 ， 制作《关系网络枢纽图》" },
      "ISFJ-S": { title: "传统守恒者", coreTraits: "经验式服务中保持系统稳态，应对持续挑战", strengths: "高可靠性支持与抗压能力并存", sceneMatch: "教育督导/行政支持/餐饮标准化", blindSpots: "回避冲突加固教条主义", suggestions: "需求表达训练配合流程微调", symbol: "恒", lifePath: "怎样从各种打工、公务员、创业到公司上市的人生", valueGuide: "⚠️ 教条主义引发冲突？<br>💎 经验主义筑抗压护城河， 每月微调10%流程 ， 制定《教条破除表》" },
      "ISFJ-C": { title: "潜力淬炼师", coreTraits: "预设应急预案时激发潜能，实现熟能生巧突破", strengths: "客户满意度与成长加速度结合", sceneMatch: "手工艺传承/个性化教育", blindSpots: "过度谨慎引发环境误判", suggestions: "接受可控混乱，记录微小进步", symbol: "渐", lifePath: "怎样从各种打工、工匠、创业到大师的人生", valueGuide: "⚠️ 过度谨慎错判环境？<br>💎 预设预案激发突破潜能， 记录每日微小进步 ，玩转《潜能释放沙盘》" },
      "INFJ-D": { title: "变革立新者", coreTraits: "强力推行变革时精准把握时机，改变陈旧意识", strengths: "浪潮引领力与系统革新力兼备", sceneMatch: "社会运动/组织变革/商业金融", blindSpots: "理想化专制伴随冒进行为", suggestions: "现实数据锚定，保留旧系统过渡", symbol: "革", lifePath: "怎样从经商、从政到千万身价、改革家的人生", valueGuide: "⚠️ 冒进行为毁系统过渡？<br>💎 精准把握变革临界点，保留30%旧系统 ， 建立《浪潮时机测算仪》" },
      "INFJ-I": { title: "心灵感应器", coreTraits: "深度共情引导中预判危机，超强第六感预警", strengths: "天赋挖掘与风险洞察双轨并行", sceneMatch: "教育培训/文化传播/旅游休闲", blindSpots: "情感透支诱发责任逃避", suggestions: "定期独处回能，深度追踪关键线索", symbol: "咸", lifePath: "怎样从各种打工、营销、培训到成为行业名人的人生", valueGuide: "⚠️ 情感透支致责任逃避？<br>💎 第六感预判隐形危机， 深度追踪关键线索 ，制定《风险洞察罗盘》" },
      "INFJ-S": { title: "厚积隐修者", coreTraits: "低调传播理念时长期潜伏，抓住时机全力冲刺", strengths: "长期影响力与精准爆发力融合", sceneMatch: "哲学研究/心理咨询", blindSpots: "疏离现实导致目标偏离", suggestions: "加入实践社群，设置任务计时器", symbol: "小畜", lifePath: "怎样从做科研、研究专业领域到成为哲学家的人生", valueGuide: "⚠️ 疏离现实致目标偏离？<br>💎 长期潜伏精准爆发，加入实践社群，制作《目标校准器》" },
      "INFJ-C": { title: "生态疏导师", coreTraits: "预判系统风险时隐秘疏导暗流，持续滋润体系", strengths: "可持续生态与危机化解力并重", sceneMatch: "公益组织/乡村振兴", blindSpots: "思维反刍伴随资源错配", suggestions: "行动验证构想，监控隐藏节点", symbol: "井", lifePath: "怎样从本地发展的打工、从政、创业到公益家的人生", valueGuide: "⚠️ 思维反刍致资源错配？<br>💎 隐秘疏导滋养系统，监控隐藏节点 ，制定《生态自愈图谱》" },
      "INTJ-D": { title: "秩序领导者", coreTraits: "铁腕清除障碍时破旧立新，带来蓬勃生机", strengths: "战局掌控力与系统重建力双优", sceneMatch: "商业并购/政策执行/企业改革", blindSpots: "权威迷恋削弱团队士气", suggestions: "植入共荣机制，定期效用评估", symbol: "乾", lifePath: "怎样从普通员工、中层干部到成为高官的人生", valueGuide: "⚠️ 权威迷恋削弱团队士气？<br>💎 铁腕破旧立新重生机，植入共荣机制 ，开启《士气激活密码》" },
      "INTJ-I": { title: "愿景创造者", coreTraits: "构建影响网络时兼顾实际理想，创造可持续成功", strengths: "资源整合力与双赢设计力融合", sceneMatch: "创业融资/财富管理", blindSpots: "操纵倾向导致功劳独占", suggestions: "信息透明共享，定期利他行动", symbol: "大有", lifePath: "怎样从营销、从政、创业到高官或百亿身价的人生", valueGuide: "⚠️ 操纵倾向致功劳独占？<br>💎 双赢设计创持续成功，定期利他行动 ，制定《共享财富公式》" },
      "INTJ-S": { title: "狂智平衡师", coreTraits: "独立设计系统时维持危险平衡，创造临界奇迹", strengths: "复杂系统设计与危机转化力兼备", sceneMatch: "战略投资/伦理审查", blindSpots: "孤军奋战引发精力透支", suggestions: "委托执行环节，设置休息警报", symbol: "大过", lifePath: "怎样从普通员工、中层干部到成为高官的人生", valueGuide: "⚠️ 孤军奋战致精力透支？<br>💎 危险平衡创临界奇迹，设强制休息警报，制定《能量守恒表》" },
      "INTJ-C": { title: "跨界风控师", coreTraits: "模拟系统漏洞时融合多领域，诞生突破成果", strengths: "抗崩溃能力与创新爆发力并重", sceneMatch: "航天工程/跨学科研发", blindSpots: "拖延行动伴随畏难情绪", suggestions: "最小化启动，跨界组合实验", symbol: "既济", lifePath: "怎样从普通员工、各种小老板到实现千万身价的人生", valueGuide: "⚠️ 畏难情绪阻行动启动？<br>💎 多领域融合抗崩溃，最小化启动实验，制作《跨界反应釜》" },
      "ISTP-D": { title: "实战决断者", coreTraits: "高风险挑战中把握历史机遇，闪电扫清阻碍", strengths: "危机解决与战略机遇捕捉双优", sceneMatch: "危机救援/公关传媒/创意产业", blindSpots: "鲁莽冲动强化刚愎自用", suggestions: "预判连锁反应，收集反对意见", symbol: "夬", lifePath: "怎样从设计、讲师、创业到成为创意家的人生", valueGuide: "⚠️ 刚愎自用致连锁危机？<br>💎 混沌中闪电清阻碍，收集三份反对意见，制定《决断避坑清单》" },
      "ISTP-I": { title: "灵动协作者", coreTraits: "炫技化解危机时激发团队潜能，敏锐适应主导", strengths: "即兴发挥与团队激活力融合", sceneMatch: "极限运动/文艺表演", blindSpots: "责任感缺失引发领导错位", suggestions: "承担长期任务，咨询前辈智慧", symbol: "随", lifePath: "怎样从文艺、表演、各种销售到大师的人生", valueGuide: "⚠️ 责任感缺失致领导错位？<br>💎 炫技化解危机激活力，咨询三位前辈，制定《即兴控场秘籍》" },
      "ISTP-S": { title: "技术改造家", coreTraits: "规避人际纠纷时理论落地，遵守规律听取建议", strengths: "设备可靠性与实操智慧并存", sceneMatch: "AI开发/技术研发/精密制造", blindSpots: "存在感薄弱加剧冒险倾向", suggestions: "主动知识分享，设置计划止损点", symbol: "履", lifePath: "怎样成就自己从秘书到探险家的人生", valueGuide: "⚠️ 存在感薄弱加剧冒险？<br>💎 理论落地守设备可靠，设置计划止损点 ，制作《冒险系数测算仪》" },
      "ISTP-C": { title: "障碍清道夫", coreTraits: "拆解系统时闪电打通节点，优化微观效率", strengths: "故障诊断与系统疏通力兼备", sceneMatch: "灾害防治/司法鉴定/质量检测", blindSpots: "局部优化忽视宏观目标", suggestions: "关联战略方向，预留重建周期", symbol: "噬嗑", lifePath: "怎样从公检法、互联网工作到高官厚禄、企业家的人生", valueGuide: "⚠️ 局部优化忽视宏观目标？<br>💎 闪电疏通系统节点，预留重建周期 ，建立《效能倍增路线图》" },
      "ISFP-D": { title: "价值抗辩者", coreTraits: "抗争世俗规则时周全避免冲突，用外交化解争斗", strengths: "道德勇气与冲突化解力融合", sceneMatch: "先锋艺术/网络监管/公检法", blindSpots: "极端化倾向导致得理不饶人", suggestions: "理性评估代价，复述对方观点", symbol: "讼", lifePath: "怎样从谈判业务、发挥口才到走上法院院长的人生", valueGuide: "⚠️ 极端倾向致得理不饶人？<br>💎 外交智慧化冲突，复述对方观点，制作《道德勇气指南》" },
      "ISFP-I": { title: "美学营造师", coreTraits: "营造治愈氛围时用细节创造美，提升感染力", strengths: "氛围营造与审美疗愈双重优势", sceneMatch: "品牌视觉/艺术装饰", blindSpots: "逃避现实引发过度修饰", suggestions: "落地部分创作，强制删减冗余", symbol: "贲", lifePath: "怎样从打工、老师、创业到实现百亿身价的人生", valueGuide: "⚠️ 逃避现实致过度修饰？<br>💎 细节创造治愈美，强制删减30%，制定《极简美学公式》" },
      "ISFP-S": { title: "中和守护者", coreTraits: "尊重个体节奏时保持纯粹初心，建立深度信任", strengths: "个性化关怀与真诚守护并重", sceneMatch: "艺术疗愈/手作工坊/医护教育", blindSpots: "天真轻信导致非理性反应", suggestions: "定期自我表达，前置背景调查", symbol: "中孚", lifePath: "怎样从打工、自由职业、创业到领域里理论家的人生", valueGuide: "⚠️ 天真轻信酿非理性反应？<br>💎 纯粹初心建深度信任，前置背景调查 ，制定《信任防御手册》" },
      "ISFP-C": { title: "临摹复刻者", coreTraits: "追求感官完美时无创造零误差，实现标准化执行", strengths: "工艺精度与复制效率双优", sceneMatch: "珠宝制作/文物修复/标准化生产", blindSpots: "过度分析阻碍作品完成", suggestions: "设定发布节点，启用身体直觉", symbol: "小过", lifePath: "怎样从开拓市场、团队领导人、创业到奇人的人生", valueGuide: "⚠️ 过度分析阻作品完成？<br>💎 零误差标准化执行，启用身体直觉 ，制作《直觉复刻沙盘》" },
      "INFP-D": { title: "信念殉道者", coreTraits: "孤注一掷抗争时控制破坏冲动，避免自我毁灭", strengths: "价值捍卫与欲望管理平衡", sceneMatch: "人权倡导/药品贸易/建筑工程监管", blindSpots: "悲情自毁伴随过度自信", suggestions: "寻找现实支点，体能训练宣泄", symbol: "无妄", lifePath: "怎样从中药贸易、大健康、咨询到实现十亿身价的人生", valueGuide: "⚠️ 悲情自毁伴过度自信？<br>💎 控制破坏冲动守价值，体能训练宣泄 ，建立《欲望管理表》" },
      "INFP-I": { title: "幻梦联结者", coreTraits: "传递希望故事时建立深度信任，修复断裂关系", strengths: "情感共鸣与关系修复力融合", sceneMatch: "文学创作/综合管理", blindSpots: "过度共情导致思维涣散", suggestions: "建立心理屏障，每日深度交流", symbol: "涣", lifePath: "怎样从经理人、创业到实现一亿身价的人生", valueGuide: "⚠️ 过度共情致思维涣散？<br>💎 深度信任修复关系，每日深度交流15分钟，制作《共情屏障手册》" },
      "INFP-S": { title: "生命守护者", coreTraits: "提供安全空间时超强耐心滋养，拯救濒死项目", strengths: "心灵避风港与项目再生力兼备", sceneMatch: "生命教育/教育研究/文化传承", blindSpots: "能量枯竭加剧资源封闭", suggestions: "限定助人时间，记录培育细节", symbol: "坤", lifePath: "怎样从企业策划到文化行业的领军人", valueGuide: "⚠️ 能量枯竭致资源封闭？<br>💎 超强耐心拯救濒死项目，限定助人时间，制定《项目复活指南》" },
      "INFP-C": { title: "智慧镜鉴者", coreTraits: "追问存在意义时转化精神财富，精准满足需求", strengths: "思想深度与资源转化力并重", sceneMatch: "教育政策/基础研究", blindSpots: "知识傲慢诱发虚无主义", suggestions: "践行微小善意，定期知识分享", symbol: "大畜", lifePath: "怎样从技术、销售到教授、专家、平台创始人的人生", valueGuide: "⚠️ 知识傲慢诱虚无主义？<br>💎 转化精神财富精准满足，践行微小善意 ，操练《意义重构罗盘》" },
      "INTP-D": { title: "逻辑推进器", coreTraits: "理论碾压对手时推动认知升级，超越工具局限", strengths: "辩论制胜与文明推进双重优势", sceneMatch: "学术革命/医疗创新/营销传播", blindSpots: "傲慢孤立导致全局盲区", suggestions: "承认认知局限，跨界知识学习", symbol: "离", lifePath: "怎样从金融、文化打工、创业到企业家、政客的人生", valueGuide: "⚠️ 傲慢孤立致全局盲区？<br>💎 认知升级突破工具局限，季度跨界学习 ，制定《盲区突破地图》" },
      "INTP-I": { title: "启蒙行动派", coreTraits: "趣味化理论时行动快过思考，实现快速试错", strengths: "知识普及与敏捷执行力融合", sceneMatch: "科普传播/流程优化", blindSpots: "浅尝辄止强化重复踩坑", suggestions: "深耕专业领域，三人可行性验证", symbol: "蒙", lifePath: "怎样从打工、教师、创业到成为高人的人生", valueGuide: "⚠️ 浅尝辄止致重复踩坑？<br>💎 快速试错普及知识，三人可行性验证，制作《试错成本压缩包》" },
      "INTP-S": { title: "沉思隐遁者", coreTraits: "追求绝对真理时判断退场时机，保存核心实力", strengths: "理论深度与战略保全力兼备", sceneMatch: "基础科研/哲学思辨/风险管理", blindSpots: "脱离现实加剧信息泄露", suggestions: "参与应用项目，设置撤离警报", symbol: "遁", lifePath: "怎样从经理人、创业到实现一亿身价的人生", valueGuide: "⚠️ 脱离现实加剧信息泄露？<br>💎 判断退场时机保实力，设置撤离警报，制定《战略保全协议》" },
      "INTP-C": { title: "变通适应者", coreTraits: "验证模型变量时快速适应环境，克服潜在危机", strengths: "系统分析与环境适应力融合", sceneMatch: "算法理论/航天军工/复杂系统", blindSpots: "模型囚禁导致机械执行", suggestions: "接受混沌现实，挑战新环境", symbol: "升", lifePath: "怎样从做管理、经理人、创业到实现十亿身价的人生", valueGuide: "⚠️ 模型囚禁致机械执行？<br>💎 快速适应克潜在危机，挑战新环境，操练《混沌适应沙盘》" },
      "ESTP-D": { title: "险境破局者", coreTraits: "闪电行动抢占资源时化险为夷，三重防护保障", strengths: "危机破局与身心防护力并重", sceneMatch: "应急响应/跨国商务/文化旅行", blindSpots: "短视冒进引发身份迷失", suggestions: "预判长期影响，组建支援小组", symbol: "坎", lifePath: "怎样从自由职业、创业到高人的人生", valueGuide: "⚠️ 短视冒进致身份迷失？<br>💎 三重防护化险为夷，组建支援小组，制作《险境生存锦囊》" },
      "ESTP-I": { title: "认知焕新者", coreTraits: "幽默吸引追随时颠覆陈旧观念，焕发新生力量", strengths: "团队活力与观念革新力融合", sceneMatch: "活动策划/公关危机/快消创新", blindSpots: "责任缺失固化消极思维", suggestions: "建立承诺机制，淘汰过时观念", symbol: "兑", lifePath: "怎样从打工、科研、创业到实现财务自由的人生", valueGuide: "⚠️ 责任缺失固化消极思维？<br>💎 颠覆旧观念焕新生，淘汰过时观念，制作《观念迭代日历》" },
      "ESTP-S": { title: "压力应变师", coreTraits: "专注眼前问题时解除精神枷锁，实现轻装上阵", strengths: "生存适应与压力转化力兼备", sceneMatch: "跨文化商务/危机谈判/高压力技术", blindSpots: "被动响应加剧自我压抑", suggestions: "制定阶段目标，设置释放仪式", symbol: "解", lifePath: "怎样从业务员、创业到成为领域里的专家教授的人生", valueGuide: "⚠️ 被动响应加剧自我压抑？<br>💎 解除枷锁轻装上阵，设置释放仪式，制作《压力转化器》" },
      "ESTP-C": { title: "多面应变者", coreTraits: "优化即时收益时多技能切换，应对各种突发", strengths: "机会捕捉与全能应变力融合", sceneMatch: "金融交易/科技前沿", blindSpots: "细节过度优化导致行动涣散", suggestions: "设定行动阈值，专注技能训练", symbol: "豫", lifePath: "怎样从做金融、文化打工、创业到成为投机家的人生", valueGuide: "⚠️ 细节优化致行动涣散？<br>💎 多技能切换捕机遇，专注技能训练，建立《全能应变矩阵》" },
      "ESFP-D": { title: "情绪共鸣者", coreTraits: "掌控现场节奏时用行动感染他人，创造情感共鸣", strengths: "氛围主导与情绪感染力并重", sceneMatch: "综艺主持/体育竞技/戏剧表演", blindSpots: "忽视个体需求导致关系伤害", suggestions: "深度倾听反馈，评估三方影响", symbol: "震", lifePath: "怎样从高精尖领域打工、创业到专家、教授的人生", valueGuide: "⚠️ 忽视需求致关系伤害？<br>💎 行动引爆情感海啸，评估三方影响 ，制定《共鸣能量仪》" },
      "ESFP-I": { title: "欢愉分享者", coreTraits: "创造沉浸体验时成功不忘利他，创造集体繁荣", strengths: "社交破冰与共享繁荣力融合", sceneMatch: "团队建设/客户体验/新兴行业", blindSpots: "回避深度话题加剧责任逃避", suggestions: "练习深度对话，举办成果共享会", symbol: "丰", lifePath: "怎样从打工、自由职业、创新创业到十亿身价的人生", valueGuide: "⚠️ 回避话题加剧责任逃避？<br>💎 沉浸体验创集体繁荣，举办成果共享会 ，制定《深度对话清单》" },
      "ESFP-S": { title: "共情维和者", coreTraits: "察觉他人需求时雪中送炭，持续增益系统", strengths: "情感雷达与精准赋能力兼备", sceneMatch: "客户服务/社区活动/政企关系", blindSpots: "边界模糊导致行动空想", suggestions: "每日独处回能，目标打卡拆解", symbol: "益", lifePath: "怎样从做销售、做门店到社交家、企业家的人生", valueGuide: "⚠️ 边界模糊致行动空想？<br>💎 雪中送炭持续增益，目标打卡拆解，制定《精准赋能导航》" },
      "ESFP-C": { title: "资源炼金师", coreTraits: "提升感官享受时吸收精华拒诱惑，实现资源最大化", strengths: "生活美学与资源转化力融合", sceneMatch: "酒店管理/会展策划/健康产业", blindSpots: "物质主义诱发信任危机", suggestions: "探索精神价值，列出可分享资源", symbol: "颐", lifePath: "怎样从房地产打工、创业到养生专家、企业家的人生", valueGuide: "⚠️ 物质主义诱信任危机？<br>💎 吸收精华拒诱惑，列出可分享资源 ，制作《资源炼金釜》" },
      "ENFP-D": { title: "愿景推进者", coreTraits: "发动理想变革时供给成长能量，平衡发展节奏", strengths: "变革核心与生态平衡力并重", sceneMatch: "营销革命/项目孵化/行政管理", blindSpots: "能量透支导致功利算计", suggestions: "分阶段推进，无条件支持项目", symbol: "鼎", lifePath: "怎样从各种打工、文化领域、创业到二把手的人生", valueGuide: "⚠️ 能量透支致功利算计？<br>💎 供给能量平衡发展，无条件支持项目，制定《能量守恒表》" },
      "ENFP-I": { title: "精英磁石", coreTraits: "联结跨界创意时让能人自愿跟随，组建精英团队", strengths: "创新生态与人才吸引力融合", sceneMatch: "团队激励/玄学服务/影视创作", blindSpots: "项目中断伴随突然撂挑", suggestions: "聚焦核心目标，深度服务追随者", symbol: "萃", lifePath: "怎样从做服务业的打工、创业到成为活动家的人生", valueGuide: "⚠️ 项目中断伴突然撂挑？<br>💎 让能人自愿跟随，深度服务追随者，建立《精英引力场》" },
      "ENFP-S": { title: "破晓开发者", coreTraits: "支持潜能释放时劣势变优势，黑暗中找生机", strengths: "潜能开发与逆境转化力兼备", sceneMatch: "教育创新/服务行业/工会工作", blindSpots: "责任回避强化行动恐惧", suggestions: "建立反馈机制，每日挑战恐惧", symbol: "晋", lifePath: "怎样从各种打工、运营、管理、创业到企业家的人生", valueGuide: "⚠️ 责任回避强化行动恐惧？<br>💎 黑暗中转化劣势，每日挑战恐惧，制定《破晓行动指南》" },
      "ENFP-C": { title: "场域构建者", coreTraits: "设计新范式时构建保护空间，促进深度连接", strengths: "文化重塑与安全连接力融合", sceneMatch: "组织文化/跨文化交流", blindSpots: "脱离现实导致预案缺失", suggestions: "落地最小原型，设计三重防护", symbol: "姤", lifePath: "怎样从各种打工、咨询、海外交流到传播家的人生", valueGuide: "⚠️ 脱离现实致预案缺失？<br>💎 构建保护性深度空间，设计三重防护，建立《文化安全边界图》" },
      "ENTP-D": { title: "思想传播者", coreTraits: "打破旧体系时快速扩散理念，改变群体认知", strengths: "系统创新与思想传播力并重", sceneMatch: "产品创新/国学创新/文化传播", blindSpots: "信任危机加剧偏见传播", suggestions: "保留过渡方案，对立立场验证", symbol: "蛊", lifePath: "怎样从各种打工、国学讲师到著书立说的人生", valueGuide: "⚠️ 信任危机加剧偏见传播？<br>💎 快速扩散改变认知，对立立场验证，制作《偏见消除器》" },
      "ENTP-I": { title: "智谋重构师", coreTraits: "激发集体智慧时薄弱处颠覆系统，创造新可能", strengths: "联想能力与系统颠覆力融合", sceneMatch: "趋势预测/舞台表演/公众演讲", blindSpots: "逻辑跳跃导致隐私泄露", suggestions: "结构化表达，突破前加固基础", symbol: "睽", lifePath: "怎样从打工、创业、公务员到高官厚禄的人生", valueGuide: "⚠️ 逻辑跳跃致隐私泄露？<br>💎 薄弱处颠覆创可能，突破前加固基础，制作《智谋防护罩》" },
      "ENTP-S": { title: "危机化者", coreTraits: "化解冲突时化对抗为能源，激活团队活力", strengths: "关系润滑与冲突转化力兼备", sceneMatch: "流程优化/艺术管理", blindSpots: "原则缺失诱发无谓冲突", suggestions: "坚守核心底线，记录摩擦价值", symbol: "蹇", lifePath: "怎样从跨行业打工、自由职业到财务自由的人生", valueGuide: "⚠️ 原则缺失诱发无谓冲突？<br>💎 化对抗为团队能源，记录摩擦价值点，制作《冲突炼金术》" },
      "ENTP-C": { title: "战略断舍者", coreTraits: "设计优雅方案时舍弃局部保整体，展现惊人决断", strengths: "问题简化与战略决断力融合", sceneMatch: "法律创新/行政事务/社团工作", blindSpots: "挑战权威导致错误决策", suggestions: "尊重既有框架，三重价值评估", symbol: "损", lifePath: "怎样从运营、社团管理、创业到协调家的人生", valueGuide: "⚠️ 挑战权威致错误决策？<br>💎 惊人决断保整体，三重价值评估，制定《优雅舍弃公式》" },
      "ESTJ-D": { title: "全景执行官", coreTraits: "清除低效环节时掌握系统全貌，预判发展趋势", strengths: "效能倍增与战略洞察力并重", sceneMatch: "工厂管理/军事指挥/金融投资", blindSpots: "官僚主义伴随破坏性批评", suggestions: "接纳灰度地带，批评附带方案", symbol: "观", lifePath: "怎样从普通员工、自由职业到经济学家的人生", valueGuide: "⚠️ 官僚主义伴破坏性批评？<br>💎 掌握全貌预判趋势，批评附带方案 ，制作《全景决策仪》" },
      "ESTJ-I": { title: "实力教练官", coreTraits: "激励目标达时无需展示实力，自然赢得尊重", strengths: "实战领导力与自然影响力融合", sceneMatch: "传统行业/跨文化合作", blindSpots: "控制欲过强导致决策失误", suggestions: "授权试错空间，三结果推演", symbol: "大壮", lifePath: "怎样从各种打工、海外交流、创业到成为名人的人生", valueGuide: "⚠️ 控制欲过强致决策失误？<br>💎 无需展示自然赢尊重，三结果推演，操练《自然影响力沙盘》" },
      "ESTJ-S": { title: "错误炼金师", coreTraits: "传承成功经验时挫折中快速复原，错误变创新", strengths: "组织连续性与抗挫创新力兼备", sceneMatch: "行政管理/质量控制/群众事业", blindSpots: "抗拒变革加剧重复犯错", suggestions: "小步试点创新，建立错误数据库", symbol: "复", lifePath: "怎样从普通员工、到自由职业，再到高官厚禄的人生", valueGuide: "⚠️ 抗拒变革致重复犯错？<br>💎 挫折中快速复原创新，建错误数据库，制作《失败炼金釜》" },
      "ESTJ-C": { title: "低调谋略家", coreTraits: "建立风控机制时清醒制定策略，吸引贵人相助", strengths: "运营安全与资源吸引力融合", sceneMatch: "供应链管理/商业理财", blindSpots: "过度保守导致目标模糊", suggestions: "计算创新收益，重要场合演练", symbol: "谦", lifePath: "怎样从各种打工、理财、创业到上市公司高管的人生", valueGuide: "⚠️ 过度保守致目标模糊？<br>💎 清醒策略引贵人，重要场合演练，制定《谋略隐形术》" },
      "ESFJ-D": { title: "和谐设计师", coreTraits: "调解冲突时促进多方共赢，化解对立创造和谐", strengths: "关系修复与系统平衡力并重", sceneMatch: "社区领袖/家族管理/文化领域", blindSpots: "道德绑架伴随规则破坏", suggestions: "尊重多元价值，每周规则自查", symbol: "泰", lifePath: "怎样从文化领域创业到实现身价十亿的人生", valueGuide: "⚠️ 道德绑架伴规则破坏？<br>💎 促进多方共赢创和谐，每周规则自查，制作《平衡设计图》" },
      "ESFJ-I": { title: "机遇导演", coreTraits: "强化归属感时识别高价值目标，实现逆袭上位", strengths: "文化凝聚与机遇捕捉力融合", sceneMatch: "客户关系/媒体公关/酒店管理", blindSpots: "忽视个性导致危险诱惑", suggestions: "定制化关怀，设置黑暗警报人", symbol: "归妹", lifePath: "怎样从操作岗位、领导、自由职业到表演家的人生", valueGuide: "⚠️ 忽视个性致危险诱惑？<br>💎 识别高价值目标逆袭，设置黑暗警报人，建立《机遇捕网》" },
      "ESFJ-S": { title: "时机等待者", coreTraits: "回避变动时超强忍耐力，精准把握时机", strengths: "需求满足与时机判断力兼备", sceneMatch: "学校教育/心理教育/社会保障", blindSpots: "自我压抑引发焦虑失控", suggestions: "建立需求清单，设置等待奖励", symbol: "需", lifePath: "怎样从各种项目的寻找到成为教育家的人生", valueGuide: "⚠️ 自我压抑引发焦虑失控？<br>💎 超强忍耐精准狙击，设置等待奖励 ，建立《时机雷达》" },
      "ESFJ-C": { title: "灵感联结者", coreTraits: "平衡各方利益时行动中获启示，简单解决复杂", strengths: "信任构建与灵感创造力融合", sceneMatch: "宴会外交/形象设计/军旅", blindSpots: "过度谨慎阻碍直觉运用", suggestions: "尝试适度冒险，携带灵感笔记本", symbol: "旅", lifePath: "怎样从运输、旅游、互联网打工、创业到企业家的人生", valueGuide: "⚠️ 过度谨慎阻碍直觉运用？<br>💎 行动中获启示解复杂，携带灵感笔记本，制作《直觉解锁器》" },
      "ENFJ-D": { title: "理念覆盖者", coreTraits: "动员集体行动时广泛传播理念，快速普及新知", strengths: "群众引领与知识传播力并重", sceneMatch: "政治活动/教育推广/知识产权", blindSpots: "理想专制导致浅层运作", suggestions: "收集现实数据，选择领域深耕", symbol: "巽", lifePath: "怎样从各种打工、教师、创业到成为国学大师的人生", valueGuide: "⚠️ 理想专制致浅层运作？<br>💎 广泛传播快速普及，选择领域深耕，制作《深度传播锚》" },
      "ENFJ-I": { title: "暗夜探索者", coreTraits: "激发个体潜能时无光环境找方向，绝境求生", strengths: "成长催化与逆境生存力融合", sceneMatch: "高管教练/科研发明/边防安保", blindSpots: "情感透支加剧独断倾向", suggestions: "设置助人边界，记录黑暗发现", symbol: "明夷", lifePath: "怎样从打工、科研、创业到科学家、知名人士的人生", valueGuide: "⚠️ 情感透支加剧独断倾向？<br>💎 无光环境绝境求生，记录黑暗发现 ，制定《至暗生存指南》" },
      "ENFJ-S": { title: "共识布道师", coreTraits: "拒绝标签化时消灭分歧找共识，协调复杂关系", strengths: "长期影响与关系协调力兼备", sceneMatch: "艺人经纪/团队培养/跨地区合伙", blindSpots: "存在感不足导致孤立倾向", suggestions: "适度自我展示，强制集体活动", symbol: "同人", lifePath: "怎样从各种打工、市场开拓、平台打工、创业到社会学家的人生", valueGuide: "⚠️ 存在感不足致孤立倾向？<br>💎 消灭分歧找共识，强制集体活动，制定《共识磁场图》" },
      "ENFJ-C": { title: "风险预言家", coreTraits: "预判人文风险时看透本质止损，避开致命陷阱", strengths: "生态构建与危机预警力融合", sceneMatch: "教育产品设计/HR体系/职业投资", blindSpots: "脱离现实诱发赌博心理", suggestions: "沉浸一线体验，设置撤退红线", symbol: "剥", lifePath: "怎样从打工、创业到实现一亿身价的人生", valueGuide: "⚠️ 脱离现实诱发赌博心理？<br>💎 看透本质避致命陷阱，设置撤退红线 ，建立《人性风险图谱》" },
      "ENTJ-D": { title: "逆境爆破手", coreTraits: "碾压障碍时越挫越勇死磕，把烂牌打成王炸", strengths: "战略执行与逆境转化力并重", sceneMatch: "商业并购/技术科研/医学研究", blindSpots: "团队离心伴随急功近利", suggestions: "植入共荣机制，目标闯关分解", symbol: "屯", lifePath: "怎样从打工或创业走上专家教授的人生", valueGuide: "⚠️ 急功近利致团队离心？<br>💎 死磕精神化烂牌为王炸，目标闯关分解，制作《逆境转化器》" },
      "ENTJ-I": { title: "希望发光者", coreTraits: "吸引资源投入时失败中保存火种，展现不摧韧性", strengths: "资源磁吸与抗衰活力融合", sceneMatch: "创业融资/学术研究/教育培训", blindSpots: "过度承诺导致信用危机", suggestions: "量化阶段成果，建立能量保护区", symbol: "困", lifePath: "怎样从打工、自由职业、创业到领域里理论家的人生", valueGuide: "⚠️ 过度承诺致信用危机？<br>💎 失败中保存不摧韧性，建能量保护区，制作《火种保存舱》" },
      "ENTJ-S": { title: "体系执政官", coreTraits: "稳健扩张时犀利发现漏洞，阻止灾难性错误", strengths: "制度构建与风险洞察力兼备", sceneMatch: "资本运作/公检法/质量监察", blindSpots: "创新衰退加剧情绪失控", suggestions: "设立创新特区，情绪日记管理", symbol: "否", lifePath: "怎样从品质管理、领导、军人到高位武官的人生", valueGuide: "⚠️ 创新衰退加剧情绪失控？<br>💎 犀利洞察阻系统崩溃，设创新特区，编写《执政官情绪日记》" },
      "ENTJ-C": { title: "质朴管控官", coreTraits: "设计冗余方案时爆发原始生命力，纯粹质朴感染", strengths: "系统抗性与本源感染力融合", sceneMatch: "集团管控/公关秘书", blindSpots: "决策迟缓伴随自我怀疑", suggestions: "设置冒险配额，每日记录优点", symbol: "未济", lifePath: "怎样从做公共关系、业务员、创业到交际家的人生", valueGuide: "⚠️ 决策迟缓伴自我怀疑？<br>💎 原始生命力纯粹感染，每日记录优点，开启《质朴力量激发器》" }
    };

    for (const [typeCode, data] of Object.entries(resultTypes)) {
      const existingResult = await this.personalityResultRepository.findByTypeCode(typeCode);

      if (!existingResult) {
        await this.personalityResultRepository.create({
          typeCode,
          ...data
        });
      }
    }
  }

  /**
   * 根据计算结果获取对应的人格测试结果
   */
  async getPersonalityResultByCalculation(calculateResultDto: CalculateResultDto): Promise<PersonalityResultResponseDto> {
    // 先计算MBTI和DISC结果
    const calculationResult = await this.calculateResults(calculateResultDto);

    // 根据计算结果获取对应的人格类型
    const result = await this.personalityResultRepository.findByTypeCode(calculationResult.finalType);

    if (!result) {
      throw new NotFoundException(`未找到人格类型 ${calculationResult.finalType} 的结果`);
    }

    return result;
  }

  /**
   * 计算MBTI和DISC结果并返回完整的人格测试结果信息
   */
  async calculateResultsWithPersonality(calculateResultDto: CalculateResultDto): Promise<CalculateResultWithPersonalityResponseDto> {
    // 先计算MBTI和DISC结果
    const calculationResult = await this.calculateResults(calculateResultDto);

    // 根据计算结果获取对应的人格类型
    const personalityResult = await this.personalityResultRepository.findByTypeCode(calculationResult.finalType);

    if (!personalityResult) {
      throw new NotFoundException(`未找到人格类型 ${calculationResult.finalType} 的结果`);
    }

    // 如果提供了用户ID，则保存到用户历史记录
    let savedToHistory = false;
    if (calculateResultDto.userId) {
      try {
        await this.saveUserAnswerRecord({
          userId: calculateResultDto.userId,
          answers: calculateResultDto.answers,
          remark: calculateResultDto.remark
        });
        savedToHistory = true;
      } catch (error) {
        // 如果保存历史记录失败，记录错误但不影响主要功能
        console.error('保存用户历史记录失败:', error);
      }
    }

    // 组合计算结果和人格测试结果
    return {
      ...calculationResult,
      personalityResult: {
        typeCode: personalityResult.typeCode,
        title: personalityResult.title,
        coreTraits: personalityResult.coreTraits,
        strengths: personalityResult.strengths,
        sceneMatch: personalityResult.sceneMatch,
        blindSpots: personalityResult.blindSpots,
        suggestions: personalityResult.suggestions,
        symbol: personalityResult.symbol,
        lifePath: personalityResult.lifePath,
        valueGuide: personalityResult.valueGuide
      },
      savedToHistory
    };
  }

  /**
   * 保存用户答题记录
   */
  async saveUserAnswerRecord(createUserAnswerRecordDto: CreateUserAnswerRecordDto): Promise<UserAnswerRecordResponseDto> {
    const { userId, answers, remark } = createUserAnswerRecordDto;

    // 先计算MBTI和DISC结果
    const calculationResult = await this.calculateResults({ answers });

    // 将之前的记录设为非当前
    await this.userAnswerRecordRepository.updateCurrentStatus(userId, false);

    // 创建新的答题记录
    const savedRecord = await this.userAnswerRecordRepository.create({
      userId,
      answers,
      mbtiType: calculationResult.mbtiType,
      discType: calculationResult.discType,
      finalType: calculationResult.finalType,
      mbtiScores: calculationResult.mbti,
      discScores: calculationResult.disc,
      dimensionScores: calculationResult.dimensionScores,
      isCurrent: true,
      remark
    });

    return savedRecord;
  }

  /**
   * 获取用户的答题记录历史（分页）
   */
  async getUserAnswerRecords(getUserAnswerRecordsDto: GetUserAnswerRecordsDto): Promise<PaginatedUserAnswerRecordsResponseDto> {
    const { userId, page = 1, pageSize = 10 } = getUserAnswerRecordsDto;

    // 使用repository的分页方法
    const { records, total } = await this.userAnswerRecordRepository.findByUserIdWithPagination(userId, page, pageSize);

    // 计算分页信息
    const totalPages = Math.ceil(total / pageSize);
    const hasNext = page < totalPages;
    const hasPrev = page > 1;

    return {
      data: records,
      pagination: {
        page,
        pageSize,
        total,
        totalPages,
        hasNext,
        hasPrev
      }
    };
  }

  /**
   * 获取用户的当前人格类型
   */
  async getUserCurrentType(getUserCurrentTypeDto: GetUserCurrentTypeDto): Promise<UserCurrentTypeResponseDto> {
    const { userId } = getUserCurrentTypeDto;

    // 获取当前类型记录
    const currentRecord = await this.userAnswerRecordRepository.findCurrentByUserId(userId);

    if (!currentRecord) {
      throw new NotFoundException('用户暂无答题记录');
    }

    // 获取对应的人格测试结果
    const personalityResult = await this.personalityResultRepository.findByTypeCode(currentRecord.finalType);

    // 获取答题历史统计
    const totalTests = await this.userAnswerRecordRepository.countByUserId(userId);

    const lastTestRecord = await this.userAnswerRecordRepository.findLatestByUserId(userId);

    return {
      userId,
      currentType: {
        mbtiType: currentRecord.mbtiType,
        discType: currentRecord.discType,
        finalType: currentRecord.finalType,
        personalityResult: personalityResult ? {
          typeCode: personalityResult.typeCode,
          title: personalityResult.title,
          coreTraits: personalityResult.coreTraits,
          strengths: personalityResult.strengths,
          sceneMatch: personalityResult.sceneMatch,
          blindSpots: personalityResult.blindSpots,
          suggestions: personalityResult.suggestions,
          symbol: personalityResult.symbol,
          lifePath: personalityResult.lifePath,
          valueGuide: personalityResult.valueGuide
        } : undefined
      },
      testHistory: {
        totalTests,
        lastTestDate: lastTestRecord?.createdAt || currentRecord.createdAt
      }
    };
  }

  /**
   * 获取用户的proto类型（当前类型）
   */
  async getUserProtoType(userId: number): Promise<{ proto: string }> {
    const currentRecord = await this.userAnswerRecordRepository.findCurrentByUserId(userId);

    if (!currentRecord) {
      throw new NotFoundException('用户暂无答题记录');
    }

    return {
      proto: currentRecord.finalType
    };
  }
}
