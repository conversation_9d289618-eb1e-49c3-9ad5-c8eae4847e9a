import { <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, IsBoolean, Min, Max } from 'class-validator';
import { Transform } from 'class-transformer';

export class CreateUserAnswerRecordDto {
  @IsNumber()
  userId: number;

  @IsArray()
  answers: number[];

  @IsOptional()
  @IsString()
  remark?: string;
}

export class GetUserAnswerRecordsDto {
  @IsNumber()
  @Transform(({ value }) => parseInt(value))
  userId: number;

  @IsOptional()
  @IsNumber()
  @Min(1)
  @Transform(({ value }) => parseInt(value) || 1)
  page?: number = 1;

  @IsOptional()
  @IsNumber()
  @Min(1)
  @Max(100)
  @Transform(({ value }) => parseInt(value) || 10)
  pageSize?: number = 10;
}

export class GetUserCurrentTypeDto {
  @IsNumber()
  userId: number;
}

export class UserAnswerRecordResponseDto {
  id: number;
  userId: number;
  answers: number[];
  mbtiType: string;
  discType: string;
  finalType: string;
  mbtiScores: { E: number; I: number; S: number; N: number; T: number; F: number; J: number; P: number };
  discScores: { D: number; I: number; S: number; C: number };
  dimensionScores: { [key: string]: number };
  isCurrent: boolean;
  remark?: string;
  createdAt: Date;
  updatedAt: Date;
}

export class PaginatedUserAnswerRecordsResponseDto {
  data: UserAnswerRecordResponseDto[];
  pagination: {
    page: number;
    pageSize: number;
    total: number;
    totalPages: number;
    hasNext: boolean;
    hasPrev: boolean;
  };
}

export class UserCurrentTypeResponseDto {
  userId: number;
  currentType: {
    mbtiType: string;
    discType: string;
    finalType: string;
    personalityResult?: {
      typeCode: string;
      title: string;
      coreTraits: string;
      strengths: string;
      sceneMatch: string;
      blindSpots: string;
      suggestions: string;
      symbol: string;
      lifePath: string;
      valueGuide: string;
    };
  };
  testHistory: {
    totalTests: number;
    lastTestDate: Date;
  };
} 