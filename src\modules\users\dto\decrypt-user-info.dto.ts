import { ApiProperty } from '@nestjs/swagger';
import { IsString, IsNotEmpty, IsOptional } from 'class-validator';

export class DecryptUserInfoDto {
  @ApiProperty({
    description: '加密的用户数据',
    example: '2Jguq6v771RzmIg824V8mAD+CYBjIoABP9geaQF8qGeHHzzZlyWsIDl6L8BQGtmISAD2JbdfPw/TUVBPse1XnhWQVA+8FvCnoVfjAex58RPOtf7PG0699gHYCaxoS5sXN7xBz75fEpmH2YWFc9Xo+31p8z6k2YBN1dLPStvqfcz1Z1w5cofvXAFbEubbjuRfiRl9JKzHnXOtQxiVICyPOg=='
  })
  @IsString()
  @IsNotEmpty()
  encryptedData: string;

  @ApiProperty({
    description: '初始向量',
    example: 'kmneH1ouMGszcWXgEqkfhA=='
  })
  @IsString()
  @IsNotEmpty()
  iv: string;

  @ApiProperty({
    description: '微信登录凭证（与sessionKey二选一）',
    example: '4a0fbec7c332c9e20c04910b287c0183da74ced7bd089b3657a724363f201d67',
    required: false
  })
  @IsString()
  @IsOptional()
  code?: string;

  @ApiProperty({
    description: '微信会话密钥（与code二选一）',
    example: 'HyVFkGl5F5OQWJZZaNzBBg==',
    required: false
  })
  @IsString()
  @IsOptional()
  sessionKey?: string;

  @ApiProperty({
    description: '数据类型（phoneNumber或userInfo）',
    example: 'phoneNumber',
    required: false
  })
  @IsString()
  @IsOptional()
  dataType?: 'phoneNumber' | 'userInfo';
}

export class DecryptedUserInfoResponseDto {
  @ApiProperty({
    description: '手机号（仅当dataType为phoneNumber时返回）',
    example: '13800138000',
    required: false
  })
  phoneNumber?: string;

  @ApiProperty({
    description: '纯手机号（仅当dataType为phoneNumber时返回）',
    example: '13800138000',
    required: false
  })
  purePhoneNumber?: string;

  @ApiProperty({
    description: '国家代码（仅当dataType为phoneNumber时返回）',
    example: '86',
    required: false
  })
  countryCode?: string;

  @ApiProperty({
    description: '昵称（仅当dataType为userInfo时返回）',
    example: '张三',
    required: false
  })
  nickName?: string;

  @ApiProperty({
    description: '性别（仅当dataType为userInfo时返回）',
    example: 1,
    required: false
  })
  gender?: number;

  @ApiProperty({
    description: '语言（仅当dataType为userInfo时返回）',
    example: 'zh_CN',
    required: false
  })
  language?: string;

  @ApiProperty({
    description: '城市（仅当dataType为userInfo时返回）',
    example: 'Guangzhou',
    required: false
  })
  city?: string;

  @ApiProperty({
    description: '省份（仅当dataType为userInfo时返回）',
    example: 'Guangdong',
    required: false
  })
  province?: string;

  @ApiProperty({
    description: '国家（仅当dataType为userInfo时返回）',
    example: 'CN',
    required: false
  })
  country?: string;

  @ApiProperty({
    description: '头像URL（仅当dataType为userInfo时返回）',
    example: 'http://wx.qlogo.cn/mmopen/vi_32/aSKcBBPpibyKNicHNTMM0qJVh8Kjgiak2AHWr8MHM4WgMEm7GFhsf8OYrySdbvAMvTsw3mo8ibKicsnfN5pRjl1p8HQ/0',
    required: false
  })
  avatarUrl?: string;

  @ApiProperty({
    description: '微信开放平台唯一标识（仅当dataType为userInfo时返回）',
    example: 'ocMvos6NjeKLIBqg5Mr9QjxrP1FA',
    required: false
  })
  unionId?: string;

  @ApiProperty({
    description: '水印信息',
    example: {
      timestamp: 1477314187,
      appid: 'wx4f4bc4dec97d474b'
    }
  })
  watermark: {
    timestamp: number;
    appid: string;
  };
} 