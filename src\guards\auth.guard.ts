import {
  Injectable,
  CanActivate,
  ExecutionContext,
  UnauthorizedException,
  Logger
} from "@nestjs/common";
import { JwtService } from "@nestjs/jwt";
import { UsersService } from "../modules/users/users.service";
import { ConfigService } from "@nestjs/config";

@Injectable()
export class AuthGuard implements CanActivate {
  constructor(
    private jwtService: JwtService,
    private usersService: UsersService,
    private configService: ConfigService
  ) { }

  async canActivate(context: ExecutionContext): Promise<boolean> {
    const request = context.switchToHttp().getRequest();
    const jwtToken = request.headers['authorization'] || request.headers['Authorization']
    if (!jwtToken) {
      throw new UnauthorizedException("用户未登录");
    }
    const token = jwtToken.replace("Bearer ", "");
    if (!token) {
      throw new UnauthorizedException("错误");
    }

    try {
      const payload = this.jwtService.verify(token);

      const isBlacklisted = await this.usersService.isTokenBlacklisted(token);
      if (isBlacklisted) {
        throw new UnauthorizedException("用户登录信息失效，请重新登录");
      }
      request.user = payload;
      request.token = token;

      return true;
    } catch (error) {

      if (error instanceof UnauthorizedException) {
        throw error;
      }

      if (error.name === 'JsonWebTokenError') {
        throw new UnauthorizedException("无效的token");
      }

      if (error.name === 'TokenExpiredError') {
        throw new UnauthorizedException("用户登录已过期");
      }
      throw new UnauthorizedException("用户验证失败");
    }
  }
}
