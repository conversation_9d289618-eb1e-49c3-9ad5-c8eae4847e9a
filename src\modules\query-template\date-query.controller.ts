import { Controller, Get, Post, Body, UseInterceptors } from '@nestjs/common';
import { DateQueryService } from './date-query.service';
import { DateQueryDto, DateQueryResponseDto } from './dto/date-query.dto';
import { ResponseInterceptor } from '../../common/interceptors/response.interceptor';

@Controller('date-query')
@UseInterceptors(ResponseInterceptor)
export class DateQueryController {
  constructor(private readonly dateQueryService: DateQueryService) {}

  /**
   * 原型接口：根据年月日查询对应的数字
   * @param dateQueryDto 包含年月日的查询参数
   * @returns 对应的数字值和相关信息
   */
  @Post('prototype')
  async queryByDate(@Body() dateQueryDto: DateQueryDto): Promise<DateQueryResponseDto> {
    return await this.dateQueryService.queryByDate(dateQueryDto);
  }

  /**
   * 初始化数据接口
   */
  @Post('initialize')
  async initializeData(): Promise<{ message: string }> {
    await this.dateQueryService.initializeData();
    return { message: '数据初始化成功' };
  }

  /**
   * 获取所有数据
   */
  @Get('all')
  async getAllData() {
    return await this.dateQueryService.findAll();
  }
} 