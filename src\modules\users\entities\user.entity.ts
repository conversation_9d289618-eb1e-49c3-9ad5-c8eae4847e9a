import {
  Entity,
  PrimaryGeneratedColumn,
  Column,
  BeforeInsert,
  BeforeUpdate,
  CreateDateColumn,
  UpdateDateColumn,
  OneToMany,
  Index,
  ManyToOne,
  JoinColumn,
} from "typeorm";
import * as bcrypt from "bcrypt";
import { ApiProperty } from "@nestjs/swagger";
import { Conversation } from "../../coze/entities/conversation.entity";

export enum Role {
  Admin = "admin",
  User = "user",
}

export enum UserType {
  Individual = "individual",
  Institution = "institution",
}

export enum Gender {
  Male = "male",
  Female = "female",
  Other = "other",
}
export enum CalendarType {
  Solar = 'solar',   // 公历
  Lunar = 'lunar',   // 农历
}

export enum MembershipType {
  Free = "free",
  Basic = "basic",
  Premium = "premium",
  Team = "team",
}

export enum PermissionLevel {
  Individual = "individual",
  Team = "team",
}
@Entity()
export class User {
  @PrimaryGeneratedColumn()
  @ApiProperty({ description: '用户唯一标识' })
  id: number;

  @Column()
  @ApiProperty({ description: '用户名（昵称）' })
  name: string;

  @Column({ type: 'varchar', unique: true, nullable: true })
  @ApiProperty({ description: '用户邮箱（唯一）', required: false })
  email: string | null;

  @Column()
  @ApiProperty({ description: '用户密码（加密存储）' })
  password: string;

  @Column({ type: 'enum', enum: Role, default: Role.User })
  @ApiProperty({ enum: Role, description: '用户角色，admin 或 user', default: Role.User })
  role: Role;

  @Column({ type: 'enum', enum: UserType, default: UserType.Individual })
  @ApiProperty({ enum: UserType, description: '用户类型：个人或机构', default: UserType.Individual })
  userType: UserType;

  @Column({ nullable: true })
  @ApiProperty({ description: '真实姓名（可选）', required: false })
  fullName: string;

  @Column({ type: 'varchar', unique: true, nullable: true })
  @ApiProperty({ description: '手机号（唯一）', required: false })
  phoneNumber: string | null;

  @Column({ nullable: true, unique: true })
  @ApiProperty({ description: '微信小程序openid（唯一）', required: false })
  @Index('idx_user_openid')
  openidWx: string;

  @Column({ nullable: true })
  @ApiProperty({ description: '地址（可选）', required: false })
  address: string;

  @Column({ type: 'enum', enum: Gender, nullable: true })
  @ApiProperty({ enum: Gender, description: '性别（可选）', required: false })
  gender: Gender;

  @Column({ type: 'date', nullable: true })
  @ApiProperty({ description: '出生日期（可选）', type: String, format: 'date', required: false })
  birthDate: Date;

  @Column({ type: 'enum', enum: CalendarType, default: CalendarType.Solar })
  @ApiProperty({ enum: CalendarType, description: '生日类型，solar 为公历，lunar 为农历', default: CalendarType.Solar })
  birthCalendarType: CalendarType;



  @Column({ default: true })
  @ApiProperty({ description: '账号是否激活', default: true })
  isActive: boolean;

  @Column({ nullable: true })
  @ApiProperty({ description: '头像链接（可选）', required: false, default: 'https://kanli-1346109866.cos.ap-beijing.myqcloud.com/17C5F717FF6A3B24965D6E1234FEBD1E.jpg' })
  profilePicture: string;

  @Column({ nullable: true })
  @ApiProperty({ description: '背景图链接（可选）', required: false })
  profileBackgroundPicture: string;

  @Column({ type: 'enum', enum: MembershipType, default: MembershipType.Free })
  @ApiProperty({ enum: MembershipType, description: '会员类型', default: MembershipType.Free })
  membershipType: MembershipType;

  @Column({ nullable: true })
  @ApiProperty({ description: '会员过期时间（可选）', type: String, format: 'date-time', required: false })
  membershipExpireDate: Date;

  @Column({ default: false })
  @ApiProperty({ description: '是否拥有团队权限', default: false })
  hasTeamPermission: boolean;

  @Column({ default: 10 })
  @ApiProperty({ description: '免费评估次数', default: 10 })
  freeAssessmentCount: number;

  @Column({ default: 10 })
  @ApiProperty({ description: '免费 AI 解读次数', default: 10 })
  freeAIInterpretationCount: number;

  @Column({ default: 0 })
  @ApiProperty({ description: '总评估次数', default: 0 })
  totalAssessmentCount: number;

  @Column({ default: 0 })
  @ApiProperty({ description: '总 AI 解读次数', default: 0 })
  totalAIInterpretationCount: number;

  @Column({ nullable: true, type: 'json' })
  @ApiProperty({ description: '原型数据（JSON）', required: false, type: Object })
  archetypeData: Record<string, any>;

  @Column({ nullable: true, type: 'json' })
  @ApiProperty({ description: '评估历史记录（JSON）', required: false, type: Object })
  assessmentHistory: Record<string, any>;

  @CreateDateColumn()
  @ApiProperty({ description: '创建时间', type: String, format: 'date-time' })
  createdAt: Date;

  @UpdateDateColumn()
  @ApiProperty({ description: '更新时间', type: String, format: 'date-time' })
  updatedAt: Date;

  @Column({ nullable: true })
  @ApiProperty({ description: '上次登录时间', type: String, format: 'date-time', required: false })
  lastLoginAt: Date;

  @Column({ default: 0 })
  @ApiProperty({ description: '登录次数', default: 0 })
  loginCount: number;

  @Column({ nullable: true, type: 'json' })
  @ApiProperty({ description: '支付记录（JSON）', required: false, type: Object })
  paymentHistory: Record<string, any>;

  @Column({ default: false })
  @ApiProperty({ description: '是否锁定出生日期', default: false })
  birthDateLocked: boolean;

  // 邀请码相关字段
  @Column({ unique: true, length: 32, nullable: true })
  @ApiProperty({ description: '用户专属邀请码', required: false })
  @Index('idx_user_invite_code')
  myInviteCode: string;

  @Column({})
  @ApiProperty({ description: '用户原型', })
  proto: string

  @Column({ nullable: true })
  @ApiProperty({ description: '邀请人ID（谁邀请了这个用户）', required: false })
  invitedById: number;

  @Column({ default: 0 })
  @ApiProperty({ description: '成功邀请的人数', default: 0 })
  inviteCount: number;

  @Column({ type: 'enum', enum: PermissionLevel, default: PermissionLevel.Individual })
  @ApiProperty({ enum: PermissionLevel, description: '权限级别', default: PermissionLevel.Individual })
  permissionLevel: PermissionLevel;

  @Column({ default: false })
  @ApiProperty({ description: '是否允许邀请他人', default: false })
  canInviteOthers: boolean;

  @Column({ default: false })
  @ApiProperty({ description: '是否拥有分享获利权益', default: false })
  hasShareProfitRights: boolean;

  @Column({ nullable: true })
  @ApiProperty({ description: '团队权限开通时间', required: false })
  teamPermissionGrantedAt: Date;

  @Column({ nullable: true, type: 'json' })
  @ApiProperty({ description: '权限历史记录', required: false })
  permissionHistory: Record<string, any>;

  // 关联关系
  @OneToMany(() => Conversation, conversation => conversation.user)
  conversations: Conversation[];

  @ManyToOne(() => User, user => user.invitedUsers, { nullable: true })
  @JoinColumn({ name: 'invitedById' })
  invitedBy: User;

  @OneToMany(() => User, user => user.invitedBy)
  invitedUsers: User[];

  // 邀请记录关联（这里需要懒加载避免循环引用）
  @OneToMany('TeamInvite', 'inviter')
  sentInvites: any[];   // 发送的邀请

  @OneToMany('TeamInvite', 'invitee')
  receivedInvites: any[];

  // 支付记录关联
  @OneToMany('Payment', 'user')
  payments: any[];  // 支付记录



  // 加密密码
  @BeforeInsert()
  @BeforeUpdate()
  async hashPassword() {
    if (this.password) {
      const salt = await bcrypt.genSalt(10);
      this.password = await bcrypt.hash(this.password, salt);
    }
  }

  // 校验密码
  async validatePassword(password: string): Promise<boolean> {
    return await bcrypt.compare(password, this.password);
  }
  /**
  * 示例用法：
  * const user = await userRepository.findOne({ where: { email: '<EMAIL>' } });
  * if (user && await user.validatePassword('userInputPassword')) {
  *   // 密码正确，允许用户登录
  * } else {
  *   // 密码错误，拒绝访问
  * }
  */
}
