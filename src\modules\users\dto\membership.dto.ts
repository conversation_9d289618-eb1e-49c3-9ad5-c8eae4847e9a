import { ApiProperty } from "@nestjs/swagger";
import { IsEnum, IsDateString, IsOptional, IsBoolean } from "class-validator";
import { MembershipType } from "../entities/user.entity";

export class UpdateMembershipDto {
  @ApiProperty({
    enum: MembershipType,
    description: "会员类型"
  })
  @IsEnum(MembershipType, { message: "会员类型格式不正确" })
  membershipType: MembershipType;

  @ApiProperty({
    description: "会员过期时间",
    type: String,
    format: "date-time",
    example: "2024-12-31T23:59:59.000Z"
  })
  @IsDateString({}, { message: "会员过期时间格式不正确" })
  membershipExpireDate: string;

  @ApiProperty({ description: "是否拥有团队权限（可选）", required: false })
  @IsOptional()
  @IsBoolean()
  hasTeamPermission?: boolean;
}

export class MembershipStatusDto {
  @ApiProperty({ enum: MembershipType, description: "会员类型" })
  membershipType: MembershipType;

  @ApiProperty({ description: "会员过期时间", type: String, format: "date-time", required: false })
  membershipExpireDate?: Date;

  @ApiProperty({ description: "是否拥有团队权限" })
  hasTeamPermission: boolean;

  @ApiProperty({ description: "会员是否有效" })
  isActive: boolean;

  @ApiProperty({ description: "剩余天数（如果会员有效）", required: false })
  remainingDays?: number;
} 