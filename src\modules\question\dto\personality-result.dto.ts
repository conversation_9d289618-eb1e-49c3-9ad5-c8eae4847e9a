import { IsString, IsNotEmpty, IsOptional } from 'class-validator';

export class CreatePersonalityResultDto {
  @IsString()
  @IsNotEmpty()
  typeCode: string;

  @IsString()
  @IsNotEmpty()
  title: string;

  @IsString()
  @IsNotEmpty()
  coreTraits: string;

  @IsString()
  @IsNotEmpty()
  strengths: string;

  @IsString()
  @IsNotEmpty()
  sceneMatch: string;

  @IsString()
  @IsNotEmpty()
  blindSpots: string;

  @IsString()
  @IsNotEmpty()
  suggestions: string;

  @IsString()
  @IsNotEmpty()
  symbol: string;

  @IsString()
  @IsNotEmpty()
  lifePath: string;

  @IsString()
  @IsNotEmpty()
  valueGuide: string;
}

export class GetPersonalityResultDto {
  @IsString()
  @IsNotEmpty()
  typeCode: string;
}

export class PersonalityResultResponseDto {
  id: number;
  typeCode: string;
  title: string;
  coreTraits: string;
  strengths: string;
  sceneMatch: string;
  blindSpots: string;
  suggestions: string;
  symbol: string;
  lifePath: string;
  valueGuide: string;
  createdAt: Date;
  updatedAt: Date;
} 