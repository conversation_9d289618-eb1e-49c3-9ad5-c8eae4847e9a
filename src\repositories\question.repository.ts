import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { Question } from '../modules/question/entities/question.entity';

@Injectable()
export class QuestionRepository {
  constructor(
    @InjectRepository(Question)
    private questionRepository: Repository<Question>
  ) { }

  async findAll(): Promise<Question[]> {
    return this.questionRepository.find({ relations: ['options'] });
  }

  async findById(id: number): Promise<Question | null> {
    return this.questionRepository.findOne({ 
      where: { id }, 
      relations: ['options'] 
    });
  }

  async create(data: Partial<Question>): Promise<Question> {
    const question = this.questionRepository.create(data);
    return this.questionRepository.save(question);
  }

  async update(id: number, data: Partial<Question>): Promise<void> {
    await this.questionRepository.update(id, data);
  }

  async delete(id: number): Promise<void> {
    await this.questionRepository.delete(id);
  }

  async findWithOptions(): Promise<Question[]> {
    return this.questionRepository.find({ 
      relations: ['options'],
      order: { id: 'ASC' }
    });
  }
} 