import { IsString, <PERSON><PERSON>otEmpty, <PERSON><PERSON><PERSON><PERSON>, IsOptional } from 'class-validator';
import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';

export class WechatPaymentDto {
  @ApiProperty({ description: '小程序appid' })
  @IsString()
  @IsNotEmpty()
  appid: string;

  @ApiProperty({ description: '微信账号的uuid' })
  @IsString()
  @IsNotEmpty()
  payer: string;

  @ApiProperty({ description: '微信账号的openid' })
  @IsString()
  @IsNotEmpty()
  openid: string;

  @ApiProperty({ description: '支付金额（元）' })
  @IsString()
  @IsNotEmpty()
  amount: string;

  @ApiProperty({ description: '订单编号' })
  @IsString()
  @IsNotEmpty()
  orderNo: string;

  @ApiProperty({ description: '支付描述' })
  @IsString()
  @IsNotEmpty()
  description: string;
}

export class WxNotifyDto {
  @ApiProperty({ description: '通知ID' })
  @IsString()
  @IsNotEmpty()
  id: string;

  @ApiProperty({ description: '创建时间' })
  @IsString()
  @IsNotEmpty()
  create_time: string;

  @ApiProperty({ description: '通知类型' })
  @IsString()
  @IsNotEmpty()
  event_type: string;

  @ApiProperty({ description: '通知数据类型' })
  @IsString()
  @IsNotEmpty()
  resource_type: string;

  @ApiProperty({ description: '通知数据' })
  @IsNotEmpty()
  resource: {
    algorithm: string;
    ciphertext: string;
    associated_data: string;
    nonce: string;
  };

  @ApiPropertyOptional({ description: '摘要' })
  @IsOptional()
  @IsString() 
  summary?: string;
}

export interface TransactionResource {
  appid: string;
  mchid: string;
  out_trade_no: string;
  transaction_id: string;
  trade_type: string;
  trade_state: string;
  trade_state_desc: string;
  bank_type: string;
  attach: string;
  success_time: string;
  payer: {
    openid: string;
  };
  amount: {
    total: number;
    payer_total: number;
    currency: string;
    payer_currency: string;
  };
} 