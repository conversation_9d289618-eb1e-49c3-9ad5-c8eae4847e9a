import { IsNotEmpty, IsString, IsOptional, IsEnum, IsUUID } from 'class-validator';
import { ApiProperty } from '@nestjs/swagger';
import { ConversationStatus, BotType } from '../entities/conversation.entity';

export class CreateConversationDto {
    @IsNotEmpty()
    @IsString()
    @ApiProperty({ description: '对话标题' })
    title: string;

    @IsOptional()
    @IsString()
    @ApiProperty({ description: '初始消息', required: false })
    initialMessage?: string;

    @IsOptional()
    @IsEnum(BotType)
    @ApiProperty({ enum: BotType, description: '智能体类型', default: BotType.DEFAULT })
    botType?: BotType;

    @IsOptional()
    @IsString()
    @ApiProperty({ description: '使用的区域', default: 'zh' })
    region?: string;

    @IsOptional()
    @ApiProperty({ description: '对话元数据', required: false })
    metadata?: Record<string, any>;
}

export class UpdateConversationDto {
    @IsOptional()
    @IsString()
    @ApiProperty({ description: '对话标题', required: false })
    title?: string;

    @IsOptional()
    @IsEnum(ConversationStatus)
    @ApiProperty({ enum: ConversationStatus, description: '对话状态', required: false })
    status?: ConversationStatus;

    @IsOptional()
    @ApiProperty({ description: '对话元数据', required: false })
    metadata?: Record<string, any>;
}

export class ConversationListDto {
    @IsOptional()
    @IsEnum(ConversationStatus)
    @ApiProperty({ enum: ConversationStatus, description: '对话状态过滤', required: false })
    status?: ConversationStatus;

    @IsOptional()
    @ApiProperty({ description: '页码', default: 1 })
    page?: number = 1;

    @IsOptional()
    @ApiProperty({ description: '页大小', default: 20 })
    pageSize?: number = 20;
}