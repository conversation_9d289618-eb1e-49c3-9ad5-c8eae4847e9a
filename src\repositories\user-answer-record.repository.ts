import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { UserAnswerRecord } from '../modules/question/entities/user-answer-record.entity';

@Injectable()
export class UserAnswerRecordRepository {
  constructor(
    @InjectRepository(UserAnswerRecord)
    private userAnswerRecordRepository: Repository<UserAnswerRecord>
  ) { }

  async findByUserId(userId: number): Promise<UserAnswerRecord[]> {
    return this.userAnswerRecordRepository.find({
      where: { userId },
      order: { createdAt: 'DESC' }
    });
  }

  async findByUserIdWithPagination(userId: number, page: number, pageSize: number): Promise<{
    records: UserAnswerRecord[];
    total: number;
  }> {
    const skip = (page - 1) * pageSize;
    
    const [records, total] = await this.userAnswerRecordRepository.findAndCount({
      where: { userId },
      order: { createdAt: 'DESC' },
      skip,
      take: pageSize
    });

    return { records, total };
  }

  async findCurrentByUserId(userId: number): Promise<UserAnswerRecord | null> {
    return this.userAnswerRecordRepository.findOne({
      where: { userId, isCurrent: true }
    });
  }

  async findLatestByUserId(userId: number): Promise<UserAnswerRecord | null> {
    return this.userAnswerRecordRepository.findOne({
      where: { userId },
      order: { createdAt: 'DESC' }
    });
  }

  async create(data: Partial<UserAnswerRecord>): Promise<UserAnswerRecord> {
    const userAnswerRecord = this.userAnswerRecordRepository.create(data);
    return this.userAnswerRecordRepository.save(userAnswerRecord);
  }

  async updateCurrentStatus(userId: number, isCurrent: boolean): Promise<void> {
    await this.userAnswerRecordRepository.update(
      { userId, isCurrent: true },
      { isCurrent: false }
    );
  }

  async countByUserId(userId: number): Promise<number> {
    return this.userAnswerRecordRepository.count({
      where: { userId }
    });
  }

  async delete(id: number): Promise<void> {
    await this.userAnswerRecordRepository.delete(id);
  }

  async deleteByUserId(userId: number): Promise<void> {
    await this.userAnswerRecordRepository.delete({ userId });
  }
} 