import {
  Controller,
  Get,
  Post,
  Put,
  Param,
  Query,
  Body,
  Logger,
  HttpStatus,
  HttpException,
  ParseIntPipe,
  ValidationPipe,
  UsePipes,
} from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse, ApiParam, ApiQuery } from '@nestjs/swagger';
import { PaymentService } from './payment.service';
import { CreatePaymentRequestDto, CreateWechatPaymentDto, CreatePaymentResponseDto } from './dto/create-payment-request.dto';
import { UpdatePaymentStatusDto, PaymentQueryDto, PaymentResponseDto, PaymentStatsResponseDto } from './dto/update-payment-status.dto';
import { PaymentVerificationDto } from './dto/create-payment-request.dto';

@ApiTags('支付管理')
@Controller('payment')
export class PaymentController {
  private readonly logger = new Logger(PaymentController.name);

  constructor(private readonly paymentService: PaymentService) { }

  /**
   * 创建支付订单（新版本）
   */
  @Post('create')
  @ApiOperation({ summary: '创建支付订单', description: '创建新的支付订单并返回支付参数' })
  @ApiResponse({ status: 201, description: '支付订单创建成功', type: CreatePaymentResponseDto })
  @ApiResponse({ status: 400, description: '请求参数错误' })
  @ApiResponse({ status: 500, description: '服务器内部错误' })
  @UsePipes(new ValidationPipe({ transform: true }))
  async createPaymentOrder(@Body() createPaymentDto: CreatePaymentRequestDto): Promise<CreatePaymentResponseDto> {
    try {
      this.logger.log(`收到创建支付请求 - UserId: ${createPaymentDto.userId}, Type: ${createPaymentDto.type}, Amount: ${createPaymentDto.amount}`);

      const result = await this.paymentService.createPaymentOrder(createPaymentDto);

      this.logger.log(`支付订单创建成功 - PaymentId: ${result.paymentId}, OrderNo: ${result.orderNo}`);
      return result;
    } catch (error) {
      this.logger.error(`创建支付订单失败: ${error.message}`);
      throw new HttpException(
        {
          status: HttpStatus.INTERNAL_SERVER_ERROR,
          error: error.message,
          message: '创建支付订单失败'
        },
        HttpStatus.INTERNAL_SERVER_ERROR
      );
    }
  }

  /**
   * 创建微信支付订单
   */
  @Post('wechat/create')
  @ApiOperation({ summary: '创建微信支付订单', description: '创建微信支付订单并返回支付参数' })
  @ApiResponse({ status: 201, description: '微信支付订单创建成功', type: CreatePaymentResponseDto })
  @ApiResponse({ status: 400, description: '请求参数错误' })
  @ApiResponse({ status: 500, description: '服务器内部错误' })
  @UsePipes(new ValidationPipe({ transform: true }))
  async createWechatPayment(@Body() createWechatPaymentDto: CreateWechatPaymentDto): Promise<CreatePaymentResponseDto> {
    try {
      this.logger.log(`收到创建微信支付请求 - UserId: ${createWechatPaymentDto.userId}, OpenId: ${createWechatPaymentDto.openid}`);

      const result = await this.paymentService.createPaymentOrder(createWechatPaymentDto);

      this.logger.log(`微信支付订单创建成功 - PaymentId: ${result.paymentId}, OrderNo: ${result.orderNo}`);
      return result;
    } catch (error) {
      this.logger.error(`创建微信支付订单失败: ${error.message}`);
      throw new HttpException(
        {
          status: HttpStatus.INTERNAL_SERVER_ERROR,
          error: error.message,
          message: '创建微信支付订单失败'
        },
        HttpStatus.INTERNAL_SERVER_ERROR
      );
    }
  }

  /**
   * 查询支付记录列表
   */
  @Get('list')
  @ApiOperation({ summary: '查询支付记录列表', description: '分页查询支付记录列表' })
  @ApiResponse({ status: 200, description: '查询成功' })
  @ApiResponse({ status: 400, description: '请求参数错误' })
  @UsePipes(new ValidationPipe({ transform: true }))
  async getPaymentList(@Query() queryDto: PaymentQueryDto) {
    try {
      this.logger.log(`查询支付记录列表 - 查询条件: ${JSON.stringify(queryDto)}`);

      const result = await this.paymentService.queryPayments(queryDto);

      this.logger.log(`支付记录查询成功 - 总数: ${result.total}, 当前页: ${result.page}`);
      return {
        status: 200,
        message: '查询成功',
        data: result
      };
    } catch (error) {
      this.logger.error(`查询支付记录失败: ${error.message}`);
      throw new HttpException(
        {
          status: HttpStatus.INTERNAL_SERVER_ERROR,
          error: error.message,
          message: '查询支付记录失败'
        },
        HttpStatus.INTERNAL_SERVER_ERROR
      );
    }
  }

  /**
   * 根据ID获取支付记录
   */
  @Get(':id')
  @ApiOperation({ summary: '获取支付记录详情', description: '根据支付记录ID获取详细信息' })
  @ApiParam({ name: 'id', description: '支付记录ID', type: 'number' })
  @ApiResponse({ status: 200, description: '获取成功', type: PaymentResponseDto })
  @ApiResponse({ status: 404, description: '支付记录不存在' })
  async getPaymentById(@Param('id', ParseIntPipe) id: number): Promise<PaymentResponseDto> {
    try {
      this.logger.log(`获取支付记录详情 - ID: ${id}`);

      const result = await this.paymentService.getPaymentById(id);

      this.logger.log(`支付记录获取成功 - ID: ${id}, OrderNo: ${result.orderNo}`);
      return result;
    } catch (error) {
      this.logger.error(`获取支付记录失败: ${error.message}`);
      if (error.message.includes('不存在')) {
        throw new HttpException(
          {
            status: HttpStatus.NOT_FOUND,
            error: error.message,
            message: '支付记录不存在'
          },
          HttpStatus.NOT_FOUND
        );
      }
      throw new HttpException(
        {
          status: HttpStatus.INTERNAL_SERVER_ERROR,
          error: error.message,
          message: '获取支付记录失败'
        },
        HttpStatus.INTERNAL_SERVER_ERROR
      );
    }
  }

  /**
   * 根据订单号获取支付记录
   */
  @Get('order/:orderNo')
  @ApiOperation({ summary: '根据订单号获取支付记录', description: '根据订单号获取支付记录详细信息' })
  @ApiParam({ name: 'orderNo', description: '订单号', type: 'string' })
  @ApiResponse({ status: 200, description: '获取成功', type: PaymentResponseDto })
  @ApiResponse({ status: 404, description: '支付记录不存在' })
  async getPaymentByOrderNo(@Param('orderNo') orderNo: string): Promise<PaymentResponseDto> {
    try {
      this.logger.log(`根据订单号获取支付记录 - OrderNo: ${orderNo}`);

      const result = await this.paymentService.getPaymentByOrderNo(orderNo);

      this.logger.log(`支付记录获取成功 - OrderNo: ${orderNo}, ID: ${result.id}`);
      return result;
    } catch (error) {
      this.logger.error(`获取支付记录失败: ${error.message}`);
      if (error.message.includes('不存在')) {
        throw new HttpException(
          {
            status: HttpStatus.NOT_FOUND,
            error: error.message,
            message: '支付记录不存在'
          },
          HttpStatus.NOT_FOUND
        );
      }
      throw new HttpException(
        {
          status: HttpStatus.INTERNAL_SERVER_ERROR,
          error: error.message,
          message: '获取支付记录失败'
        },
        HttpStatus.INTERNAL_SERVER_ERROR
      );
    }
  }

  // 保留原有的创建支付方法以保持向后兼容
  @Get('create')
  async createPayment(
    @Query('openid') openid: string,
    @Query('amount') amount?: number,
    @Query('description') description?: string,
  ) {
    this.logger.log(`收到创建支付请求 - openid: ${openid}`);

    if (!openid) {
      return {
        status: 400,
        error: 'openid参数不能为空',
        message: '输入源"/body/payer/openid"映射到字段"用户标识"必填性规则校验失败，此字段为必填项'
      };
    }

    try {
      const result = await this.paymentService.createPayment(
        openid,
        amount || 1,
        description || "Asnull的支付测试"
      );

      return result;
    } catch (error) {
      this.logger.error(`创建支付失败: ${error.message}`);
      return {
        status: 500,
        error: error.message,
        message: '创建支付订单失败'
      };
    }
  }

  /**
   * 更新支付状态
   */
  @Put(':id/status')
  @ApiOperation({ summary: '更新支付状态', description: '手动更新支付记录状态' })
  @ApiParam({ name: 'id', description: '支付记录ID', type: 'number' })
  @ApiResponse({ status: 200, description: '更新成功' })
  @ApiResponse({ status: 404, description: '支付记录不存在' })
  @UsePipes(new ValidationPipe({ transform: true }))
  async updatePaymentStatus(
    @Param('id', ParseIntPipe) id: number,
    @Body() updateStatusDto: UpdatePaymentStatusDto
  ) {
    try {
      this.logger.log(`更新支付状态 - ID: ${id}, Status: ${updateStatusDto.status}`);

      await this.paymentService.updatePaymentStatus(id, updateStatusDto);

      this.logger.log(`支付状态更新成功 - ID: ${id}`);
      return {
        status: 200,
        message: '支付状态更新成功'
      };
    } catch (error) {
      this.logger.error(`更新支付状态失败: ${error.message}`);
      if (error.message.includes('不存在')) {
        throw new HttpException(
          {
            status: HttpStatus.NOT_FOUND,
            error: error.message,
            message: '支付记录不存在'
          },
          HttpStatus.NOT_FOUND
        );
      }
      throw new HttpException(
        {
          status: HttpStatus.INTERNAL_SERVER_ERROR,
          error: error.message,
          message: '更新支付状态失败'
        },
        HttpStatus.INTERNAL_SERVER_ERROR
      );
    }
  }

  /**
   * 获取用户支付统计
   */
  @Get('user/:userId/stats')
  @ApiOperation({ summary: '获取用户支付统计', description: '获取指定用户的支付统计信息' })
  @ApiParam({ name: 'userId', description: '用户ID', type: 'number' })
  @ApiResponse({ status: 200, description: '获取成功', type: PaymentStatsResponseDto })
  async getUserPaymentStats(@Param('userId', ParseIntPipe) userId: number): Promise<PaymentStatsResponseDto> {
    try {
      this.logger.log(`获取用户支付统计 - UserId: ${userId}`);

      const result = await this.paymentService.getUserPaymentStats(userId);

      this.logger.log(`用户支付统计获取成功 - UserId: ${userId}, TotalPayments: ${result.totalPayments}`);
      return result;
    } catch (error) {
      this.logger.error(`获取用户支付统计失败: ${error.message}`);
      throw new HttpException(
        {
          status: HttpStatus.INTERNAL_SERVER_ERROR,
          error: error.message,
          message: '获取用户支付统计失败'
        },
        HttpStatus.INTERNAL_SERVER_ERROR
      );
    }
  }

  /**
   * 验证支付状态
   */
  @Post('verify')
  @ApiOperation({ summary: '验证支付状态', description: '验证支付记录的当前状态' })
  @ApiResponse({ status: 200, description: '验证成功', type: PaymentResponseDto })
  @ApiResponse({ status: 404, description: '支付记录不存在' })
  @UsePipes(new ValidationPipe({ transform: true }))
  async verifyPaymentStatus(@Body() verificationDto: PaymentVerificationDto): Promise<PaymentResponseDto> {
    try {
      this.logger.log(`验证支付状态 - OrderNo: ${verificationDto.orderNo}`);

      const result = await this.paymentService.verifyPaymentStatus(verificationDto);

      this.logger.log(`支付状态验证成功 - OrderNo: ${verificationDto.orderNo}, Status: ${result.status}`);
      return result;
    } catch (error) {
      this.logger.error(`验证支付状态失败: ${error.message}`);
      if (error.message.includes('不存在')) {
        throw new HttpException(
          {
            status: HttpStatus.NOT_FOUND,
            error: error.message,
            message: '支付记录不存在'
          },
          HttpStatus.NOT_FOUND
        );
      }
      throw new HttpException(
        {
          status: HttpStatus.INTERNAL_SERVER_ERROR,
          error: error.message,
          message: '验证支付状态失败'
        },
        HttpStatus.INTERNAL_SERVER_ERROR
      );
    }
  }

  /**
   * 支付回调处理
   */
  @Post('notify')
  @ApiOperation({ summary: '支付回调处理', description: '处理第三方支付平台的回调通知' })
  @ApiResponse({ status: 200, description: '回调处理成功' })
  async handlePaymentNotify(@Body() callbackData: any) {
    this.logger.log(`收到支付回调: ${JSON.stringify(callbackData)}`);

    try {
      const result = await this.paymentService.handlePaymentCallback(callbackData);
      return result;
    } catch (error) {
      this.logger.error(`处理支付回调失败: ${error.message}`);
      return {
        success: false,
        message: '处理支付回调失败'
      };
    }
  }

  /**
   * 健康检查
   */
  @Get()
  @ApiOperation({ summary: '健康检查', description: '支付模块健康检查' })
  @ApiResponse({ status: 200, description: '服务正常' })
  getHello(): string {
    return 'Hello Payment Module!';
  }

  /**
   * 管理员测试接口
   */
  @Get('admin/test')
  @ApiOperation({ summary: '管理员测试接口', description: '支付模块管理员测试接口' })
  @ApiResponse({ status: 200, description: '测试成功' })
  adminTest() {
    this.logger.log('支付模块管理员测试接口被调用');
    return {
      status: 200,
      message: '支付模块运行正常',
      timestamp: new Date().toISOString(),
      module: 'PaymentModule',
      version: '2.0.0'
    };
  }
}