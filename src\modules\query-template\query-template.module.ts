import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { QueryTemplate } from './entities/query-template.entity';
import { DateQuery } from './entities/date-query.entity';
import { QueryTemplateService } from './query-template.service';
import { DateQueryService } from './date-query.service';
import { QueryTemplateController } from './query-template.controller';
import { DateQueryController } from './date-query.controller';

@Module({
  imports: [TypeOrmModule.forFeature([QueryTemplate, DateQuery])],
  providers: [QueryTemplateService, DateQueryService],
  controllers: [QueryTemplateController, DateQueryController],
  exports: [QueryTemplateService, DateQueryService],
})
export class QueryTemplateModule {}  