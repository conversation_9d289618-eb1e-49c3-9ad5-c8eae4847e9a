import { Controller, Get, Post, Put, Delete, Body, Param, ParseIntPipe } from '@nestjs/common';
import { QueryTemplateService } from './query-template.service';
import { QueryTemplate } from './entities/query-template.entity';
import { CreateQueryTemplateDto } from './dto/create-query-template.dto';

@Controller('query-templates')
export class QueryTemplateController {
  constructor(private readonly queryTemplateService: QueryTemplateService) {}

  @Post()
  async create(@Body() templateData: CreateQueryTemplateDto) {
    return await this.queryTemplateService.create(templateData);
  }

  @Get()
  async findAll() {
    return await this.queryTemplateService.findAll();
  }

  @Get(':id')
  async findOne(@Param('id', ParseIntPipe) id: number) {
    return await this.queryTemplateService.findOne(id);
  }

  @Put(':id')
  async update(@Param('id', ParseIntPipe) id: number, @Body() templateData: Partial<QueryTemplate>) {
    return await this.queryTemplateService.update(id, templateData);
  }

  @Delete(':id')
  async remove(@Param('id', ParseIntPipe) id: number) {
    return await this.queryTemplateService.remove(id);
  }
} 