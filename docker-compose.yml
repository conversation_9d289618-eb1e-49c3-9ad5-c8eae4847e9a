version: '3.8'
services:
  app:
    build: .
    ports:
      - "3000:3000"
    environment:
      - NODE_ENV=production
      - DB_HOST=mysql
      - DB_PORT=3306
      - DB_USERNAME=root
      - DB_PASSWORD=root
      - DB_NAME=kanli
      - REDIS_HOST=redis
      - REDIS_PORT=6379
    depends_on:
      - mysql
      - redis
    networks:
      - kanli-network

  mysql:
    image: mysql:8.0
    environment:
      - MYSQL_ROOT_PASSWORD=yourpassword
      - MYSQL_DATABASE=kanli
    volumes:
      - mysql-data:/var/lib/mysql
    networks:
      - kanli-network

  redis:
    image: redis:7-alpine
    networks:
      - kanli-network

volumes:
  mysql-data:

networks:
  kanli-network: