{"openapi": "3.0.0", "paths": {"/": {"get": {"operationId": "AppController_get<PERSON><PERSON>", "parameters": [], "responses": {"200": {"description": ""}}, "tags": ["App"]}}, "/api-json": {"get": {"description": "返回完整的 OpenAPI 3.0 JSON 规范文档，不包含响应包装格式", "operationId": "AppController_getSwagger<PERSON>son", "parameters": [], "responses": {"200": {"description": "返回原始的 OpenAPI JSON 规范", "content": {"application/json": {"schema": {"type": "object", "properties": {"openapi": {"type": "string", "example": "3.0.0"}, "info": {"type": "object", "properties": {"title": {"type": "string", "example": "KanLi API"}, "version": {"type": "string", "example": "1.0"}}}, "paths": {"type": "object"}}}}}}}, "summary": "获取 Swagger JSON 规范", "tags": ["App"]}}, "/health": {"get": {"operationId": "HealthController_checkHealth", "parameters": [], "responses": {"200": {"description": "健康检查结果", "content": {"application/json": {"schema": {"example": {"code": 200, "message": "请求成功", "data": {"database": {"status": "connected", "config": {"type": "mysql", "host": "localhost", "port": 3306, "database": "kanli"}}, "redis": {"status": "connected", "config": {"host": "localhost", "port": 6379, "db": 0}}, "overall": "healthy"}}}}}}}, "summary": "检查所有服务健康状态", "tags": ["健康检查"]}}, "/health/simple": {"get": {"operationId": "HealthController_simpleHealthCheck", "parameters": [], "responses": {"200": {"description": "简单检查结果", "content": {"application/json": {"schema": {"example": {"code": 200, "message": "请求成功", "data": {"status": "healthy", "timestamp": "2024-01-15T10:30:00.000Z"}}}}}}}, "summary": "简单健康检查", "tags": ["健康检查"]}}, "/health/database": {"get": {"operationId": "HealthController_checkDatabase", "parameters": [], "responses": {"200": {"description": ""}}, "summary": "检查数据库连接", "tags": ["健康检查"]}}, "/health/redis": {"get": {"operationId": "HealthController_checkRedis", "parameters": [], "responses": {"200": {"description": ""}}, "summary": "检查Redis连接", "tags": ["健康检查"]}}, "/captcha/send": {"post": {"operationId": "CaptchaController_sendCaptcha", "parameters": [], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/SendCaptchaDto"}}}}, "responses": {"200": {"description": "验证码发送成功", "content": {"application/json": {"schema": {"example": {"code": 200, "message": "请求成功", "data": {"phoneNumber": "13800138000", "message": "验证码已发送，请注意查收", "expiresIn": 300}}}}}}, "400": {"description": "请求参数错误或发送过于频繁"}}, "summary": "发送验证码", "tags": ["验证码"]}}, "/captcha/check-rate-limit": {"get": {"operationId": "CaptchaController_checkRateLimit", "parameters": [{"name": "phoneNumber", "required": true, "in": "query", "schema": {"type": "string"}}], "responses": {"200": {"description": "查询成功", "content": {"application/json": {"schema": {"example": {"code": 200, "message": "请求成功", "data": {"isLimited": true, "remainingTime": 30}}}}}}}, "summary": "检查发送频率限制", "tags": ["验证码"]}}, "/captcha/remaining-time": {"get": {"operationId": "CaptchaController_getRemainingTime", "parameters": [{"name": "phoneNumber", "required": true, "in": "query", "schema": {"type": "string"}}], "responses": {"200": {"description": "查询成功", "content": {"application/json": {"schema": {"example": {"code": 200, "message": "请求成功", "data": {"remainingTime": 240}}}}}}}, "summary": "获取验证码剩余有效时间", "tags": ["验证码"]}}, "/users/register": {"post": {"operationId": "UsersController_register", "parameters": [], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateUserDto"}}}}, "responses": {"201": {"description": "注册成功", "content": {"application/json": {"schema": {"example": {"code": 200, "message": "请求成功", "data": {"user": {"id": 1, "name": "张三", "email": "<EMAIL>", "phoneNumber": "13800138000", "role": "user", "userType": "individual", "isActive": true, "membershipType": "free", "freeAssessmentCount": 10, "freeAIInterpretationCount": 10, "createdAt": "2024-01-15T10:30:00.000Z"}, "message": "注册成功"}}}}}}, "400": {"description": "注册失败，参数错误或验证码无效"}}, "summary": "注册用户", "tags": ["用户"]}}, "/users/login": {"post": {"operationId": "UsersController_login", "parameters": [], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/LoginUserDto"}}}}, "responses": {"200": {"description": "登录成功", "content": {"application/json": {"schema": {"example": {"code": 200, "message": "请求成功", "data": {"user": {"id": 1, "name": "张三", "email": "<EMAIL>", "phoneNumber": "13800138000", "role": "user", "userType": "individual", "membershipType": "free"}, "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...", "message": "登录成功"}}}}}}, "400": {"description": "登录失败，用户不存在或密码错误"}}, "summary": "用户登录", "tags": ["用户"]}}, "/users/wechat-login": {"post": {"operationId": "UsersController_wechatLogin", "parameters": [], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WechatLoginDto"}}}}, "responses": {"200": {"description": "微信登录成功", "schema": {"example": {"code": 200, "message": "请求成功", "data": {"user": {"id": 1, "name": "微信用户_abc123", "openidWx": "oabc123456789", "role": "user", "userType": "individual", "membershipType": "free", "permissionLevel": "individual", "freeAssessmentCount": 10, "freeAIInterpretationCount": 10, "isActive": true, "createdAt": "2024-01-15T10:30:00.000Z"}, "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...", "message": "登录成功", "isNewUser": false}}}, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WechatLoginResponseDto"}}}}, "400": {"description": "微信登录失败，无法获取用户信息或验证失败"}}, "summary": "微信小程序一键登录", "tags": ["用户"]}}, "/users/decrypt-user-info": {"post": {"operationId": "UsersController_decryptUserInfo", "parameters": [], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/DecryptUserInfoDto"}}}}, "responses": {"200": {"description": "解密成功", "schema": {"example": {"code": 200, "message": "请求成功", "data": {"phoneNumber": "13800138000", "purePhoneNumber": "13800138000", "countryCode": "86", "watermark": {"timestamp": 1477314187, "appid": "wx4f4bc4dec97d474b"}}}}, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/DecryptedUserInfoResponseDto"}}}}, "400": {"description": "解密失败，参数错误或微信API调用失败"}}, "summary": "解密微信用户信息", "tags": ["用户"]}}, "/users/logout": {"post": {"operationId": "UsersController_logout", "parameters": [{"name": "X-JWT-Token", "required": true, "in": "header", "description": "JWT Bearer token", "schema": {"type": "string"}}], "responses": {"200": {"description": "退出登录成功", "schema": {"example": {"code": 200, "message": "请求成功", "data": {"message": "退出登录成功", "timestamp": "2024-01-15T10:30:00.000Z"}}}, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/LogoutResponseDto"}}}}, "400": {"description": "请求头缺少X-JWT-Token"}, "401": {"description": "未授权，token无效或已过期"}}, "security": [{"bearer": []}], "summary": "退出登录", "tags": ["用户"]}}, "/users/profile": {"get": {"operationId": "UsersController_getProfile", "parameters": [], "responses": {"200": {"description": "获取成功", "schema": {"example": {"code": 200, "message": "请求成功", "data": {"user": {"id": 1, "name": "张三", "email": "<EMAIL>", "phoneNumber": "13800138000", "role": "user", "userType": "individual", "membershipType": "free", "freeAssessmentCount": 8, "freeAIInterpretationCount": 5, "isActive": true, "createdAt": "2024-01-15T10:30:00.000Z"}}}}, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UserResponseDto"}}}}, "401": {"description": "未授权，token无效、已过期或已退出"}}, "security": [{"bearer": []}], "summary": "获取用户个人信息 (需要登录)", "tags": ["用户"]}}, "/users/check-usage": {"post": {"operationId": "UsersController_checkUsage", "parameters": [], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UsageCheckDto"}}}}, "responses": {"200": {"description": "检查成功", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UsageCheckResponseDto"}}}}, "401": {"description": "未授权"}}, "security": [{"bearer": []}], "summary": "检查使用次数权限 (需要登录)", "tags": ["用户"]}}, "/users/consume-usage": {"post": {"operationId": "UsersController_consumeUsage", "parameters": [], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UsageCheckDto"}}}}, "responses": {"200": {"description": "消费成功", "content": {"application/json": {"schema": {"example": {"code": 200, "message": "请求成功", "data": {"success": true, "message": "使用成功", "remainingCount": 9}}}}}}, "400": {"description": "次数不足或需要付费"}, "401": {"description": "未授权"}}, "security": [{"bearer": []}], "summary": "消费使用次数 (需要登录)", "tags": ["用户"]}}, "/users/membership": {"get": {"operationId": "UsersController_getMembershipStatus", "parameters": [], "responses": {"200": {"description": "获取成功", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/MembershipStatusDto"}}}}, "401": {"description": "未授权"}}, "security": [{"bearer": []}], "summary": "获取会员状态 (需要登录)", "tags": ["用户"]}}, "/users/membership/{id}": {"patch": {"operationId": "UsersController_updateMembership", "parameters": [{"name": "id", "required": true, "in": "path", "schema": {"type": "string"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UpdateMembershipDto"}}}}, "responses": {"200": {"description": "更新成功"}, "401": {"description": "未授权"}, "403": {"description": "权限不足"}}, "security": [{"bearer": []}], "summary": "更新会员信息 (需要团队权限)", "tags": ["用户"]}}, "/users": {"get": {"operationId": "UsersController_findAll", "parameters": [], "responses": {"200": {"description": "获取成功"}, "403": {"description": "权限不足"}}, "security": [{"bearer": []}], "summary": "获取所有用户 (需要团队权限)", "tags": ["用户"]}}, "/users/{id}": {"get": {"operationId": "UsersController_findOne", "parameters": [{"name": "id", "required": true, "in": "path", "schema": {"type": "string"}}], "responses": {"200": {"description": "获取成功", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UserResponseDto"}}}}, "400": {"description": "用户不存在"}, "401": {"description": "未授权"}}, "security": [{"bearer": []}], "summary": "根据ID获取用户 (需要登录)", "tags": ["用户"]}, "patch": {"operationId": "UsersController_update", "parameters": [{"name": "id", "required": true, "in": "path", "schema": {"type": "string"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UpdateUserDto"}}}}, "responses": {"200": {"description": "更新成功"}, "400": {"description": "用户不存在或出生日期已锁定"}, "401": {"description": "未授权"}}, "security": [{"bearer": []}], "summary": "更新用户信息 (需要登录)", "tags": ["用户"]}, "delete": {"operationId": "UsersController_remove", "parameters": [{"name": "id", "required": true, "in": "path", "schema": {"type": "string"}}], "responses": {"200": {"description": "删除成功"}, "400": {"description": "用户不存在"}, "401": {"description": "未授权"}, "403": {"description": "权限不足"}}, "security": [{"bearer": []}], "summary": "删除用户 (需要团队权限)", "tags": ["用户"]}}, "/jwt-test/verify-token": {"post": {"operationId": "JwtTestController_verifyToken", "parameters": [], "responses": {"200": {"description": "Token验证成功", "content": {"application/json": {"schema": {"example": {"code": 200, "message": "请求成功", "data": {"valid": true, "payload": {"sub": "1", "name": "张三", "email": "<EMAIL>", "role": "user", "iat": 1642248000, "exp": 1642251600}, "issuedAt": "2024-01-15T10:00:00.000Z", "expiresAt": "2024-01-15T11:00:00.000Z", "timeToExpire": "45分钟23秒"}}}}}}, "401": {"description": "Token无效或已过期"}}, "security": [{"bearer": []}], "summary": "验证JWT token是否有效", "tags": ["JWT测试"]}}, "/jwt-test/test-auth": {"get": {"operationId": "JwtTestController_testAuth", "parameters": [], "responses": {"200": {"description": "认证成功", "content": {"application/json": {"schema": {"example": {"code": 200, "message": "请求成功", "data": {"authenticated": true, "user": {"sub": "1", "name": "张三", "email": "<EMAIL>", "role": "user", "iat": 1642248000, "exp": 1642251600}, "message": "JWT认证守卫测试通过"}}}}}}, "401": {"description": "未授权，token无效或已过期"}}, "security": [{"bearer": []}], "summary": "测试JWT认证守卫 (需要登录)", "tags": ["JWT测试"]}}, "/jwt-test/refresh-token": {"post": {"operationId": "JwtTestController_refreshToken", "parameters": [], "responses": {"200": {"description": "Token刷新成功", "content": {"application/json": {"schema": {"example": {"code": 200, "message": "请求成功", "data": {"oldToken": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...", "newToken": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...", "expiresAt": "2024-01-15T12:00:00.000Z", "message": "Token刷新成功"}}}}}}, "401": {"description": "未授权"}}, "security": [{"bearer": []}], "summary": "刷新JWT token (需要登录)", "tags": ["JWT测试"]}}, "/jwt-test/token-info": {"get": {"operationId": "JwtTestController_getTokenInfo", "parameters": [], "responses": {"200": {"description": "获取成功", "content": {"application/json": {"schema": {"example": {"code": 200, "message": "请求成功", "data": {"token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...", "payload": {"sub": "1", "name": "张三", "email": "<EMAIL>", "role": "user", "iat": 1642248000, "exp": 1642251600}, "header": {"alg": "HS256", "typ": "JWT"}, "issuedAt": "2024-01-15T10:00:00.000Z", "expiresAt": "2024-01-15T11:00:00.000Z", "timeToExpire": "45分钟23秒", "isBlacklisted": false}}}}}}, "401": {"description": "未授权"}}, "security": [{"bearer": []}], "summary": "获取当前token详细信息 (需要登录)", "tags": ["JWT测试"]}}, "/jwt-test/test-permissions": {"get": {"operationId": "JwtTestController_testPermissions", "parameters": [], "responses": {"200": {"description": "权限测试成功", "content": {"application/json": {"schema": {"example": {"code": 200, "message": "请求成功", "data": {"userId": 1, "role": "user", "permissions": {"canAccessProfile": true, "canCreateInvite": false, "canMakePayment": true, "hasTeamPermission": false, "hasIndividualPermission": true}, "userInfo": {"id": 1, "name": "张三", "email": "<EMAIL>", "membershipType": "free", "permissionLevel": "individual"}, "message": "权限测试完成"}}}}}}, "401": {"description": "未授权"}}, "security": [{"bearer": []}], "summary": "测试用户权限信息 (需要登录)", "tags": ["JWT测试"]}}, "/jwt-test/generate-test-token": {"post": {"operationId": "JwtTestController_generateTestToken", "parameters": [], "responses": {"200": {"description": "Token生成成功", "content": {"application/json": {"schema": {"example": {"code": 200, "message": "请求成功", "data": {"token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...", "payload": {"sub": "999", "name": "测试用户", "email": "<EMAIL>", "role": "user"}, "expiresAt": "2024-01-15T11:00:00.000Z", "message": "测试Token生成成功"}}}}}}, "400": {"description": "参数错误"}}, "summary": "生成测试用的JWT token (仅用于开发测试)", "tags": ["JWT测试"]}}, "/cards": {"post": {"operationId": "CardsController_create", "parameters": [], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateCardDto"}}}}, "responses": {"201": {"description": ""}}, "tags": ["Cards"]}, "get": {"operationId": "CardsController_findAll", "parameters": [], "responses": {"200": {"description": ""}}, "tags": ["Cards"]}}, "/cards/{id}": {"get": {"operationId": "CardsController_findOne", "parameters": [{"name": "id", "required": true, "in": "path", "schema": {"type": "string"}}], "responses": {"200": {"description": ""}}, "tags": ["Cards"]}, "patch": {"operationId": "CardsController_update", "parameters": [{"name": "id", "required": true, "in": "path", "schema": {"type": "string"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UpdateCardDto"}}}}, "responses": {"200": {"description": ""}}, "tags": ["Cards"]}, "delete": {"operationId": "CardsController_remove", "parameters": [{"name": "id", "required": true, "in": "path", "schema": {"type": "string"}}], "responses": {"200": {"description": ""}}, "tags": ["Cards"]}}, "/coze/conversations": {"post": {"description": "为当前用户创建一个新的AI聊天对话会话。每个对话会话都有唯一的ID，用于后续的消息交互。", "operationId": "CozeController_createConversation", "parameters": [], "requestBody": {"required": true, "description": "创建对话的请求参数", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateConversationDto"}, "examples": {"basic": {"summary": "基础对话创建", "description": "创建一个简单的对话会话", "value": {"title": "我的AI编程助手", "botType": "default"}}, "withInitialMessage": {"summary": "包含初始消息的对话", "description": "创建对话并发送第一条消息", "value": {"title": "深度学习讨论", "initialMessage": "你好，我想了解深度学习的基础概念", "botType": "default", "metadata": {"topic": "machine_learning", "difficulty": "beginner"}}}}}}}, "responses": {"201": {"description": "对话创建成功", "content": {"application/json": {"schema": {"type": "object", "properties": {"code": {"type": "number", "example": 200}, "message": {"type": "string", "example": "对话创建成功"}, "data": {"type": "object", "properties": {"id": {"type": "string", "example": "uuid"}, "title": {"type": "string", "example": "我的AI编程助手"}, "status": {"type": "string", "example": "active"}, "botType": {"type": "string", "example": "default"}, "messageCount": {"type": "number", "example": 0}, "createdAt": {"type": "string", "format": "date-time"}, "lastActiveAt": {"type": "string", "format": "date-time"}}}}}}}}}, "security": [{"JWT": []}], "summary": "创建新的AI对话会话（默认智能体）", "tags": ["AI聊天管理"]}, "get": {"description": "分页获取当前用户的所有AI对话会话列表，支持按创建时间倒序排列。", "operationId": "CozeController_getUserConversations", "parameters": [{"name": "page", "required": false, "in": "query", "description": "页码，从1开始", "schema": {"example": 1, "type": "number"}}, {"name": "pageSize", "required": false, "in": "query", "description": "每页显示数量，默认20条，最大100条", "schema": {"example": 20, "type": "number"}}], "responses": {"200": {"description": "对话列表获取成功", "content": {"application/json": {"schema": {"type": "object", "properties": {"data": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "string", "format": "uuid", "description": "对话唯一标识符"}, "title": {"type": "string", "description": "对话标题"}, "status": {"type": "string", "enum": ["active", "archived", "deleted"], "description": "对话状态"}, "messageCount": {"type": "number", "description": "消息总数"}, "lastMessageAt": {"type": "string", "format": "date-time", "description": "最后一条消息时间"}, "lastActiveAt": {"type": "string", "format": "date-time", "description": "最后活跃时间"}, "createdAt": {"type": "string", "format": "date-time", "description": "创建时间"}, "updatedAt": {"type": "string", "format": "date-time", "description": "更新时间"}, "metadata": {"type": "object", "description": "对话元数据"}}}}, "pagination": {"type": "object", "properties": {"total": {"type": "number", "description": "总记录数"}, "page": {"type": "number", "description": "当前页码"}, "pageSize": {"type": "number", "description": "每页大小"}, "totalPages": {"type": "number", "description": "总页数"}, "hasNext": {"type": "boolean", "description": "是否有下一页"}, "hasPrev": {"type": "boolean", "description": "是否有上一页"}}}}, "example": {"data": [{"id": "550e8400-e29b-41d4-a716-************", "title": "Python Web开发学习", "status": "active", "messageCount": 12, "lastMessageAt": "2024-01-15T14:30:00Z", "lastActiveAt": "2024-01-15T14:30:00Z", "createdAt": "2024-01-15T10:00:00Z", "updatedAt": "2024-01-15T14:30:00Z", "metadata": {"topic": "programming", "difficulty": "intermediate"}}, {"id": "123e4567-e89b-12d3-a456-426614174000", "title": "机器学习基础讨论", "status": "archived", "messageCount": 8, "lastMessageAt": "2024-01-14T16:45:00Z", "lastActiveAt": "2024-01-14T16:45:00Z", "createdAt": "2024-01-14T15:00:00Z", "updatedAt": "2024-01-14T17:00:00Z", "metadata": {"topic": "machine_learning", "completed": true}}], "pagination": {"total": 25, "page": 1, "pageSize": 20, "totalPages": 2, "hasNext": true, "hasPrev": false}}}}}}, "401": {"description": "用户未认证，请先登录"}, "403": {"description": "用户权限不足"}, "500": {"description": "服务器内部错误"}}, "security": [{"JWT": []}], "summary": "获取用户的AI对话列表", "tags": ["AI聊天管理"]}}, "/coze/interpretation/conversations": {"post": {"description": "为当前用户创建一个新的解读智能体对话会话。", "operationId": "CozeController_createInterpretationConversation", "parameters": [], "requestBody": {"required": true, "description": "创建对话的请求参数", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateConversationDto"}}}}, "responses": {"201": {"description": ""}}, "security": [{"JWT": []}], "summary": "创建新的AI对话会话（解读智能体）", "tags": ["AI聊天管理"]}, "get": {"description": "获取当前用户的所有解读智能体对话会话列表，支持分页查询。", "operationId": "CozeController_getInterpretationConversations", "parameters": [{"name": "page", "required": false, "in": "query", "description": "页码，默认为1", "schema": {"example": 1, "type": "number"}}, {"name": "pageSize", "required": false, "in": "query", "description": "每页数量，默认为20", "schema": {"example": 20, "type": "number"}}], "responses": {"200": {"description": ""}}, "security": [{"JWT": []}], "summary": "获取用户的解读智能体对话列表", "tags": ["AI聊天管理"]}}, "/coze/proto/conversations": {"post": {"description": "为当前用户创建一个新的原型智能体对话会话。", "operationId": "CozeController_createProtoConversation", "parameters": [], "requestBody": {"required": true, "description": "创建对话的请求参数", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateConversationDto"}}}}, "responses": {"201": {"description": ""}}, "security": [{"JWT": []}], "summary": "创建新的AI对话会话（原型智能体）", "tags": ["AI聊天管理"]}, "get": {"description": "获取当前用户的所有原型智能体对话会话列表，支持分页查询。", "operationId": "CozeController_getProtoConversations", "parameters": [{"name": "page", "required": false, "in": "query", "description": "页码，默认为1", "schema": {"example": 1, "type": "number"}}, {"name": "pageSize", "required": false, "in": "query", "description": "每页数量，默认为20", "schema": {"example": 20, "type": "number"}}], "responses": {"200": {"description": ""}}, "security": [{"JWT": []}], "summary": "获取用户的原型智能体对话列表", "tags": ["AI聊天管理"]}}, "/coze/conversations/{id}": {"get": {"description": "根据对话ID获取对话的详细信息，包括对话的基本信息和最近的消息概览。", "operationId": "CozeController_getConversation", "parameters": [{"name": "id", "required": true, "in": "path", "description": "对话的唯一标识符", "schema": {"format": "uuid", "example": "550e8400-e29b-41d4-a716-************", "type": "string"}}], "responses": {"200": {"description": "对话详情获取成功", "content": {"application/json": {"schema": {"type": "object", "properties": {"id": {"type": "string", "format": "uuid"}, "title": {"type": "string"}, "description": {"type": "string"}, "userId": {"type": "string", "format": "uuid"}, "messageCount": {"type": "number"}, "lastMessageAt": {"type": "string", "format": "date-time"}, "createdAt": {"type": "string", "format": "date-time"}, "updatedAt": {"type": "string", "format": "date-time"}}}}}}, "400": {"description": "对话ID格式错误"}, "401": {"description": "用户未认证，请先登录"}, "403": {"description": "无权限访问该对话"}, "404": {"description": "对话不存在"}, "500": {"description": "服务器内部错误"}}, "security": [{"JWT": []}], "summary": "获取指定对话的详细信息", "tags": ["AI聊天管理"]}, "put": {"description": "更新指定对话的标题、描述等基本信息。只有对话的创建者可以更新对话信息。", "operationId": "CozeController_updateConversation", "parameters": [{"name": "id", "required": true, "in": "path", "description": "对话的唯一标识符", "schema": {"format": "uuid", "example": "550e8400-e29b-41d4-a716-************", "type": "string"}}], "requestBody": {"required": true, "description": "更新对话的请求参数", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UpdateConversationDto"}, "examples": {"updateTitle": {"summary": "更新对话标题", "description": "仅更新对话的标题", "value": {"title": "新的对话标题 - Python Web开发"}}, "updateStatus": {"summary": "更新对话状态", "description": "将对话标记为已归档", "value": {"status": "archived"}}, "updateAll": {"summary": "更新所有可选字段", "description": "同时更新标题、状态和元数据", "value": {"title": "已完成的项目讨论", "status": "archived", "metadata": {"completedAt": "2024-01-15T10:30:00Z", "rating": 5, "tags": ["completed", "helpful", "programming"]}}}, "updateMetadata": {"summary": "仅更新元数据", "description": "添加或更新对话的元数据信息", "value": {"metadata": {"lastTopic": "API设计", "complexity": "intermediate", "estimatedDuration": "45分钟"}}}}}}}, "responses": {"200": {"description": "对话更新成功", "content": {"application/json": {"schema": {"type": "object", "properties": {"id": {"type": "string", "format": "uuid"}, "title": {"type": "string"}, "description": {"type": "string"}, "updatedAt": {"type": "string", "format": "date-time"}}}}}}, "400": {"description": "请求参数错误或对话ID格式错误"}, "401": {"description": "用户未认证，请先登录"}, "403": {"description": "无权限更新该对话"}, "404": {"description": "对话不存在"}, "500": {"description": "服务器内部错误"}}, "security": [{"JWT": []}], "summary": "更新对话信息", "tags": ["AI聊天管理"]}, "delete": {"description": "删除指定的对话及其所有相关消息。此操作不可逆，只有对话的创建者可以删除对话。", "operationId": "CozeController_deleteConversation", "parameters": [{"name": "id", "required": true, "in": "path", "description": "要删除的对话唯一标识符", "schema": {"format": "uuid", "example": "550e8400-e29b-41d4-a716-************", "type": "string"}}], "responses": {"200": {"description": "对话删除成功", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean", "description": "删除操作是否成功"}}}}}}, "400": {"description": "对话ID格式错误"}, "401": {"description": "用户未认证，请先登录"}, "403": {"description": "无权限删除该对话"}, "404": {"description": "对话不存在"}, "500": {"description": "服务器内部错误"}}, "security": [{"JWT": []}], "summary": "删除对话", "tags": ["AI聊天管理"]}}, "/coze/messages": {"post": {"description": "向AI发送消息并获取回复。使用标准请求-响应模式，等待AI完成回复后一次性返回完整结果。适用于短文本对话。", "operationId": "CozeController_sendMessage", "parameters": [], "requestBody": {"required": true, "description": "发送消息的请求参数", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateMessageDto"}, "examples": {"simpleText": {"summary": "简单文本消息", "description": "发送一个基础的文本消息给AI", "value": {"conversationId": "550e8400-e29b-41d4-a716-************", "content": "你好，请帮我解释一下什么是机器学习？", "type": "text", "streaming": false}}, "programmingQuestion": {"summary": "编程相关问题", "description": "询问具体的编程问题", "value": {"conversationId": "550e8400-e29b-41d4-a716-************", "content": "如何在TypeScript中实现单例模式？请提供代码示例。", "type": "text", "streaming": false, "metadata": {"language": "typescript", "category": "design_pattern", "difficulty": "intermediate"}}}, "codeReview": {"summary": "代码审查请求", "description": "请求AI审查代码片段", "value": {"conversationId": "550e8400-e29b-41d4-a716-************", "content": "请帮我审查这段代码：\n```javascript\nfunction fetchUser(id) {\n  return fetch(`/api/users/${id}`).then(res => res.json());\n}\n```\n有什么可以改进的地方吗？", "type": "text", "streaming": false, "metadata": {"requestType": "code_review", "language": "javascript", "focusAreas": ["error_handling", "best_practices"]}}}, "withObjectString": {"summary": "结构化数据消息", "description": "发送包含结构化数据的消息", "value": {"conversationId": "550e8400-e29b-41d4-a716-************", "content": "分析这个API响应数据的结构", "type": "object_string", "streaming": false, "metadata": {"dataType": "api_response", "format": "json"}}}}}}}, "responses": {"201": {"description": "消息发送成功，返回用户消息和AI回复的完整信息", "content": {"application/json": {"schema": {"type": "object", "properties": {"userMessage": {"type": "object", "properties": {"id": {"type": "string", "format": "uuid", "description": "用户消息ID"}, "conversationId": {"type": "string", "format": "uuid", "description": "对话ID"}, "content": {"type": "string", "description": "用户发送的消息内容"}, "role": {"type": "string", "enum": ["user"], "description": "消息角色"}, "type": {"type": "string", "enum": ["text", "image", "file", "object_string"], "description": "消息类型"}, "status": {"type": "string", "enum": ["pending", "completed", "failed"], "description": "消息状态"}, "metadata": {"type": "object", "description": "消息元数据"}, "createdAt": {"type": "string", "format": "date-time", "description": "创建时间"}}}, "aiMessage": {"type": "object", "properties": {"id": {"type": "string", "format": "uuid", "description": "AI消息ID"}, "conversationId": {"type": "string", "format": "uuid", "description": "对话ID"}, "content": {"type": "string", "description": "AI回复的内容"}, "role": {"type": "string", "enum": ["assistant"], "description": "消息角色"}, "type": {"type": "string", "enum": ["text", "answer", "function_call", "tool_output"], "description": "消息类型"}, "status": {"type": "string", "enum": ["completed", "failed"], "description": "消息状态"}, "metadata": {"type": "object", "description": "消息元数据"}, "usage": {"type": "object", "properties": {"promptTokens": {"type": "number", "description": "输入Token数量"}, "completionTokens": {"type": "number", "description": "输出Token数量"}, "totalTokens": {"type": "number", "description": "总Token数量"}}, "description": "Token使用量统计"}, "createdAt": {"type": "string", "format": "date-time", "description": "创建时间"}, "completedAt": {"type": "string", "format": "date-time", "description": "完成时间"}}}}, "example": {"userMessage": {"id": "123e4567-e89b-12d3-a456-426614174001", "conversationId": "550e8400-e29b-41d4-a716-************", "content": "你好，请帮我解释一下什么是机器学习？", "role": "user", "type": "text", "status": "completed", "metadata": {}, "createdAt": "2024-01-15T10:30:00Z"}, "aiMessage": {"id": "123e4567-e89b-12d3-a456-426614174002", "conversationId": "550e8400-e29b-41d4-a716-************", "content": "机器学习是人工智能的一个分支，它使计算机能够在没有明确编程的情况下学习和改进性能...", "role": "assistant", "type": "answer", "status": "completed", "metadata": {"model": "gpt-3.5-turbo", "temperature": 0.7}, "usage": {"promptTokens": 25, "completionTokens": 150, "totalTokens": 175}, "createdAt": "2024-01-15T10:30:01Z", "completedAt": "2024-01-15T10:30:05Z"}}}}}}, "400": {"description": "请求参数错误或消息内容为空"}, "401": {"description": "用户未认证，请先登录"}, "403": {"description": "无权限访问该对话或消息配额不足"}, "404": {"description": "对话不存在"}, "429": {"description": "请求频率过高，请稍后再试"}, "500": {"description": "服务器内部错误或AI服务暂时不可用"}}, "security": [{"JWT": []}], "summary": "发送AI消息（标准模式）", "tags": ["AI聊天管理"]}}, "/coze/interpretation/messages": {"post": {"description": "向解读智能体发送消息并获取回复。", "operationId": "CozeController_sendInterpretationMessage", "parameters": [], "requestBody": {"required": true, "description": "发送消息的请求参数", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateMessageDto"}}}}, "responses": {"201": {"description": ""}}, "security": [{"JWT": []}], "summary": "发送消息到解读智能体", "tags": ["AI聊天管理"]}}, "/coze/proto/messages": {"post": {"description": "向原型智能体发送消息并获取回复。", "operationId": "CozeController_sendProtoMessage", "parameters": [], "requestBody": {"required": true, "description": "发送消息的请求参数", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateMessageDto"}}}}, "responses": {"201": {"description": ""}}, "security": [{"JWT": []}], "summary": "发送消息到原型智能体", "tags": ["AI聊天管理"]}}, "/coze/messages/stream": {"post": {"description": "向AI发送消息并以Server-Sent Events (SSE)方式实时流式接收回复。适用于长文本生成，用户可以实时看到AI的回复过程。", "operationId": "CozeController_streamMessage", "parameters": [], "requestBody": {"required": true, "description": "发送流式消息的请求参数", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateMessageDto"}, "examples": {"longFormContent": {"summary": "长文本生成请求", "description": "请求AI生成较长的内容，适合流式输出", "value": {"conversationId": "550e8400-e29b-41d4-a716-************", "content": "请详细介绍一下深度学习的发展历程，包括关键里程碑、重要人物和技术突破", "type": "text", "streaming": true, "metadata": {"expectedLength": "long", "topic": "deep_learning_history"}}}, "codeGeneration": {"summary": "代码生成请求", "description": "请求AI生成较复杂的代码，实时查看生成过程", "value": {"conversationId": "550e8400-e29b-41d4-a716-************", "content": "请帮我写一个完整的React组件，实现一个带搜索、分页和排序功能的用户列表。要求使用TypeScript和React Hooks。", "type": "text", "streaming": true, "metadata": {"language": "typescript", "framework": "react", "complexity": "high", "features": ["search", "pagination", "sorting"]}}}, "tutorialExplanation": {"summary": "教程式解释", "description": "请求详细的分步骤解释，适合流式展示", "value": {"conversationId": "550e8400-e29b-41d4-a716-************", "content": "请一步一步地教我如何从零开始构建一个RESTful API，使用Node.js和Express框架", "type": "text", "streaming": true, "metadata": {"instructionType": "step_by_step", "technology": "nodejs_express", "audience": "beginner"}}}, "documentAnalysis": {"summary": "文档分析请求", "description": "分析复杂文档或代码，逐步输出分析结果", "value": {"conversationId": "550e8400-e29b-41d4-a716-************", "content": "请分析这个复杂的SQL查询性能问题，并提供优化建议：\nSELECT u.*, p.*, c.count FROM users u LEFT JOIN profiles p ON u.id = p.user_id LEFT JOIN (SELECT user_id, COUNT(*) as count FROM comments GROUP BY user_id) c ON u.id = c.user_id WHERE u.created_at > '2023-01-01' ORDER BY u.last_login_at DESC", "type": "text", "streaming": true, "metadata": {"analysisType": "sql_performance", "database": "postgresql", "requestType": "optimization"}}}}}}}, "responses": {"200": {"description": "流式消息发送成功，返回SSE数据流", "headers": {"Content-Type": {"description": "text/event-stream"}, "Cache-Control": {"description": "no-cache"}, "Connection": {"description": "keep-alive"}}, "content": {"text/event-stream": {"schema": {"type": "string", "description": "Server-Sent Events格式的数据流，每个事件都是JSON格式", "examples": {"streamEvents": {"summary": "流式事件示例", "description": "展示完整的流式响应过程中的各种事件类型", "value": "data: {\"event\":\"start\",\"data\":{\"messageId\":\"123e4567-e89b-12d3-a456-426614174003\",\"conversationId\":\"550e8400-e29b-41d4-a716-************\"}}\n\ndata: {\"event\":\"chunk\",\"data\":\"深度学习\"}\n\ndata: {\"event\":\"chunk\",\"data\":\"是人工智能\"}\n\ndata: {\"event\":\"chunk\",\"data\":\"的一个重要分支\"}\n\ndata: {\"event\":\"chunk\",\"data\":\"，它通过算法\"}\n\ndata: {\"event\":\"chunk\",\"data\":\"让计算机从数据中学习\"}\n\ndata: {\"event\":\"done\",\"data\":{\"messageId\":\"123e4567-e89b-12d3-a456-426614174003\",\"status\":\"completed\",\"usage\":{\"promptTokens\":32,\"completionTokens\":245,\"totalTokens\":277},\"completedAt\":\"2024-01-15T10:30:15Z\"}}\n\n"}, "errorEvent": {"summary": "错误事件示例", "description": "当流式处理过程中发生错误时的响应格式", "value": "data: {\"event\":\"start\",\"data\":{\"messageId\":\"123e4567-e89b-12d3-a456-426614174004\",\"conversationId\":\"550e8400-e29b-41d4-a716-************\"}}\n\ndata: {\"event\":\"chunk\",\"data\":\"正在处理您的请求\"}\n\ndata: {\"event\":\"error\",\"data\":{\"error\":\"AI服务暂时不可用\",\"code\":\"AI_SERVICE_UNAVAILABLE\",\"messageId\":\"123e4567-e89b-12d3-a456-426614174004\"}}\n\n"}}}}}}, "400": {"description": "请求参数错误或消息内容为空"}, "401": {"description": "用户未认证，请先登录"}, "403": {"description": "无权限访问该对话或消息配额不足"}, "404": {"description": "对话不存在"}, "429": {"description": "请求频率过高，请稍后再试"}, "500": {"description": "服务器内部错误或AI服务暂时不可用"}}, "security": [{"JWT": []}], "summary": "发送AI消息（流式模式）", "tags": ["AI聊天管理"]}}, "/coze/interpretation/messages/stream": {"post": {"description": "向解读智能体发送消息并以流式方式接收回复。", "operationId": "CozeController_streamInterpretationMessage", "parameters": [], "requestBody": {"required": true, "description": "发送流式消息的请求参数", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateMessageDto"}}}}, "responses": {"201": {"description": ""}}, "security": [{"JWT": []}], "summary": "发送流式消息到解读智能体", "tags": ["AI聊天管理"]}}, "/coze/proto/messages/stream": {"post": {"description": "向原型智能体发送消息并以流式方式接收回复。", "operationId": "CozeController_streamProtoMessage", "parameters": [], "requestBody": {"required": true, "description": "发送流式消息的请求参数", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateMessageDto"}}}}, "responses": {"201": {"description": ""}}, "security": [{"JWT": []}], "summary": "发送流式消息到原型智能体", "tags": ["AI聊天管理"]}}, "/coze/conversations/{id}/messages": {"get": {"description": "分页获取指定默认智能体对话中的所有消息记录，按时间倒序排列。包括用户消息和AI回复。", "operationId": "CozeController_getConversationMessages", "parameters": [{"name": "id", "required": true, "in": "path", "description": "对话的唯一标识符", "schema": {"format": "uuid", "example": "550e8400-e29b-41d4-a716-************", "type": "string"}}, {"name": "page", "required": false, "in": "query", "description": "页码，从1开始", "schema": {"example": 1, "type": "number"}}, {"name": "pageSize", "required": false, "in": "query", "description": "每页显示数量，默认50条，最大200条", "schema": {"example": 50, "type": "number"}}], "responses": {"200": {"description": "消息列表获取成功", "content": {"application/json": {"schema": {"type": "object", "properties": {"code": {"type": "number", "example": 200}, "message": {"type": "string", "example": "消息列表获取成功"}, "data": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "string", "format": "uuid", "description": "消息唯一标识符"}, "conversationId": {"type": "string", "format": "uuid", "description": "所属对话ID"}, "content": {"type": "string", "description": "消息内容"}, "role": {"type": "string", "enum": ["user", "assistant", "system"], "description": "消息角色"}, "type": {"type": "string", "enum": ["text", "image", "file", "object_string", "answer", "function_call", "tool_output"], "description": "消息类型"}, "status": {"type": "string", "enum": ["pending", "completed", "failed"], "description": "消息状态"}, "metadata": {"type": "object", "description": "消息元数据"}, "usage": {"type": "object", "properties": {"promptTokens": {"type": "number", "description": "输入Token数量"}, "completionTokens": {"type": "number", "description": "输出Token数量"}, "totalTokens": {"type": "number", "description": "总Token数量"}}, "description": "Token使用量统计（仅AI消息有此字段）"}, "createdAt": {"type": "string", "format": "date-time", "description": "创建时间"}, "completedAt": {"type": "string", "format": "date-time", "description": "完成时间（仅已完成消息有此字段）"}}}}, "pagination": {"type": "object", "properties": {"total": {"type": "number", "description": "总消息数"}, "page": {"type": "number", "description": "当前页码"}, "pageSize": {"type": "number", "description": "每页大小"}, "totalPages": {"type": "number", "description": "总页数"}, "hasNext": {"type": "boolean", "description": "是否有下一页"}, "hasPrev": {"type": "boolean", "description": "是否有上一页"}}}, "timestamp": {"type": "string", "description": "响应时间戳"}}}}}}, "400": {"description": "对话ID格式错误或分页参数错误"}, "401": {"description": "用户未认证，请先登录"}, "403": {"description": "无权限访问该对话的消息"}, "404": {"description": "对话不存在"}, "500": {"description": "服务器内部错误"}}, "security": [{"JWT": []}], "summary": "获取对话的消息历史（默认智能体）", "tags": ["AI聊天管理"]}}, "/coze/proto/conversations/{id}/messages": {"get": {"description": "分页获取指定原型智能体对话中的所有消息记录，按时间倒序排列。包括用户消息和AI回复。", "operationId": "CozeController_getProtoConversationMessages", "parameters": [{"name": "id", "required": true, "in": "path", "description": "对话的唯一标识符", "schema": {"format": "uuid", "example": "550e8400-e29b-41d4-a716-************", "type": "string"}}, {"name": "page", "required": false, "in": "query", "description": "页码，从1开始", "schema": {"example": 1, "type": "number"}}, {"name": "pageSize", "required": false, "in": "query", "description": "每页显示数量，默认50条，最大200条", "schema": {"example": 50, "type": "number"}}], "responses": {"200": {"description": "原型智能体消息列表获取成功", "content": {"application/json": {"schema": {"type": "object", "properties": {"code": {"type": "number", "example": 200}, "message": {"type": "string", "example": "原型智能体消息列表获取成功"}, "data": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "string", "format": "uuid", "description": "消息唯一标识符"}, "conversationId": {"type": "string", "format": "uuid", "description": "所属对话ID"}, "content": {"type": "string", "description": "消息内容"}, "role": {"type": "string", "enum": ["user", "assistant", "system"], "description": "消息角色"}, "type": {"type": "string", "enum": ["text", "image", "file", "object_string", "answer", "function_call", "tool_output"], "description": "消息类型"}, "status": {"type": "string", "enum": ["pending", "completed", "failed"], "description": "消息状态"}, "metadata": {"type": "object", "description": "消息元数据"}, "usage": {"type": "object", "properties": {"promptTokens": {"type": "number", "description": "输入Token数量"}, "completionTokens": {"type": "number", "description": "输出Token数量"}, "totalTokens": {"type": "number", "description": "总Token数量"}}, "description": "Token使用量统计（仅AI消息有此字段）"}, "createdAt": {"type": "string", "format": "date-time", "description": "创建时间"}, "completedAt": {"type": "string", "format": "date-time", "description": "完成时间（仅已完成消息有此字段）"}}}}, "pagination": {"type": "object", "properties": {"total": {"type": "number", "description": "总消息数"}, "page": {"type": "number", "description": "当前页码"}, "pageSize": {"type": "number", "description": "每页大小"}, "totalPages": {"type": "number", "description": "总页数"}, "hasNext": {"type": "boolean", "description": "是否有下一页"}, "hasPrev": {"type": "boolean", "description": "是否有上一页"}}}, "timestamp": {"type": "string", "description": "响应时间戳"}}}}}}, "400": {"description": "对话ID格式错误或分页参数错误"}, "401": {"description": "用户未认证，请先登录"}, "403": {"description": "无权限访问该对话的消息"}, "404": {"description": "对话不存在"}, "500": {"description": "服务器内部错误"}}, "security": [{"JWT": []}], "summary": "获取原型智能体对话的消息历史", "tags": ["AI聊天管理"]}}, "/coze/interpretation/conversations/{id}/messages": {"get": {"description": "分页获取指定解读智能体对话中的所有消息记录，按时间倒序排列。包括用户消息和AI回复。", "operationId": "CozeController_getInterpretationConversationMessages", "parameters": [{"name": "id", "required": true, "in": "path", "description": "对话的唯一标识符", "schema": {"format": "uuid", "example": "550e8400-e29b-41d4-a716-************", "type": "string"}}, {"name": "page", "required": false, "in": "query", "description": "页码，从1开始", "schema": {"example": 1, "type": "number"}}, {"name": "pageSize", "required": false, "in": "query", "description": "每页显示数量，默认50条，最大200条", "schema": {"example": 50, "type": "number"}}], "responses": {"200": {"description": "解读智能体消息列表获取成功", "content": {"application/json": {"schema": {"type": "object", "properties": {"code": {"type": "number", "example": 200}, "message": {"type": "string", "example": "解读智能体消息列表获取成功"}, "data": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "string", "format": "uuid", "description": "消息唯一标识符"}, "conversationId": {"type": "string", "format": "uuid", "description": "所属对话ID"}, "content": {"type": "string", "description": "消息内容"}, "role": {"type": "string", "enum": ["user", "assistant", "system"], "description": "消息角色"}, "type": {"type": "string", "enum": ["text", "image", "file", "object_string", "answer", "function_call", "tool_output"], "description": "消息类型"}, "status": {"type": "string", "enum": ["pending", "completed", "failed"], "description": "消息状态"}, "metadata": {"type": "object", "description": "消息元数据"}, "usage": {"type": "object", "properties": {"promptTokens": {"type": "number", "description": "输入Token数量"}, "completionTokens": {"type": "number", "description": "输出Token数量"}, "totalTokens": {"type": "number", "description": "总Token数量"}}, "description": "Token使用量统计（仅AI消息有此字段）"}, "createdAt": {"type": "string", "format": "date-time", "description": "创建时间"}, "completedAt": {"type": "string", "format": "date-time", "description": "完成时间（仅已完成消息有此字段）"}}}}, "pagination": {"type": "object", "properties": {"total": {"type": "number", "description": "总消息数"}, "page": {"type": "number", "description": "当前页码"}, "pageSize": {"type": "number", "description": "每页大小"}, "totalPages": {"type": "number", "description": "总页数"}, "hasNext": {"type": "boolean", "description": "是否有下一页"}, "hasPrev": {"type": "boolean", "description": "是否有上一页"}}}, "timestamp": {"type": "string", "description": "响应时间戳"}}}}}}, "400": {"description": "对话ID格式错误或分页参数错误"}, "401": {"description": "用户未认证，请先登录"}, "403": {"description": "无权限访问该对话的消息"}, "404": {"description": "对话不存在"}, "500": {"description": "服务器内部错误"}}, "security": [{"JWT": []}], "summary": "获取解读智能体对话的消息历史", "tags": ["AI聊天管理"]}}, "/coze/conversations/{conversationId}/chats/{chatId}": {"delete": {"description": "取消当前正在进行的AI对话请求。用于停止长时间运行的AI生成任务，特别是流式对话。", "operationId": "CozeController_cancelChat", "parameters": [{"name": "conversationId", "required": true, "in": "path", "description": "对话的唯一标识符", "schema": {"format": "uuid", "example": "550e8400-e29b-41d4-a716-************", "type": "string"}}, {"name": "chatId", "required": true, "in": "path", "description": "聊天会话的标识符，用于标识特定的AI对话请求", "schema": {"example": "chat_1234567890", "type": "string"}}], "responses": {"200": {"description": "聊天取消成功", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean", "description": "取消操作是否成功"}}}}}}, "400": {"description": "对话ID或聊天ID格式错误"}, "401": {"description": "用户未认证，请先登录"}, "403": {"description": "无权限取消该聊天"}, "404": {"description": "对话或聊天不存在"}, "410": {"description": "聊天已完成，无法取消"}, "500": {"description": "服务器内部错误"}}, "security": [{"JWT": []}], "summary": "取消正在进行的AI对话", "tags": ["AI聊天管理"]}}, "/team-invite/my-invite-code": {"get": {"operationId": "TeamInviteController_getMyInviteCode", "parameters": [], "responses": {"200": {"description": "获取成功", "content": {"application/json": {"schema": {"example": {"code": 200, "message": "请求成功", "data": {"inviteCode": "ABC12345", "inviteLink": "http://localhost:3000/invite/ABC12345"}}}}}}, "401": {"description": "未授权"}, "403": {"description": "没有邀请权限"}}, "security": [{"bearer": []}], "summary": "获取我的永久邀请码", "tags": ["团队邀请"]}}, "/team-invite/check/{inviteCode}": {"get": {"operationId": "TeamInviteController_checkInviteCode", "parameters": [{"name": "inviteCode", "required": true, "in": "path", "schema": {"type": "string"}}], "responses": {"200": {"description": "检查成功", "content": {"application/json": {"schema": {"example": {"code": 200, "message": "请求成功", "data": {"valid": true, "inviterName": "张三", "inviterId": 1}}}}}}, "401": {"description": "未授权"}}, "security": [{"bearer": []}], "summary": "检查邀请码是否有效", "tags": ["团队邀请"]}}, "/team-invite/accept": {"post": {"operationId": "TeamInviteController_acceptInvite", "parameters": [], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/AcceptInviteDto"}}}}, "responses": {"200": {"description": "邀请接受成功", "content": {"application/json": {"schema": {"example": {"code": 200, "message": "请求成功", "data": {"success": true, "message": "邀请接受成功，您已获得团队权限", "teamPermissionGranted": true}}}}}}, "400": {"description": "邀请码无效或已过期"}, "401": {"description": "未授权"}}, "security": [{"bearer": []}], "summary": "接受邀请", "tags": ["团队邀请"]}}, "/team-invite/my-info": {"get": {"operationId": "TeamInviteController_getMyInviteInfo", "parameters": [], "responses": {"200": {"description": "获取成功", "schema": {"example": {"code": 200, "message": "请求成功", "data": {"myInviteCode": "ABC12345", "inviteCount": 5, "canInviteOthers": true, "stats": {"totalInvites": 5, "successfulInvites": 5, "pendingInvites": 0, "expiredInvites": 0, "successRate": 100}, "sentInvites": []}}}, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/MyInviteInfoDto"}}}}, "401": {"description": "未授权"}}, "security": [{"bearer": []}], "summary": "获取我的邀请信息", "tags": ["团队邀请"]}}, "/team-invite/my-invited-users": {"get": {"operationId": "TeamInviteController_getMyInvitedUsers", "parameters": [], "responses": {"200": {"description": "获取成功", "schema": {"example": {"code": 200, "message": "请求成功", "data": {"myInviteCode": "ABC12345", "totalCount": 3, "invitedUsers": [{"id": 2, "name": "李四", "email": "<EMAIL>", "phoneNumber": "13800138001", "joinedAt": "2024-01-15T10:30:00.000Z", "permissionLevel": "team", "userType": "individual", "membershipType": "free"}, {"id": 3, "name": "王五", "email": "<EMAIL>", "phoneNumber": "13800138002", "joinedAt": "2024-01-16T14:20:00.000Z", "permissionLevel": "team", "userType": "individual", "membershipType": "basic"}], "summary": {"totalUsers": 3, "activeUsers": 3, "teamUsers": 3, "individualUsers": 0}}}}, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/MyInvitedUsersDto"}}}}, "401": {"description": "未授权"}}, "security": [{"bearer": []}], "summary": "获取使用我邀请码的用户详细信息", "tags": ["团队邀请"]}}, "/team-invite/my-invited-users-count": {"get": {"operationId": "TeamInviteController_getMyInvitedUsersCount", "parameters": [], "responses": {"200": {"description": "获取成功", "content": {"application/json": {"schema": {"example": {"code": 200, "message": "请求成功", "data": {"myInviteCode": "ABC12345", "totalCount": 5, "activeCount": 4, "teamCount": 3}}}}}}, "401": {"description": "未授权"}}, "security": [{"bearer": []}], "summary": "获取使用我邀请码的用户数量统计", "tags": ["团队邀请"]}}, "/team-invite": {"post": {"deprecated": true, "description": "该API已废弃，请使用 GET /my-invite-code 获取永久邀请码", "operationId": "TeamInviteController_createInvite", "parameters": [], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateInviteDto"}}}}, "responses": {"200": {"description": "返回用户的永久邀请码", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/InviteResponseDto"}}}}, "401": {"description": "未授权"}, "403": {"description": "没有邀请权限"}}, "security": [{"bearer": []}], "summary": "创建邀请码（已废弃）", "tags": ["团队邀请"]}}, "/team-invite/detail/{id}": {"get": {"deprecated": true, "description": "该API已废弃，永久邀请码系统不再需要此功能", "operationId": "TeamInviteController_getInviteDetail", "parameters": [{"name": "id", "required": true, "in": "path", "schema": {"type": "number"}}], "responses": {"400": {"description": "该功能已废弃"}}, "security": [{"bearer": []}], "summary": "获取邀请记录详情（已废弃）", "tags": ["团队邀请"]}}, "/team-invite/{id}": {"delete": {"deprecated": true, "description": "该API已废弃，永久邀请码无法取消", "operationId": "TeamInviteController_cancelInvite", "parameters": [{"name": "id", "required": true, "in": "path", "schema": {"type": "number"}}], "responses": {"400": {"description": "永久邀请码无法取消"}}, "security": [{"bearer": []}], "summary": "取消邀请（已废弃）", "tags": ["团队邀请"]}}, "/team-invite/cleanup-expired": {"post": {"deprecated": true, "description": "该API已废弃，永久邀请码不会过期", "operationId": "TeamInviteController_cleanupExpiredInvites", "parameters": [], "responses": {"200": {"description": "永久邀请码不会过期", "content": {"application/json": {"schema": {"example": {"code": 200, "message": "请求成功", "data": {"count": 0}}}}}}}, "security": [{"bearer": []}], "summary": "清理过期邀请（已废弃）", "tags": ["团队邀请"]}}, "/invite-stats/overview": {"get": {"operationId": "TeamInviteStatsController_getOverview", "parameters": [{"name": "timeRange", "required": false, "in": "query", "description": "时间范围", "schema": {"default": "30d", "type": "string", "enum": ["7d", "30d", "90d", "custom"]}}, {"name": "startDate", "required": false, "in": "query", "description": "开始日期（自定义时间范围时使用）", "schema": {"type": "string"}}, {"name": "endDate", "required": false, "in": "query", "description": "结束日期（自定义时间范围时使用）", "schema": {"type": "string"}}, {"name": "sortBy", "required": false, "in": "query", "description": "排序方式", "schema": {"default": "usage", "type": "string", "enum": ["usage", "lastActive", "joinDate"]}}, {"name": "limit", "required": false, "in": "query", "description": "限制返回数量", "schema": {"minimum": 1, "maximum": 100, "type": "number"}}], "responses": {"200": {"description": "获取成功", "content": {"application/json": {"schema": {"example": {"code": 200, "message": "请求成功", "data": {"totalInvitedUsers": 15, "activeUsers": 12, "avgUsagePerUser": 25.6, "activeRate": 80, "totalUsage": 384, "growthRate": 15.5, "timeRange": "30d"}}}}}}}, "security": [{"bearer": []}], "summary": "获取邀请用户统计概览", "tags": ["邀请统计"]}}, "/invite-stats/usage-trend": {"get": {"operationId": "TeamInviteStatsController_getUsageTrend", "parameters": [{"name": "timeRange", "required": false, "in": "query", "description": "时间范围", "schema": {"default": "30d", "type": "string", "enum": ["7d", "30d", "90d"]}}, {"name": "chartType", "required": false, "in": "query", "description": "图表类型", "schema": {"type": "string", "enum": ["line", "bar"]}}], "responses": {"200": {"description": "获取成功", "content": {"application/json": {"schema": {"example": {"code": 200, "message": "请求成功", "data": {"chartData": {"xAxis": {"type": "category", "data": ["2024-01-01", "2024-01-02"]}, "yAxis": {"type": "value", "name": "使用次数"}, "series": [{"name": "总使用次数", "type": "line", "data": [25, 30]}, {"name": "活跃用户数", "type": "line", "data": [8, 10]}]}, "overview": {"totalInvitedUsers": 15, "activeUsers": 12, "avgUsagePerUser": 25.6, "activeRate": 80, "totalUsage": 384, "growthRate": 15.5, "timeRange": "30d"}, "timeRange": "30d"}}}}}}}, "security": [{"bearer": []}], "summary": "获取使用趋势数据（ECharts格式）", "tags": ["邀请统计"]}}, "/invite-stats/user-ranking": {"get": {"operationId": "TeamInviteStatsController_getUserRanking", "parameters": [{"name": "limit", "required": false, "in": "query", "description": "限制返回数量", "schema": {"minimum": 1, "maximum": 50, "type": "number"}}, {"name": "sortBy", "required": false, "in": "query", "description": "排序方式", "schema": {"default": "usage", "type": "string", "enum": ["usage", "lastActive", "joinDate"]}}], "responses": {"200": {"description": "获取成功", "content": {"application/json": {"schema": {"example": {"code": 200, "message": "请求成功", "data": {"chartData": {"xAxis": {"type": "category", "data": ["用户A", "用户B", "用户C"]}, "yAxis": {"type": "value", "name": "使用次数"}, "series": [{"name": "使用次数", "type": "bar", "data": [45, 38, 32]}]}, "userList": [{"userId": 1, "userName": "用户A", "userEmail": "<EMAIL>", "rank": 1, "usageCount": 45, "lastActiveDate": "2024-01-15T10:30:00.000Z", "joinDate": "2024-01-01T00:00:00.000Z"}], "totalUsers": 10}}}}}}}, "security": [{"bearer": []}], "summary": "获取用户活跃度排名（ECharts格式）", "tags": ["邀请统计"]}}, "/invite-stats/daily-activity": {"get": {"operationId": "TeamInviteStatsController_getDailyActivity", "parameters": [{"name": "timeRange", "required": false, "in": "query", "description": "时间范围", "schema": {"default": "30d", "type": "string", "enum": ["30d", "90d"]}}], "responses": {"200": {"description": "获取成功", "content": {"application/json": {"schema": {"example": {"code": 200, "message": "请求成功", "data": {"chartData": {"tooltip": {"position": "top"}, "xAxis": {"type": "category", "data": ["周一", "周二", "周三"]}, "yAxis": {"type": "category", "data": ["第1周", "第2周"]}, "visualMap": {"min": 0, "max": 20}, "series": [{"name": "活跃度", "type": "heatmap", "data": [[0, 0, 15], [1, 0, 12]]}]}, "dailyData": [{"date": "2024-01-01", "value": 15, "usageCount": 25}, {"date": "2024-01-02", "value": 12, "usageCount": 20}], "timeRange": "30d"}}}}}}}, "security": [{"bearer": []}], "summary": "获取每日活跃度热力图数据（ECharts格式）", "tags": ["邀请统计"]}}, "/invite-stats/feature-usage": {"get": {"operationId": "TeamInviteStatsController_getFeatureUsage", "parameters": [{"name": "timeRange", "required": false, "in": "query", "description": "时间范围", "schema": {"default": "30d", "type": "string", "enum": ["7d", "30d", "90d"]}}], "responses": {"200": {"description": "获取成功", "content": {"application/json": {"schema": {"example": {"code": 200, "message": "请求成功", "data": {"chartData": {"tooltip": {"trigger": "item"}, "legend": {"data": ["AI解读", "评估查询"]}, "series": [{"name": "功能使用", "type": "pie", "radius": "50%", "data": [{"name": "AI解读", "value": 45}, {"name": "评估查询", "value": 35}]}]}, "featureList": [{"name": "AI解读", "value": 45, "percentage": 45}, {"name": "评估查询", "value": 35, "percentage": 35}], "totalUsage": 100}}}}}}}, "security": [{"bearer": []}], "summary": "获取功能使用分布数据（ECharts格式）", "tags": ["邀请统计"]}}, "/payment/create": {"post": {"description": "创建新的支付订单并返回支付参数", "operationId": "PaymentController_createPaymentOrder", "parameters": [], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreatePaymentRequestDto"}}}}, "responses": {"201": {"description": "支付订单创建成功", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreatePaymentResponseDto"}}}}, "400": {"description": "请求参数错误"}, "500": {"description": "服务器内部错误"}}, "summary": "创建支付订单", "tags": ["支付管理"]}, "get": {"operationId": "PaymentController_createPayment", "parameters": [{"name": "openid", "required": true, "in": "query", "schema": {"type": "string"}}, {"name": "amount", "required": true, "in": "query", "schema": {"type": "number"}}, {"name": "description", "required": true, "in": "query", "schema": {"type": "string"}}], "responses": {"200": {"description": ""}}, "tags": ["支付管理"]}}, "/payment/wechat/create": {"post": {"description": "创建微信支付订单并返回支付参数", "operationId": "PaymentController_createWechatPayment", "parameters": [], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateWechatPaymentDto"}}}}, "responses": {"201": {"description": "微信支付订单创建成功", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreatePaymentResponseDto"}}}}, "400": {"description": "请求参数错误"}, "500": {"description": "服务器内部错误"}}, "summary": "创建微信支付订单", "tags": ["支付管理"]}}, "/payment/list": {"get": {"description": "分页查询支付记录列表", "operationId": "PaymentController_getPaymentList", "parameters": [{"name": "status", "required": false, "in": "query", "description": "支付状态筛选", "schema": {"example": "success", "type": "string", "enum": ["pending", "processing", "success", "failed", "cancelled", "refunded"]}}, {"name": "userId", "required": false, "in": "query", "description": "用户ID筛选", "schema": {"example": 123, "type": "number"}}, {"name": "orderNo", "required": false, "in": "query", "description": "订单号筛选", "schema": {"example": "ORDER_20240115103000123456", "type": "string"}}, {"name": "page", "required": false, "in": "query", "description": "页码", "schema": {"minimum": 1, "example": 1, "type": "number"}}, {"name": "limit", "required": false, "in": "query", "description": "每页数量", "schema": {"minimum": 1, "maximum": 100, "example": 20, "type": "number"}}], "responses": {"200": {"description": "查询成功"}, "400": {"description": "请求参数错误"}}, "summary": "查询支付记录列表", "tags": ["支付管理"]}}, "/payment/{id}": {"get": {"description": "根据支付记录ID获取详细信息", "operationId": "PaymentController_getPaymentById", "parameters": [{"name": "id", "required": true, "in": "path", "description": "支付记录ID", "schema": {"type": "number"}}], "responses": {"200": {"description": "获取成功", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/PaymentResponseDto"}}}}, "404": {"description": "支付记录不存在"}}, "summary": "获取支付记录详情", "tags": ["支付管理"]}}, "/payment/order/{orderNo}": {"get": {"description": "根据订单号获取支付记录详细信息", "operationId": "PaymentController_getPaymentByOrderNo", "parameters": [{"name": "orderNo", "required": true, "in": "path", "description": "订单号", "schema": {"type": "string"}}], "responses": {"200": {"description": "获取成功", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/PaymentResponseDto"}}}}, "404": {"description": "支付记录不存在"}}, "summary": "根据订单号获取支付记录", "tags": ["支付管理"]}}, "/payment/{id}/status": {"put": {"description": "手动更新支付记录状态", "operationId": "PaymentController_updatePaymentStatus", "parameters": [{"name": "id", "required": true, "in": "path", "description": "支付记录ID", "schema": {"type": "number"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UpdatePaymentStatusDto"}}}}, "responses": {"200": {"description": "更新成功"}, "404": {"description": "支付记录不存在"}}, "summary": "更新支付状态", "tags": ["支付管理"]}}, "/payment/user/{userId}/stats": {"get": {"description": "获取指定用户的支付统计信息", "operationId": "PaymentController_getUserPaymentStats", "parameters": [{"name": "userId", "required": true, "in": "path", "description": "用户ID", "schema": {"type": "number"}}], "responses": {"200": {"description": "获取成功", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/PaymentStatsResponseDto"}}}}}, "summary": "获取用户支付统计", "tags": ["支付管理"]}}, "/payment/verify": {"post": {"description": "验证支付记录的当前状态", "operationId": "PaymentController_verifyPaymentStatus", "parameters": [], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/PaymentVerificationDto"}}}}, "responses": {"200": {"description": "验证成功", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/PaymentResponseDto"}}}}, "404": {"description": "支付记录不存在"}}, "summary": "验证支付状态", "tags": ["支付管理"]}}, "/payment/notify": {"post": {"description": "处理第三方支付平台的回调通知", "operationId": "PaymentController_handlePaymentNotify", "parameters": [], "responses": {"200": {"description": "回调处理成功"}}, "summary": "支付回调处理", "tags": ["支付管理"]}}, "/payment": {"get": {"description": "支付模块健康检查", "operationId": "PaymentController_get<PERSON><PERSON>", "parameters": [], "responses": {"200": {"description": "服务正常"}}, "summary": "健康检查", "tags": ["支付管理"]}}, "/payment/admin/test": {"get": {"description": "支付模块管理员测试接口", "operationId": "PaymentController_adminTest", "parameters": [], "responses": {"200": {"description": "测试成功"}}, "summary": "管理员测试接口", "tags": ["支付管理"]}}, "/query-templates": {"post": {"operationId": "QueryTemplateController_create", "parameters": [], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateQueryTemplateDto"}}}}, "responses": {"201": {"description": ""}}, "tags": ["QueryTemplate"]}, "get": {"operationId": "QueryTemplateController_findAll", "parameters": [], "responses": {"200": {"description": ""}}, "tags": ["QueryTemplate"]}}, "/query-templates/{id}": {"get": {"operationId": "QueryTemplateController_findOne", "parameters": [{"name": "id", "required": true, "in": "path", "schema": {"type": "number"}}], "responses": {"200": {"description": ""}}, "tags": ["QueryTemplate"]}, "put": {"operationId": "QueryTemplateController_update", "parameters": [{"name": "id", "required": true, "in": "path", "schema": {"type": "number"}}], "responses": {"200": {"description": ""}}, "tags": ["QueryTemplate"]}, "delete": {"operationId": "QueryTemplateController_remove", "parameters": [{"name": "id", "required": true, "in": "path", "schema": {"type": "number"}}], "responses": {"200": {"description": ""}}, "tags": ["QueryTemplate"]}}, "/date-query/prototype": {"post": {"operationId": "DateQueryController_queryByDate", "parameters": [], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/DateQueryDto"}}}}, "responses": {"201": {"description": ""}}, "tags": ["<PERSON><PERSON><PERSON><PERSON>"]}}, "/date-query/initialize": {"post": {"operationId": "DateQueryController_initializeData", "parameters": [], "responses": {"201": {"description": ""}}, "tags": ["<PERSON><PERSON><PERSON><PERSON>"]}}, "/date-query/all": {"get": {"operationId": "DateQueryController_getAllData", "parameters": [], "responses": {"200": {"description": ""}}, "tags": ["<PERSON><PERSON><PERSON><PERSON>"]}}, "/questions": {"get": {"operationId": "QuestionController_findAll", "parameters": [], "responses": {"200": {"description": ""}}, "tags": ["题目管理"]}, "post": {"operationId": "QuestionController_create", "parameters": [], "responses": {"201": {"description": ""}}, "tags": ["题目管理"]}}, "/questions/{id}": {"get": {"operationId": "QuestionController_getQuestionById", "parameters": [{"name": "id", "required": true, "in": "path", "schema": {"type": "number"}}], "responses": {"200": {"description": ""}}, "tags": ["题目管理"]}, "delete": {"operationId": "QuestionController_remove", "parameters": [{"name": "id", "required": true, "in": "path", "schema": {"type": "number"}}], "responses": {"200": {"description": ""}}, "tags": ["题目管理"]}}, "/questions/calculate-results": {"post": {"operationId": "QuestionController_calculateResults", "parameters": [], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CalculateResultDto"}}}}, "responses": {"200": {"description": "计算结果成功", "schema": {"example": {"mbti": {"E": 5, "I": 3, "S": 4, "N": 4, "T": 6, "F": 2, "J": 3, "P": 5}, "disc": {"D": 8, "I": 6, "S": 10, "C": 8}, "mbtiType": "ENFP", "discType": "S", "finalType": "ENFP-S", "dimensionScores": {"能量互动": 5, "信息感知": 4, "决策形成": 6, "行动实施": 5}}}, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CalculateResultResponseDto"}}}}, "400": {"description": "答案数组长度不正确或题目数量不正确"}}, "summary": "计算MBTI和DISC结果", "tags": ["题目管理"]}}, "/questions/personality-results": {"post": {"operationId": "QuestionController_createPersonalityResult", "parameters": [], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreatePersonalityResultDto"}}}}, "responses": {"201": {"description": "创建成功", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/PersonalityResultResponseDto"}}}}, "400": {"description": "该人格类型已存在"}}, "summary": "创建人格测试结果", "tags": ["题目管理"]}, "get": {"operationId": "QuestionController_getAllPersonalityResults", "parameters": [], "responses": {"200": {"description": "获取成功", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/PersonalityResultResponseDto"}}}}}}, "summary": "获取所有人格测试结果", "tags": ["题目管理"]}}, "/questions/personality-results/get": {"post": {"operationId": "QuestionController_getPersonalityResult", "parameters": [], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/GetPersonalityResultDto"}}}}, "responses": {"200": {"description": "获取成功", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/PersonalityResultResponseDto"}}}}, "404": {"description": "未找到该人格类型的结果"}}, "summary": "根据类型代码获取人格测试结果", "tags": ["题目管理"]}}, "/questions/personality-results/seed": {"post": {"operationId": "QuestionController_seedPersonalityResults", "parameters": [], "responses": {"200": {"description": "批量创建成功"}}, "summary": "批量创建人格测试结果数据", "tags": ["题目管理"]}}, "/questions/personality-results/calculate": {"post": {"operationId": "QuestionController_getPersonalityResultByCalculation", "parameters": [], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CalculateResultDto"}}}}, "responses": {"200": {"description": "获取成功", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/PersonalityResultResponseDto"}}}}, "400": {"description": "答案数组长度不正确或题目数量不正确"}, "404": {"description": "未找到对应的人格类型结果"}}, "summary": "根据答题结果获取对应的人格测试结果", "tags": ["题目管理"]}}, "/questions/calculate-results-with-personality": {"post": {"operationId": "QuestionController_calculateResultsWithPersonality", "parameters": [], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CalculateResultDto"}}}}, "responses": {"200": {"description": "计算成功并返回完整结果", "schema": {"example": {"mbti": {"E": 5, "I": 3, "S": 4, "N": 4, "T": 6, "F": 2, "J": 3, "P": 5}, "disc": {"D": 8, "I": 6, "S": 10, "C": 8}, "mbtiType": "ISTJ", "discType": "D", "finalType": "ISTJ-D", "dimensionScores": {"能量互动": 5, "信息感知": 4, "决策形成": 6, "行动实施": 5}, "personalityResult": {"typeCode": "ISTJ-D", "title": "规则凝聚者", "coreTraits": "以身作则推进制度，强硬把控细节中凝聚团队忠诚", "strengths": "极高目标达成率与团队向心力并重", "sceneMatch": "危机管理/制度落地/军官/领导", "blindSpots": "压制异议倾向，易陷入任人唯亲，追求虚名", "suggestions": "主动倾听反馈，建立匿名吐槽机制", "symbol": "师", "lifePath": "怎样从带团队到做一名游刃有余的一把手", "valueGuide": "⚠️ 团队因压制异议分崩离析？制度落地沦为虚名？<br>💎 以身作则锻造铁军，建立匿名吐槽机制 ，制定《忠诚团队构建蓝图》"}, "savedToHistory": true}}, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CalculateResultWithPersonalityResponseDto"}}}}, "400": {"description": "答案数组长度不正确或题目数量不正确"}, "404": {"description": "未找到对应的人格类型结果"}}, "summary": "计算MBTI和DISC结果并返回完整的人格测试结果信息", "tags": ["题目管理"]}}, "/questions/user-answer-records": {"post": {"operationId": "QuestionController_saveUserAnswerRecord", "parameters": [], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateUserAnswerRecordDto"}}}}, "responses": {"201": {"description": "保存成功", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UserAnswerRecordResponseDto"}}}}, "400": {"description": "答案数组长度不正确或题目数量不正确"}}, "summary": "保存用户答题记录", "tags": ["题目管理"]}}, "/questions/user-answer-records/history": {"get": {"operationId": "QuestionController_getUserAnswerRecords", "parameters": [{"name": "pageSize", "required": false, "in": "query", "description": "每页数量，默认为10，最大100", "schema": {"type": "number"}}, {"name": "page", "required": false, "in": "query", "description": "页码，默认为1", "schema": {"type": "number"}}, {"name": "userId", "required": true, "in": "query", "description": "用户ID", "schema": {"type": "number"}}], "responses": {"200": {"description": "获取成功", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/PaginatedUserAnswerRecordsResponseDto"}}}}}, "summary": "获取用户的答题记录历史", "tags": ["题目管理"]}}, "/questions/user-answer-records/current-type": {"post": {"operationId": "QuestionController_getUserCurrentType", "parameters": [], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/GetUserCurrentTypeDto"}}}}, "responses": {"200": {"description": "获取成功", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UserCurrentTypeResponseDto"}}}}, "404": {"description": "用户暂无答题记录"}}, "summary": "获取用户的当前人格类型", "tags": ["题目管理"]}}, "/questions/user-answer-records/proto/{userId}": {"get": {"operationId": "QuestionController_getUserProtoType", "parameters": [{"name": "userId", "required": true, "in": "path", "schema": {"type": "number"}}], "responses": {"200": {"description": "获取成功", "content": {"application/json": {"schema": {"example": {"proto": "ISTJ-D"}}}}}, "404": {"description": "用户暂无答题记录"}}, "summary": "获取用户的proto类型（当前类型）", "tags": ["题目管理"]}}, "/uploads/upload": {"post": {"operationId": "UploadController_uploadFile", "parameters": [], "requestBody": {"required": true, "content": {"multipart/form-data": {"schema": {"type": "object", "properties": {"file": {"type": "string", "format": "binary", "description": "要上传的文件"}}}}}}, "responses": {"200": {"description": "上传成功"}, "400": {"description": "上传失败"}}, "summary": "上传文件", "tags": ["上传"]}}}, "info": {"title": "KanLi API", "description": "KanLi API Documentation", "version": "1.0", "contact": {}}, "tags": [{"name": "kanli", "description": ""}], "servers": [], "components": {"schemas": {"SendCaptchaDto": {"type": "object", "properties": {"phoneNumber": {"type": "string", "description": "手机号", "example": "13800138000", "pattern": "^1[3-9]\\d{9}$"}}, "required": ["phoneNumber"]}, "CreateUserDto": {"type": "object", "properties": {"phoneNumber": {"type": "string", "description": "手机号（可选）"}, "name": {"type": "string", "description": "用户名", "example": "张三"}, "email": {"type": "string", "description": "邮箱（可选）", "example": "<EMAIL>"}, "password": {"type": "string", "description": "密码", "example": "Password123!"}, "captcha": {"type": "string", "description": "验证码（当提供手机号时必填）"}, "fullName": {"type": "string", "description": "真实姓名（可选）"}, "address": {"type": "string", "description": "地址（可选）"}, "gender": {"type": "string", "enum": ["male", "female", "other"], "description": "性别（可选）", "example": "male"}, "birthDate": {"type": "string", "description": "出生日期（可选）", "format": "date", "example": "1990-01-01"}, "userType": {"type": "string", "enum": ["individual", "institution"], "description": "用户类型", "default": "individual", "example": "individual"}, "profilePicture": {"type": "string", "description": "头像链接（可选）"}, "profileBackgroundPicture": {"type": "string", "description": "背景图链接（可选）"}}, "required": ["name", "password", "userType"]}, "LoginUserDto": {"type": "object", "properties": {"phoneNumber": {"type": "string", "description": "手机号", "example": "13800138000"}, "password": {"type": "string", "description": "密码", "example": "Password123!"}}, "required": ["phoneNumber", "password"]}, "WechatLoginDto": {"type": "object", "properties": {"code": {"type": "string", "description": "微信小程序登录凭证code", "example": "061FGqIV0K0XcX1wKfIF0X8w8y0FGqIt"}}, "required": ["code"]}, "WechatLoginResponseDto": {"type": "object", "properties": {"user": {"type": "object", "description": "用户信息"}, "token": {"type": "string", "description": "JWT令牌"}, "message": {"type": "string", "description": "响应消息"}, "isNewUser": {"type": "boolean", "description": "是否为新用户"}}, "required": ["user", "token", "message", "isNewUser"]}, "DecryptUserInfoDto": {"type": "object", "properties": {"encryptedData": {"type": "string", "description": "加密的用户数据", "example": "2Jguq6v771RzmIg824V8mAD+CYBjIoABP9geaQF8qGeHHzzZlyWsIDl6L8BQGtmISAD2JbdfPw/TUVBPse1XnhWQVA+8FvCnoVfjAex58RPOtf7PG0699gHYCaxoS5sXN7xBz75fEpmH2YWFc9Xo+31p8z6k2YBN1dLPStvqfcz1Z1w5cofvXAFbEubbjuRfiRl9JKzHnXOtQxiVICyPOg=="}, "iv": {"type": "string", "description": "初始向量", "example": "kmneH1ouMGszcWXgEqkfhA=="}, "code": {"type": "string", "description": "微信登录凭证（与sessionKey二选一）", "example": "4a0fbec7c332c9e20c04910b287c0183da74ced7bd089b3657a724363f201d67"}, "sessionKey": {"type": "string", "description": "微信会话密钥（与code二选一）", "example": "HyVFkGl5F5OQWJZZaNzBBg=="}, "dataType": {"type": "string", "description": "数据类型（phoneNumber或userInfo）", "example": "phoneNumber"}}, "required": ["encryptedData", "iv"]}, "DecryptedUserInfoResponseDto": {"type": "object", "properties": {"phoneNumber": {"type": "string", "description": "手机号（仅当dataType为phoneNumber时返回）", "example": "13800138000"}, "purePhoneNumber": {"type": "string", "description": "纯手机号（仅当dataType为phoneNumber时返回）", "example": "13800138000"}, "countryCode": {"type": "string", "description": "国家代码（仅当dataType为phoneNumber时返回）", "example": "86"}, "nickName": {"type": "string", "description": "昵称（仅当dataType为userInfo时返回）", "example": "张三"}, "gender": {"type": "number", "description": "性别（仅当dataType为userInfo时返回）", "example": 1}, "language": {"type": "string", "description": "语言（仅当dataType为userInfo时返回）", "example": "zh_CN"}, "city": {"type": "string", "description": "城市（仅当dataType为userInfo时返回）", "example": "Guangzhou"}, "province": {"type": "string", "description": "省份（仅当dataType为userInfo时返回）", "example": "Guangdong"}, "country": {"type": "string", "description": "国家（仅当dataType为userInfo时返回）", "example": "CN"}, "avatarUrl": {"type": "string", "description": "头像URL（仅当dataType为userInfo时返回）", "example": "http://wx.qlogo.cn/mmopen/vi_32/aSKcBBPpibyKNicHNTMM0qJVh8Kjgiak2AHWr8MHM4WgMEm7GFhsf8OYrySdbvAMvTsw3mo8ibKicsnfN5pRjl1p8HQ/0"}, "unionId": {"type": "string", "description": "微信开放平台唯一标识（仅当dataType为userInfo时返回）", "example": "ocMvos6NjeKLIBqg5Mr9QjxrP1FA"}, "watermark": {"type": "object", "description": "水印信息", "example": {"timestamp": 1477314187, "appid": "wx4f4bc4dec97d474b"}}}, "required": ["watermark"]}, "LogoutResponseDto": {"type": "object", "properties": {"message": {"type": "string", "description": "退出登录消息"}, "timestamp": {"type": "string", "description": "退出时间"}}, "required": ["message", "timestamp"]}, "UserResponseDto": {"type": "object", "properties": {"id": {"type": "number", "description": "用户唯一标识"}, "name": {"type": "string", "description": "用户名（昵称）"}, "email": {"type": "string", "description": "用户邮箱（唯一）"}, "role": {"type": "string", "enum": ["admin", "user"], "description": "用户角色"}, "userType": {"type": "string", "enum": ["individual", "institution"], "description": "用户类型：个人或机构"}, "fullName": {"type": "string", "description": "真实姓名（可选）"}, "phoneNumber": {"type": "string", "description": "手机号（唯一）"}, "address": {"type": "string", "description": "地址（可选）"}, "gender": {"type": "string", "enum": ["male", "female", "other"], "description": "性别（可选）"}, "birthDate": {"type": "string", "description": "出生日期（可选）", "format": "date"}, "isActive": {"type": "boolean", "description": "账号是否激活"}, "profilePicture": {"type": "string", "description": "头像链接（可选）"}, "profileBackgroundPicture": {"type": "string", "description": "背景图链接（可选）"}, "membershipType": {"type": "string", "enum": ["free", "basic", "premium", "team"], "description": "会员类型"}, "membershipExpireDate": {"type": "string", "description": "会员过期时间（可选）", "format": "date-time"}, "hasTeamPermission": {"type": "boolean", "description": "是否拥有团队权限"}, "freeAssessmentCount": {"type": "number", "description": "免费评估次数"}, "freeAIInterpretationCount": {"type": "number", "description": "免费 AI 解读次数"}, "totalAssessmentCount": {"type": "number", "description": "总评估次数"}, "totalAIInterpretationCount": {"type": "number", "description": "总 AI 解读次数"}, "lastLoginAt": {"type": "string", "description": "上次登录时间", "format": "date-time"}, "loginCount": {"type": "number", "description": "登录次数"}, "birthDateLocked": {"type": "boolean", "description": "是否锁定出生日期"}, "createdAt": {"type": "string", "description": "创建时间", "format": "date-time"}, "updatedAt": {"type": "string", "description": "更新时间", "format": "date-time"}}, "required": ["id", "name", "email", "role", "userType", "phoneNumber", "isActive", "membershipType", "hasTeamPermission", "freeAssessmentCount", "freeAIInterpretationCount", "totalAssessmentCount", "totalAIInterpretationCount", "loginCount", "birthDateLocked", "createdAt", "updatedAt"]}, "UsageCheckDto": {"type": "object", "properties": {"type": {"type": "string", "enum": ["assessment", "aiInterpretation"], "description": "使用类型：评估或AI解读", "example": "assessment"}}, "required": ["type"]}, "UsageCheckResponseDto": {"type": "object", "properties": {"canUse": {"type": "boolean", "description": "是否可以使用（true表示可以免费使用或会员权限，false表示需要付费）"}, "remainingFreeCount": {"type": "number", "description": "剩余免费次数"}, "isMember": {"type": "boolean", "description": "是否为会员"}, "membershipExpireDate": {"format": "date-time", "type": "string", "description": "会员过期时间（如果是会员）"}, "message": {"type": "string", "description": "提示信息"}}, "required": ["canUse", "remainingFreeCount", "isMember", "message"]}, "MembershipStatusDto": {"type": "object", "properties": {"membershipType": {"type": "string", "enum": ["free", "basic", "premium", "team"], "description": "会员类型"}, "membershipExpireDate": {"type": "string", "description": "会员过期时间", "format": "date-time"}, "hasTeamPermission": {"type": "boolean", "description": "是否拥有团队权限"}, "isActive": {"type": "boolean", "description": "会员是否有效"}, "remainingDays": {"type": "number", "description": "剩余天数（如果会员有效）"}}, "required": ["membershipType", "hasTeamPermission", "isActive"]}, "UpdateMembershipDto": {"type": "object", "properties": {"membershipType": {"type": "string", "enum": ["free", "basic", "premium", "team"], "description": "会员类型"}, "membershipExpireDate": {"type": "string", "description": "会员过期时间", "format": "date-time", "example": "2024-12-31T23:59:59.000Z"}, "hasTeamPermission": {"type": "boolean", "description": "是否拥有团队权限（可选）"}}, "required": ["membershipType", "membershipExpireDate"]}, "UpdateUserDto": {"type": "object", "properties": {"membershipType": {"type": "string", "enum": ["free", "basic", "premium", "team"], "description": "会员类型（可选）"}, "membershipExpireDate": {"type": "string", "description": "会员过期时间（可选）", "format": "date-time"}, "hasTeamPermission": {"type": "boolean", "description": "是否拥有团队权限（可选）"}, "isActive": {"type": "boolean", "description": "是否激活账号（可选）"}, "birthDateLocked": {"type": "boolean", "description": "是否锁定出生日期（可选）"}, "birthCalendarType": {"type": "string", "description": "生日是公历还是阴历（可选）"}}}, "CreateCardDto": {"type": "object", "properties": {}}, "UpdateCardDto": {"type": "object", "properties": {}}, "CreateConversationDto": {"type": "object", "properties": {"title": {"type": "string", "description": "对话标题"}, "initialMessage": {"type": "string", "description": "初始消息"}, "botType": {"type": "string", "enum": ["default", "interpretation", "proto"], "description": "智能体类型", "default": "default"}, "region": {"type": "string", "description": "使用的区域", "default": "zh"}, "metadata": {"type": "object", "description": "对话元数据"}}, "required": ["title", "botType", "region"]}, "UpdateConversationDto": {"type": "object", "properties": {"title": {"type": "string", "description": "对话标题"}, "status": {"type": "string", "enum": ["active", "archived", "deleted"], "description": "对话状态"}, "metadata": {"type": "object", "description": "对话元数据"}}}, "CreateMessageDto": {"type": "object", "properties": {"conversationId": {"type": "string", "description": "对话ID"}, "content": {"type": "string", "description": "消息内容"}, "botType": {"type": "string", "enum": ["default", "interpretation", "proto"], "description": "智能体类型", "default": "default"}, "type": {"type": "string", "enum": ["text", "image", "file", "object_string", "answer", "function_call", "tool_output"], "description": "消息类型", "default": "text"}, "streaming": {"type": "boolean", "description": "是否使用流式响应", "default": false}, "metadata": {"type": "object", "description": "消息元数据"}}, "required": ["conversationId", "content", "botType", "type", "streaming"]}, "AcceptInviteDto": {"type": "object", "properties": {"inviteCode": {"type": "string", "description": "邀请码", "example": "ABC12345", "minLength": 8, "maxLength": 32}}, "required": ["inviteCode"]}, "InviteStatsDto": {"type": "object", "properties": {"totalInvites": {"type": "number", "description": "总邀请数"}, "successfulInvites": {"type": "number", "description": "成功邀请数"}, "pendingInvites": {"type": "number", "description": "待接受邀请数"}, "expiredInvites": {"type": "number", "description": "已过期邀请数"}, "successRate": {"type": "number", "description": "邀请成功率（百分比）"}}, "required": ["totalInvites", "successfulInvites", "pendingInvites", "expiredInvites", "successRate"]}, "MyInviteInfoDto": {"type": "object", "properties": {"myInviteCode": {"type": "string", "description": "我的邀请码"}, "inviteCount": {"type": "number", "description": "邀请人数"}, "stats": {"description": "邀请统计", "allOf": [{"$ref": "#/components/schemas/InviteStatsDto"}]}, "canInviteOthers": {"type": "boolean", "description": "是否可以邀请他人"}, "sentInvites": {"description": "我发送的邀请列表", "type": "array", "items": {"type": "string"}}}, "required": ["myInviteCode", "inviteCount", "stats", "canInviteOthers", "sentInvites"]}, "MyInvitedUsersDto": {"type": "object", "properties": {"myInviteCode": {"type": "string", "description": "我的邀请码"}, "totalCount": {"type": "number", "description": "总邀请人数"}, "invitedUsers": {"description": "使用我邀请码的用户列表", "type": "array", "items": {"type": "string"}}, "summary": {"type": "object", "description": "统计信息"}}, "required": ["myInviteCode", "totalCount", "invitedUsers", "summary"]}, "CreateInviteDto": {"type": "object", "properties": {"type": {"type": "string", "enum": ["direct", "link"], "description": "邀请类型（已废弃：永久邀请码统一为DIRECT类型）", "default": "direct", "example": "direct", "deprecated": true}, "expiresAt": {"type": "string", "description": "邀请过期时间（已废弃：永久邀请码不会过期）", "example": "2024-12-31T23:59:59.000Z", "deprecated": true}, "note": {"type": "string", "description": "邀请备注", "example": "邀请同事加入团队"}, "validHours": {"type": "number", "description": "邀请链接有效期（已废弃：永久邀请码不会过期）", "example": 24, "minimum": 1, "maximum": 168, "deprecated": true}}, "required": ["type"]}, "InviteResponseDto": {"type": "object", "properties": {"id": {"type": "number", "description": "邀请记录ID"}, "inviteCode": {"type": "string", "description": "邀请码"}, "inviterId": {"type": "number", "description": "邀请人ID"}, "inviterName": {"type": "string", "description": "邀请人姓名"}, "inviteeId": {"type": "number", "description": "被邀请人ID"}, "inviteeName": {"type": "string", "description": "被邀请人姓名"}, "status": {"type": "string", "enum": ["pending", "accepted", "expired", "cancelled"], "description": "邀请状态"}, "type": {"type": "string", "enum": ["direct", "link"], "description": "邀请类型"}, "inviteLink": {"type": "string", "description": "邀请链接"}, "expiresAt": {"format": "date-time", "type": "string", "description": "过期时间"}, "acceptedAt": {"format": "date-time", "type": "string", "description": "接受时间"}, "note": {"type": "string", "description": "备注"}, "createdAt": {"format": "date-time", "type": "string", "description": "创建时间"}, "updatedAt": {"format": "date-time", "type": "string", "description": "更新时间"}}, "required": ["id", "inviteCode", "inviterId", "<PERSON><PERSON><PERSON><PERSON>", "status", "type", "createdAt", "updatedAt"]}, "CreatePaymentRequestDto": {"type": "object", "properties": {"userId": {"type": "number", "description": "用户ID", "example": 123}, "type": {"type": "string", "enum": ["team_permission", "assessment_credits", "ai_interpretation_credits"], "description": "支付类型", "example": "team_permission"}, "method": {"type": "string", "enum": ["wechat_pay", "alipay"], "description": "支付方式", "example": "wechat_pay"}, "amount": {"type": "number", "description": "支付金额（分）", "example": 9900, "minimum": 1}, "quantity": {"type": "number", "description": "购买数量（购买次数时使用）", "example": 10, "minimum": 1, "default": 1}, "description": {"type": "string", "description": "支付描述", "example": "开通团队权限"}, "clientIp": {"type": "string", "description": "客户端IP地址", "example": "127.0.0.1"}, "metadata": {"type": "object", "description": "支付扩展信息", "example": {"source": "mini_program", "device": "mobile", "version": "1.0.0"}}, "notifyUrl": {"type": "string", "description": "支付回调地址（可选，使用默认配置）", "example": "https://api.example.com/payment/notify"}, "returnUrl": {"type": "string", "description": "支付成功后跳转地址", "example": "https://app.example.com/payment/success"}}, "required": ["userId", "type", "method", "amount"]}, "CreatePaymentResponseDto": {"type": "object", "properties": {"paymentId": {"type": "number", "description": "支付记录ID"}, "orderNo": {"type": "string", "description": "订单号"}, "paymentParams": {"type": "object", "description": "支付参数（用于前端调起支付）"}, "qrCode": {"type": "string", "description": "支付二维码（扫码支付时）"}, "paymentUrl": {"type": "string", "description": "支付链接（H5支付时）"}, "expiresAt": {"format": "date-time", "type": "string", "description": "支付过期时间"}, "createdAt": {"format": "date-time", "type": "string", "description": "创建时间"}}, "required": ["paymentId", "orderNo", "paymentParams", "expiresAt", "createdAt"]}, "CreateWechatPaymentDto": {"type": "object", "properties": {"userId": {"type": "number", "description": "用户ID", "example": 123}, "type": {"type": "string", "enum": ["team_permission", "assessment_credits", "ai_interpretation_credits"], "description": "支付类型", "example": "team_permission"}, "method": {"type": "string", "enum": ["wechat_pay", "alipay"], "description": "支付方式", "example": "wechat_pay"}, "amount": {"type": "number", "description": "支付金额（分）", "example": 9900, "minimum": 1}, "quantity": {"type": "number", "description": "购买数量（购买次数时使用）", "example": 10, "minimum": 1, "default": 1}, "description": {"type": "string", "description": "支付描述", "example": "开通团队权限"}, "clientIp": {"type": "string", "description": "客户端IP地址", "example": "127.0.0.1"}, "metadata": {"type": "object", "description": "支付扩展信息", "example": {"source": "mini_program", "device": "mobile", "version": "1.0.0"}}, "notifyUrl": {"type": "string", "description": "支付回调地址（可选，使用默认配置）", "example": "https://api.example.com/payment/notify"}, "returnUrl": {"type": "string", "description": "支付成功后跳转地址", "example": "https://app.example.com/payment/success"}, "openid": {"type": "string", "description": "微信用户openid", "example": "oUpF8uMuAJO_M2pxb1Q9zNjWeS6o"}, "appid": {"type": "string", "description": "微信小程序appid（可选，使用默认配置）", "example": "wx1234567890abcdef"}}, "required": ["userId", "type", "method", "amount", "openid"]}, "PaymentResponseDto": {"type": "object", "properties": {"id": {"type": "number", "description": "支付记录ID"}, "orderNo": {"type": "string", "description": "订单号"}, "userId": {"type": "number", "description": "用户ID"}, "status": {"type": "string", "enum": ["pending", "processing", "success", "failed", "cancelled", "refunded"], "description": "支付状态"}, "amount": {"type": "number", "description": "支付金额（分）"}, "description": {"type": "string", "description": "支付描述"}, "thirdPartyTransactionId": {"type": "string", "description": "第三方支付平台交易号"}, "paidAt": {"format": "date-time", "type": "string", "description": "支付完成时间"}, "failureReason": {"type": "string", "description": "失败原因"}, "createdAt": {"format": "date-time", "type": "string", "description": "创建时间"}, "updatedAt": {"format": "date-time", "type": "string", "description": "更新时间"}}, "required": ["id", "orderNo", "userId", "status", "amount", "createdAt", "updatedAt"]}, "UpdatePaymentStatusDto": {"type": "object", "properties": {"status": {"type": "string", "enum": ["pending", "processing", "success", "failed", "cancelled", "refunded"], "description": "支付状态", "example": "success"}, "thirdPartyTransactionId": {"type": "string", "description": "第三方支付平台交易号", "example": "wx_transaction_123456789"}, "failureReason": {"type": "string", "description": "失败原因（支付失败时必填）", "example": "余额不足"}, "thirdPartyResponse": {"type": "object", "description": "第三方支付响应数据", "example": {"trade_state": "SUCCESS", "bank_type": "CMC"}}, "paidAt": {"type": "string", "description": "支付完成时间（ISO 8601格式）", "example": "2024-01-15T10:30:00Z"}, "refundedAt": {"type": "string", "description": "退款时间（ISO 8601格式）", "example": "2024-01-16T14:20:00Z"}, "refundAmount": {"type": "number", "description": "退款金额（分）", "example": 9900, "minimum": 0}, "metadata": {"type": "object", "description": "支付扩展信息", "example": {"channel": "wechat_mini_program", "device": "mobile"}}}, "required": ["status"]}, "PaymentStatsResponseDto": {"type": "object", "properties": {"totalPayments": {"type": "number", "description": "总支付次数"}, "successfulPayments": {"type": "number", "description": "成功支付次数"}, "failedPayments": {"type": "number", "description": "失败支付次数"}, "totalAmount": {"type": "number", "description": "总支付金额（分）"}, "successfulAmount": {"type": "number", "description": "成功支付金额（分）"}, "successRate": {"type": "number", "description": "成功率（百分比）"}}, "required": ["totalPayments", "successfulPayments", "failedPayments", "totalAmount", "successfulAmount", "successRate"]}, "PaymentVerificationDto": {"type": "object", "properties": {"orderNo": {"type": "string", "description": "订单号", "example": "ORDER_20240115103000123456"}, "thirdPartyTransactionId": {"type": "string", "description": "第三方交易号", "example": "wx_transaction_123456789"}}, "required": ["orderNo"]}, "CreateQueryTemplateDto": {"type": "object", "properties": {}}, "DateQueryDto": {"type": "object", "properties": {}}, "CalculateResultDto": {"type": "object", "properties": {"answers": {"description": "用户答案数组，索引对应题目ID，值为选项索引（0-3）", "example": [0, 1, 2, 3, 0, 1, 2, 3, 0, 1, 2, 3, 0, 1, 2, 3, 0, 1, 2, 3, 0, 1, 2, 3, 0, 1, 2, 3, 0, 1, 2, 3], "type": "array", "items": {"type": "number"}}, "userId": {"type": "number", "description": "用户ID，如果提供则会保存到用户历史记录", "example": 1}, "remark": {"type": "string", "description": "备注信息", "example": "第一次测试"}}, "required": ["answers"]}, "MbtiResultDto": {"type": "object", "properties": {"E": {"type": "number", "description": "外向-内向维度", "example": {"E": 5, "I": 3}}, "I": {"type": "number", "description": "内向-外向维度", "example": {"E": 5, "I": 3}}, "S": {"type": "number", "description": "感觉-直觉维度", "example": {"S": 4, "N": 4}}, "N": {"type": "number", "description": "直觉-感觉维度", "example": {"S": 4, "N": 4}}, "T": {"type": "number", "description": "思考-情感维度", "example": {"T": 6, "F": 2}}, "F": {"type": "number", "description": "情感-思考维度", "example": {"T": 6, "F": 2}}, "J": {"type": "number", "description": "判断-知觉维度", "example": {"J": 3, "P": 5}}, "P": {"type": "number", "description": "知觉-判断维度", "example": {"J": 3, "P": 5}}}, "required": ["E", "I", "S", "N", "T", "F", "J", "P"]}, "DiscResultDto": {"type": "object", "properties": {"D": {"type": "number", "description": "支配型", "example": 8}, "I": {"type": "number", "description": "影响型", "example": 6}, "S": {"type": "number", "description": "稳健型", "example": 10}, "C": {"type": "number", "description": "谨慎型", "example": 8}}, "required": ["D", "I", "S", "C"]}, "CalculateResultResponseDto": {"type": "object", "properties": {"mbti": {"description": "MBTI各维度得分", "allOf": [{"$ref": "#/components/schemas/MbtiResultDto"}]}, "disc": {"description": "DISC各维度得分", "allOf": [{"$ref": "#/components/schemas/DiscResultDto"}]}, "mbtiType": {"type": "string", "description": "MBTI类型", "example": "ENFP"}, "discType": {"type": "string", "description": "DISC主导风格", "example": "S"}, "finalType": {"type": "string", "description": "最终组合类型", "example": "ENFP-S"}, "dimensionScores": {"type": "object", "description": "各维度详细得分"}}, "required": ["mbti", "disc", "mbtiType", "discType", "finalType", "dimensionScores"]}, "CreatePersonalityResultDto": {"type": "object", "properties": {}}, "PersonalityResultResponseDto": {"type": "object", "properties": {}}, "GetPersonalityResultDto": {"type": "object", "properties": {}}, "CalculateResultWithPersonalityResponseDto": {"type": "object", "properties": {"mbti": {"description": "MBTI各维度得分", "allOf": [{"$ref": "#/components/schemas/MbtiResultDto"}]}, "disc": {"description": "DISC各维度得分", "allOf": [{"$ref": "#/components/schemas/DiscResultDto"}]}, "mbtiType": {"type": "string", "description": "MBTI类型", "example": "ISTJ"}, "discType": {"type": "string", "description": "DISC主导风格", "example": "D"}, "finalType": {"type": "string", "description": "最终组合类型", "example": "ISTJ-D"}, "dimensionScores": {"type": "object", "description": "各维度详细得分"}, "personalityResult": {"type": "object", "description": "人格测试结果详情", "example": {"typeCode": "ISTJ-D", "title": "规则凝聚者", "coreTraits": "以身作则推进制度，强硬把控细节中凝聚团队忠诚", "strengths": "极高目标达成率与团队向心力并重", "sceneMatch": "危机管理/制度落地/军官/领导", "blindSpots": "压制异议倾向，易陷入任人唯亲，追求虚名", "suggestions": "主动倾听反馈，建立匿名吐槽机制", "symbol": "师", "lifePath": "怎样从带团队到做一名游刃有余的一把手", "valueGuide": "⚠️ 团队因压制异议分崩离析？制度落地沦为虚名？<br>💎 以身作则锻造铁军，建立匿名吐槽机制 ，制定《忠诚团队构建蓝图》"}}, "savedToHistory": {"type": "boolean", "description": "是否已保存到用户历史记录", "example": true}}, "required": ["mbti", "disc", "mbtiType", "discType", "finalType", "dimensionScores", "personalityResult", "savedToHistory"]}, "CreateUserAnswerRecordDto": {"type": "object", "properties": {}}, "UserAnswerRecordResponseDto": {"type": "object", "properties": {}}, "PaginatedUserAnswerRecordsResponseDto": {"type": "object", "properties": {}}, "GetUserCurrentTypeDto": {"type": "object", "properties": {}}, "UserCurrentTypeResponseDto": {"type": "object", "properties": {}}}}}