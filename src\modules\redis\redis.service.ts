import { Injectable } from "@nestjs/common";
import { InjectRedis } from "@nestjs-modules/ioredis";
import Redis from "ioredis";

@Injectable()
export class RedisService {
  constructor(@InjectRedis() private readonly redis: Redis) { }
  /**
   * 存储验证码
   * @param phoneNumber 手机号
   * @param code 验证码
   * @param ttl 过期时间（秒）
   */
  async setCaptcha(
    phoneNumber: string,
    code: string,
    ttl: number = 300
  ): Promise<void> {
    const key = this.getCaptchaKey(phoneNumber);
    await this.redis.setex(key, ttl, code);
  }

  /**
   * 获取验证码
   * @param phoneNumber 手机号
   * @returns 验证码或null
   */
  async getCaptcha(phoneNumber: string): Promise<string | null> {
    const key = this.getCaptchaKey(phoneNumber);
    return await this.redis.get(key);
  }

  /**
   * 删除验证码
   * @param phoneNumber 手机号
   */
  async deleteCaptcha(phoneNumber: string): Promise<void> {
    const key = this.getCaptchaKey(phoneNumber);
    await this.redis.del(key);
  }

  /**
   * 检查验证码是否存在
   * @param phoneNumber 手机号
   * @returns boolean
   */
  async captchaExists(phoneNumber: string): Promise<boolean> {
    const key = this.getCaptchaKey(phoneNumber);
    const exists = await this.redis.exists(key);
    return exists === 1;
  }

  /**
   * 获取验证码剩余过期时间
   * @param phoneNumber 手机号
   * @returns 剩余秒数，-1表示key不存在，-2表示没有设置过期时间
   */
  async getCaptchaTTL(phoneNumber: string): Promise<number> {
    const key = this.getCaptchaKey(phoneNumber);
    return await this.redis.ttl(key);
  }

  /**
   * 设置发送频率限制
   * @param phoneNumber 手机号
   * @param ttl 限制时间（秒）
   */
  async setRateLimit(phoneNumber: string, ttl: number = 60): Promise<void> {
    const key = this.getRateLimitKey(phoneNumber);
    await this.redis.setex(key, ttl, "1");
  }

  /**
   * 检查是否在发送频率限制内
   * @param phoneNumber 手机号
   * @returns boolean
   */
  async isRateLimited(phoneNumber: string): Promise<boolean> {
    const key = this.getRateLimitKey(phoneNumber);
    const exists = await this.redis.exists(key);
    return exists === 1;
  }

  /**
   * 生成验证码缓存key
   * @param phoneNumber 手机号
   * @returns string
   */
  private getCaptchaKey(phoneNumber: string): string {
    return `captcha:${phoneNumber}`;
  }

  /**
   * 生成频率限制key
   * @param phoneNumber 手机号
   * @returns string
   */
  private getRateLimitKey(phoneNumber: string): string {
    return `rate_limit:${phoneNumber}`;
  }

  /**
   * 通用的Redis操作方法
   */

  /**
   * 设置键值对
   * @param key 键
   * @param value 值
   * @param ttl 过期时间（秒）
   */
  async set(key: string, value: string, ttl?: number): Promise<void> {
    if (ttl) {
      await this.redis.setex(key, ttl, value);
    } else {
      await this.redis.set(key, value);
    }
  }

  /**
   * 获取值
   * @param key 键
   * @returns 值或null
   */
  async get(key: string): Promise<string | null> {
    return await this.redis.get(key);
  }

  /**
   * 删除键
   * @param key 键
   */
  async delete(key: string): Promise<void> {
    await this.redis.del(key);
  }

  /**
   * 检查键是否存在
   * @param key 键
   * @returns boolean
   */
  async exists(key: string): Promise<boolean> {
    const exists = await this.redis.exists(key);
    return exists === 1;
  }

  /**
   * 设置过期时间
   * @param key 键
   * @param ttl 过期时间（秒）
   */
  async expire(key: string, ttl: number): Promise<void> {
    await this.redis.expire(key, ttl);
  }

  /**
   * 获取剩余过期时间
   * @param key 键
   * @returns 剩余秒数
   */
  async ttl(key: string): Promise<number> {
    return await this.redis.ttl(key);
  }
  /**
 * 聊天会话相关的Redis操作
 */

  /**
   * 设置用户当前活跃对话
   * @param userId 用户ID
   * @param conversationId 对话ID
   * @param ttl 过期时间（秒）
   */
  async setUserActiveConversation(userId: number, conversationId: string, ttl: number = 3600): Promise<void> {
    const key = this.getUserActiveConversationKey(userId);
    await this.redis.setex(key, ttl, conversationId);
  }

  /**
   * 获取用户当前活跃对话
   * @param userId 用户ID
   * @returns 对话ID或null
   */
  async getUserActiveConversation(userId: number): Promise<string | null> {
    const key = this.getUserActiveConversationKey(userId);
    return await this.redis.get(key);
  }

  /**
   * 删除用户当前活跃对话
   * @param userId 用户ID
   */
  async deleteUserActiveConversation(userId: number): Promise<void> {
    const key = this.getUserActiveConversationKey(userId);
    await this.redis.del(key);
  }

  /**
   * 设置流式聊天状态
   * @param conversationId 对话ID
   * @param chatId 聊天ID
   * @param state 状态数据
   * @param ttl 过期时间（秒）
   */
  async setStreamingState(conversationId: string, chatId: string, state: any, ttl: number = 1800): Promise<void> {
    const key = this.getStreamingStateKey(conversationId, chatId);
    await this.redis.setex(key, ttl, JSON.stringify(state));
  }

  /**
   * 获取流式聊天状态
   * @param conversationId 对话ID
   * @param chatId 聊天ID
   * @returns 状态数据或null
   */
  async getStreamingState(conversationId: string, chatId: string): Promise<any> {
    const key = this.getStreamingStateKey(conversationId, chatId);
    const data = await this.redis.get(key);
    return data ? JSON.parse(data) : null;
  }

  /**
   * 删除流式聊天状态
   * @param conversationId 对话ID
   * @param chatId 聊天ID
   */
  async deleteStreamingState(conversationId: string, chatId: string): Promise<void> {
    const key = this.getStreamingStateKey(conversationId, chatId);
    await this.redis.del(key);
  }

  /**
   * 设置聊天会话信息
   * @param sessionId 会话ID
   * @param sessionData 会话数据
   * @param ttl 过期时间（秒）
   */
  async setChatSession(sessionId: string, sessionData: any, ttl: number = 7200): Promise<void> {
    const key = this.getChatSessionKey(sessionId);
    await this.redis.setex(key, ttl, JSON.stringify(sessionData));
  }

  /**
   * 获取聊天会话信息
   * @param sessionId 会话ID
   * @returns 会话数据或null
   */
  async getChatSession(sessionId: string): Promise<any> {
    const key = this.getChatSessionKey(sessionId);
    const data = await this.redis.get(key);
    return data ? JSON.parse(data) : null;
  }

  /**
   * 删除聊天会话信息
   * @param sessionId 会话ID
   */
  async deleteChatSession(sessionId: string): Promise<void> {
    const key = this.getChatSessionKey(sessionId);
    await this.redis.del(key);
  }

  /**
   * 私有方法：生成Redis键
   */
  private getUserActiveConversationKey(userId: number): string {
    return `user_active_conversation:${userId}`;
  }

  private getStreamingStateKey(conversationId: string, chatId: string): string {
    return `streaming_state:${conversationId}:${chatId}`;
  }

  private getChatSessionKey(sessionId: string): string {
    return `chat_session:${sessionId}`;
  }
}
