import {
  Controller,
  Post,
  Get,
  Put,
  Delete,
  Body,
  Param,
  Query,
  Res,
  UseGuards,
  Request,
  HttpStatus,
  ParseUUIDPipe,
  ParseIntPipe,
} from '@nestjs/common';
import { Response } from 'express';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiBearerAuth,
  ApiParam,
  ApiQuery,
  ApiBody,
  ApiConsumes,
  ApiProduces
} from '@nestjs/swagger';
import { CozeService } from './coze.service';
import { AuthGuard } from '../../guards/auth.guard';
import { CreateConversationDto, UpdateConversationDto, ConversationListDto } from './dto/conversation.dto';
import { CreateMessageDto, MessageListDto } from './dto/message.dto';
import { BotType } from './entities/conversation.entity';

/**
 * AI聊天管理控制器
 * 
 * 本控制器提供完整的AI聊天功能，包括对话管理和消息处理。
 * 支持标准模式和流式模式的AI对话。
 * 
 * 重要枚举类型说明：
 * 
 * ConversationStatus（对话状态）:
 * - active: 活跃状态
 * - archived: 已归档
 * - deleted: 已删除
 * 
 * MessageRole（消息角色）:
 * - user: 用户消息
 * - assistant: AI助手消息
 * - system: 系统消息
 * 
 * MessageType（消息类型）:
 * - text: 纯文本消息
 * - image: 图片消息
 * - file: 文件消息
 * - object_string: 结构化数据消息
 * - answer: AI回答消息
 * - function_call: 函数调用消息
 * - tool_output: 工具输出消息
 * 
 * MessageStatus（消息状态）:
 * - pending: 处理中
 * - completed: 已完成
 * - failed: 处理失败
 * 
 * BotType（智能体类型）:
 * - default: 默认智能体
 * - interpretation: 解读智能体
 * - proto: 原型智能体
 */
@ApiTags('AI聊天管理')
@Controller('coze')
@UseGuards(AuthGuard)
@ApiBearerAuth('JWT')
export class CozeController {
  constructor(private readonly cozeService: CozeService) { }

  @Post('conversations')
  @ApiOperation({
    summary: '创建新的AI对话会话（默认智能体）',
    description: '为当前用户创建一个新的AI聊天对话会话。每个对话会话都有唯一的ID，用于后续的消息交互。'
  })
  @ApiBody({
    type: CreateConversationDto,
    description: '创建对话的请求参数',
    examples: {
      basic: {
        summary: '基础对话创建',
        description: '创建一个简单的对话会话',
        value: {
          title: '我的AI编程助手',
          botType: 'default'
        }
      },
      withInitialMessage: {
        summary: '包含初始消息的对话',
        description: '创建对话并发送第一条消息',
        value: {
          title: '深度学习讨论',
          initialMessage: '你好，我想了解深度学习的基础概念',
          botType: 'default',
          metadata: {
            topic: 'machine_learning',
            difficulty: 'beginner'
          }
        }
      }
    }
  })
  @ApiResponse({
    status: HttpStatus.CREATED,
    description: '对话创建成功',
    schema: {
      type: 'object',
      properties: {
        code: { type: 'number', example: 200 },
        message: { type: 'string', example: '对话创建成功' },
        data: {
          type: 'object',
          properties: {
            id: { type: 'string', example: 'uuid' },
            title: { type: 'string', example: '我的AI编程助手' },
            status: { type: 'string', example: 'active' },
            botType: { type: 'string', example: 'default' },
            messageCount: { type: 'number', example: 0 },
            createdAt: { type: 'string', format: 'date-time' },
            lastActiveAt: { type: 'string', format: 'date-time' }
          }
        }
      }
    }
  })
  async createConversation(@Request() req: any, @Body() createDto: CreateConversationDto) {
    createDto.botType = BotType.DEFAULT;
    const conversation = await this.cozeService.createConversation(req.user.sub, createDto);
    return {
      code: 200,
      message: '对话创建成功',
      data: conversation
    };
  }

  @Post('interpretation/conversations')
  @ApiOperation({
    summary: '创建新的AI对话会话（解读智能体）',
    description: '为当前用户创建一个新的解读智能体对话会话。'
  })
  @ApiBody({
    type: CreateConversationDto,
    description: '创建对话的请求参数'
  })
  async createInterpretationConversation(@Request() req: any, @Body() createDto: CreateConversationDto) {
    // 使用解读智能体
    createDto.botType = BotType.INTERPRETATION;
    const conversation = await this.cozeService.createConversation(req.user.sub, createDto);
    return {
      code: 200,
      message: '解读对话创建成功',
      data: conversation
    };
  }

  @Post('proto/conversations')
  @ApiOperation({
    summary: '创建新的AI对话会话（原型智能体）',
    description: '为当前用户创建一个新的原型智能体对话会话。'
  })
  @ApiBody({
    type: CreateConversationDto,
    description: '创建对话的请求参数'
  })
  async createProtoConversation(@Request() req: any, @Body() createDto: CreateConversationDto) {
    // 使用原型智能体
    createDto.botType = BotType.PROTO;
    const conversation = await this.cozeService.createConversation(req.user.sub, createDto);
    return {
      code: 200,
      message: '原型对话创建成功',
      data: conversation
    };
  }

  @Get('conversations')
  @ApiOperation({
    summary: '获取用户的AI对话列表',
    description: '分页获取当前用户的所有AI对话会话列表，支持按创建时间倒序排列。'
  })
  @ApiQuery({
    name: 'page',
    required: false,
    type: Number,
    description: '页码，从1开始',
    example: 1
  })
  @ApiQuery({
    name: 'pageSize',
    required: false,
    type: Number,
    description: '每页显示数量，默认20条，最大100条',
    example: 20
  })
  @ApiResponse({
    status: 200,
    description: '对话列表获取成功',
    schema: {
      type: 'object',
      properties: {
        data: {
          type: 'array',
          items: {
            type: 'object',
            properties: {
              id: { type: 'string', format: 'uuid', description: '对话唯一标识符' },
              title: { type: 'string', description: '对话标题' },
              status: { type: 'string', enum: ['active', 'archived', 'deleted'], description: '对话状态' },
              messageCount: { type: 'number', description: '消息总数' },
              lastMessageAt: { type: 'string', format: 'date-time', description: '最后一条消息时间' },
              lastActiveAt: { type: 'string', format: 'date-time', description: '最后活跃时间' },
              createdAt: { type: 'string', format: 'date-time', description: '创建时间' },
              updatedAt: { type: 'string', format: 'date-time', description: '更新时间' },
              metadata: { type: 'object', description: '对话元数据' }
            }
          }
        },
        pagination: {
          type: 'object',
          properties: {
            total: { type: 'number', description: '总记录数' },
            page: { type: 'number', description: '当前页码' },
            pageSize: { type: 'number', description: '每页大小' },
            totalPages: { type: 'number', description: '总页数' },
            hasNext: { type: 'boolean', description: '是否有下一页' },
            hasPrev: { type: 'boolean', description: '是否有上一页' }
          }
        }
      },
      example: {
        data: [
          {
            id: '550e8400-e29b-41d4-a716-************',
            title: 'Python Web开发学习',
            status: 'active',
            messageCount: 12,
            lastMessageAt: '2024-01-15T14:30:00Z',
            lastActiveAt: '2024-01-15T14:30:00Z',
            createdAt: '2024-01-15T10:00:00Z',
            updatedAt: '2024-01-15T14:30:00Z',
            metadata: {
              topic: 'programming',
              difficulty: 'intermediate'
            }
          },
          {
            id: '123e4567-e89b-12d3-a456-426614174000',
            title: '机器学习基础讨论',
            status: 'archived',
            messageCount: 8,
            lastMessageAt: '2024-01-14T16:45:00Z',
            lastActiveAt: '2024-01-14T16:45:00Z',
            createdAt: '2024-01-14T15:00:00Z',
            updatedAt: '2024-01-14T17:00:00Z',
            metadata: {
              topic: 'machine_learning',
              completed: true
            }
          }
        ],
        pagination: {
          total: 25,
          page: 1,
          pageSize: 20,
          totalPages: 2,
          hasNext: true,
          hasPrev: false
        }
      }
    }
  })
  @ApiResponse({ status: 401, description: '用户未认证，请先登录' })
  @ApiResponse({ status: 403, description: '用户权限不足' })
  @ApiResponse({ status: 500, description: '服务器内部错误' })
  async getUserConversations(
    @Request() req: any,
    @Query('page', new ParseIntPipe({ optional: true })) page?: number,
    @Query('pageSize', new ParseIntPipe({ optional: true })) pageSize?: number,
  ) {
    const userId = req.user?.sub || 1;
    const finalPage = page || 1;
    const finalPageSize = pageSize || 20;
    return await this.cozeService.getUserConversations(userId, finalPage, finalPageSize, BotType.DEFAULT);
  }

  @Get('interpretation/conversations')
  @ApiOperation({
    summary: '获取用户的解读智能体对话列表',
    description: '获取当前用户的所有解读智能体对话会话列表，支持分页查询。'
  })
  @ApiQuery({
    name: 'page',
    type: 'number',
    required: false,
    description: '页码，默认为1',
    example: 1
  })
  @ApiQuery({
    name: 'pageSize',
    type: 'number',
    required: false,
    description: '每页数量，默认为20',
    example: 20
  })
  async getInterpretationConversations(
    @Request() req: any,
    @Query('page', new ParseIntPipe({ optional: true })) page?: number,
    @Query('pageSize', new ParseIntPipe({ optional: true })) pageSize?: number,
  ) {
    const userId = req.user?.sub || 1;
    const finalPage = page || 1;
    const finalPageSize = pageSize || 20;
    return await this.cozeService.getUserConversations(userId, finalPage, finalPageSize, BotType.INTERPRETATION);
  }

  @Get('proto/conversations')
  @ApiOperation({
    summary: '获取用户的原型智能体对话列表',
    description: '获取当前用户的所有原型智能体对话会话列表，支持分页查询。'
  })
  @ApiQuery({
    name: 'page',
    type: 'number',
    required: false,
    description: '页码，默认为1',
    example: 1
  })
  @ApiQuery({
    name: 'pageSize',
    type: 'number',
    required: false,
    description: '每页数量，默认为20',
    example: 20
  })
  async getProtoConversations(
    @Request() req: any,
    @Query('page', new ParseIntPipe({ optional: true })) page?: number,
    @Query('pageSize', new ParseIntPipe({ optional: true })) pageSize?: number,
  ) {
    const userId = req.user?.sub || 1;
    const finalPage = page || 1;
    const finalPageSize = pageSize || 20;
    return await this.cozeService.getUserConversations(userId, finalPage, finalPageSize, BotType.PROTO);
  }

  @Get('conversations/:id')
  @ApiOperation({
    summary: '获取指定对话的详细信息',
    description: '根据对话ID获取对话的详细信息，包括对话的基本信息和最近的消息概览。'
  })
  @ApiParam({
    name: 'id',
    type: 'string',
    format: 'uuid',
    description: '对话的唯一标识符',
    example: '550e8400-e29b-41d4-a716-************'
  })
  @ApiResponse({
    status: 200,
    description: '对话详情获取成功',
    schema: {
      type: 'object',
      properties: {
        id: { type: 'string', format: 'uuid' },
        title: { type: 'string' },
        description: { type: 'string' },
        userId: { type: 'string', format: 'uuid' },
        messageCount: { type: 'number' },
        lastMessageAt: { type: 'string', format: 'date-time' },
        createdAt: { type: 'string', format: 'date-time' },
        updatedAt: { type: 'string', format: 'date-time' }
      }
    }
  })
  @ApiResponse({ status: 400, description: '对话ID格式错误' })
  @ApiResponse({ status: 401, description: '用户未认证，请先登录' })
  @ApiResponse({ status: 403, description: '无权限访问该对话' })
  @ApiResponse({ status: 404, description: '对话不存在' })
  @ApiResponse({ status: 500, description: '服务器内部错误' })
  async getConversation(
    @Request() req: any,
    @Param('id', ParseUUIDPipe) conversationId: string,
  ) {
    // 临时处理：如果没有用户认证，使用测试用户ID
    const userId = req.user?.sub || 1;
    return this.cozeService.getConversation(conversationId, userId);
  }

  @Put('conversations/:id')
  @ApiOperation({
    summary: '更新对话信息',
    description: '更新指定对话的标题、描述等基本信息。只有对话的创建者可以更新对话信息。'
  })
  @ApiParam({
    name: 'id',
    type: 'string',
    format: 'uuid',
    description: '对话的唯一标识符',
    example: '550e8400-e29b-41d4-a716-************'
  })
  @ApiBody({
    type: UpdateConversationDto,
    description: '更新对话的请求参数',
    examples: {
      updateTitle: {
        summary: '更新对话标题',
        description: '仅更新对话的标题',
        value: {
          title: '新的对话标题 - Python Web开发'
        }
      },
      updateStatus: {
        summary: '更新对话状态',
        description: '将对话标记为已归档',
        value: {
          status: 'archived'
        }
      },
      updateAll: {
        summary: '更新所有可选字段',
        description: '同时更新标题、状态和元数据',
        value: {
          title: '已完成的项目讨论',
          status: 'archived',
          metadata: {
            completedAt: '2024-01-15T10:30:00Z',
            rating: 5,
            tags: ['completed', 'helpful', 'programming']
          }
        }
      },
      updateMetadata: {
        summary: '仅更新元数据',
        description: '添加或更新对话的元数据信息',
        value: {
          metadata: {
            lastTopic: 'API设计',
            complexity: 'intermediate',
            estimatedDuration: '45分钟'
          }
        }
      }
    }
  })
  @ApiResponse({
    status: 200,
    description: '对话更新成功',
    schema: {
      type: 'object',
      properties: {
        id: { type: 'string', format: 'uuid' },
        title: { type: 'string' },
        description: { type: 'string' },
        updatedAt: { type: 'string', format: 'date-time' }
      }
    }
  })
  @ApiResponse({ status: 400, description: '请求参数错误或对话ID格式错误' })
  @ApiResponse({ status: 401, description: '用户未认证，请先登录' })
  @ApiResponse({ status: 403, description: '无权限更新该对话' })
  @ApiResponse({ status: 404, description: '对话不存在' })
  @ApiResponse({ status: 500, description: '服务器内部错误' })
  async updateConversation(
    @Request() req: any,
    @Param('id', ParseUUIDPipe) conversationId: string,
    @Body() updateDto: UpdateConversationDto,
  ) {
    // 临时处理：如果没有用户认证，使用测试用户ID
    const userId = req.user?.sub || 1;
    return this.cozeService.updateConversation(conversationId, userId, updateDto);
  }

  @Delete('conversations/:id')
  @ApiOperation({
    summary: '删除对话',
    description: '删除指定的对话及其所有相关消息。此操作不可逆，只有对话的创建者可以删除对话。'
  })
  @ApiParam({
    name: 'id',
    type: 'string',
    format: 'uuid',
    description: '要删除的对话唯一标识符',
    example: '550e8400-e29b-41d4-a716-************'
  })
  @ApiResponse({
    status: 200,
    description: '对话删除成功',
    schema: {
      type: 'object',
      properties: {
        success: { type: 'boolean', description: '删除操作是否成功' }
      }
    }
  })
  @ApiResponse({ status: 400, description: '对话ID格式错误' })
  @ApiResponse({ status: 401, description: '用户未认证，请先登录' })
  @ApiResponse({ status: 403, description: '无权限删除该对话' })
  @ApiResponse({ status: 404, description: '对话不存在' })
  @ApiResponse({ status: 500, description: '服务器内部错误' })
  async deleteConversation(
    @Request() req: any,
    @Param('id', ParseUUIDPipe) conversationId: string,
  ) {
    // 临时处理：如果没有用户认证，使用测试用户ID
    const userId = req.user?.sub || 1;
    const result = await this.cozeService.deleteConversation(conversationId, userId);
    return { success: result };
  }

  @Post('messages')
  @ApiOperation({
    summary: '发送AI消息（标准模式）',
    description: '向AI发送消息并获取回复。使用标准请求-响应模式，等待AI完成回复后一次性返回完整结果。适用于短文本对话。'
  })
  @ApiBody({
    type: CreateMessageDto,
    description: '发送消息的请求参数',
    examples: {
      simpleText: {
        summary: '简单文本消息',
        description: '发送一个基础的文本消息给AI',
        value: {
          conversationId: '550e8400-e29b-41d4-a716-************',
          content: '你好，请帮我解释一下什么是机器学习？',
          type: 'text',
          streaming: false
        }
      },
      programmingQuestion: {
        summary: '编程相关问题',
        description: '询问具体的编程问题',
        value: {
          conversationId: '550e8400-e29b-41d4-a716-************',
          content: '如何在TypeScript中实现单例模式？请提供代码示例。',
          type: 'text',
          streaming: false,
          metadata: {
            language: 'typescript',
            category: 'design_pattern',
            difficulty: 'intermediate'
          }
        }
      },
      codeReview: {
        summary: '代码审查请求',
        description: '请求AI审查代码片段',
        value: {
          conversationId: '550e8400-e29b-41d4-a716-************',
          content: '请帮我审查这段代码：\n```javascript\nfunction fetchUser(id) {\n  return fetch(`/api/users/${id}`).then(res => res.json());\n}\n```\n有什么可以改进的地方吗？',
          type: 'text',
          streaming: false,
          metadata: {
            requestType: 'code_review',
            language: 'javascript',
            focusAreas: ['error_handling', 'best_practices']
          }
        }
      },
      withObjectString: {
        summary: '结构化数据消息',
        description: '发送包含结构化数据的消息',
        value: {
          conversationId: '550e8400-e29b-41d4-a716-************',
          content: '分析这个API响应数据的结构',
          type: 'object_string',
          streaming: false,
          metadata: {
            dataType: 'api_response',
            format: 'json'
          }
        }
      }
    }
  })
  @ApiResponse({
    status: 201,
    description: '消息发送成功，返回用户消息和AI回复的完整信息',
    schema: {
      type: 'object',
      properties: {
        userMessage: {
          type: 'object',
          properties: {
            id: { type: 'string', format: 'uuid', description: '用户消息ID' },
            conversationId: { type: 'string', format: 'uuid', description: '对话ID' },
            content: { type: 'string', description: '用户发送的消息内容' },
            role: { type: 'string', enum: ['user'], description: '消息角色' },
            type: { type: 'string', enum: ['text', 'image', 'file', 'object_string'], description: '消息类型' },
            status: { type: 'string', enum: ['pending', 'completed', 'failed'], description: '消息状态' },
            metadata: { type: 'object', description: '消息元数据' },
            createdAt: { type: 'string', format: 'date-time', description: '创建时间' }
          }
        },
        aiMessage: {
          type: 'object',
          properties: {
            id: { type: 'string', format: 'uuid', description: 'AI消息ID' },
            conversationId: { type: 'string', format: 'uuid', description: '对话ID' },
            content: { type: 'string', description: 'AI回复的内容' },
            role: { type: 'string', enum: ['assistant'], description: '消息角色' },
            type: { type: 'string', enum: ['text', 'answer', 'function_call', 'tool_output'], description: '消息类型' },
            status: { type: 'string', enum: ['completed', 'failed'], description: '消息状态' },
            metadata: { type: 'object', description: '消息元数据' },
            usage: {
              type: 'object',
              properties: {
                promptTokens: { type: 'number', description: '输入Token数量' },
                completionTokens: { type: 'number', description: '输出Token数量' },
                totalTokens: { type: 'number', description: '总Token数量' }
              },
              description: 'Token使用量统计'
            },
            createdAt: { type: 'string', format: 'date-time', description: '创建时间' },
            completedAt: { type: 'string', format: 'date-time', description: '完成时间' }
          }
        }
      },
      example: {
        userMessage: {
          id: '123e4567-e89b-12d3-a456-426614174001',
          conversationId: '550e8400-e29b-41d4-a716-************',
          content: '你好，请帮我解释一下什么是机器学习？',
          role: 'user',
          type: 'text',
          status: 'completed',
          metadata: {},
          createdAt: '2024-01-15T10:30:00Z'
        },
        aiMessage: {
          id: '123e4567-e89b-12d3-a456-426614174002',
          conversationId: '550e8400-e29b-41d4-a716-************',
          content: '机器学习是人工智能的一个分支，它使计算机能够在没有明确编程的情况下学习和改进性能...',
          role: 'assistant',
          type: 'answer',
          status: 'completed',
          metadata: {
            model: 'gpt-3.5-turbo',
            temperature: 0.7
          },
          usage: {
            promptTokens: 25,
            completionTokens: 150,
            totalTokens: 175
          },
          createdAt: '2024-01-15T10:30:01Z',
          completedAt: '2024-01-15T10:30:05Z'
        }
      }
    }
  })
  @ApiResponse({ status: 400, description: '请求参数错误或消息内容为空' })
  @ApiResponse({ status: 401, description: '用户未认证，请先登录' })
  @ApiResponse({ status: 403, description: '无权限访问该对话或消息配额不足' })
  @ApiResponse({ status: 404, description: '对话不存在' })
  @ApiResponse({ status: 429, description: '请求频率过高，请稍后再试' })
  @ApiResponse({ status: 500, description: '服务器内部错误或AI服务暂时不可用' })
  async sendMessage(@Request() req: any, @Body() createDto: CreateMessageDto) {
    if (createDto.streaming) {
      return { error: 'For streaming messages, use /coze/messages/stream endpoint' };
    }
    const userId = req.user?.sub || 1;

    // 验证对话类型是否为默认智能体
    await this.cozeService.validateConversationType(createDto.conversationId, userId, BotType.DEFAULT);

    const result = await this.cozeService.sendMessage(userId, createDto);
    return {
      code: 200,
      message: '消息发送成功',
      data: result
    };
  }

  @Post('interpretation/messages')
  @ApiOperation({
    summary: '发送消息到解读智能体',
    description: '向解读智能体发送消息并获取回复。'
  })
  @ApiBody({
    type: CreateMessageDto,
    description: '发送消息的请求参数'
  })
  async sendInterpretationMessage(@Request() req: any, @Body() createDto: CreateMessageDto) {
    if (createDto.streaming) {
      return { error: 'For streaming messages, use /coze/interpretation/messages/stream endpoint' };
    }
    const userId = req.user?.sub || 1;

    // 验证对话类型是否为解读智能体
    await this.cozeService.validateConversationType(createDto.conversationId, userId, BotType.INTERPRETATION);

    createDto.botType = BotType.INTERPRETATION;
    const result = await this.cozeService.sendMessage(userId, createDto);
    return {
      code: 200,
      message: '解读消息发送成功',
      data: result
    };
  }

  @Post('proto/messages')
  @ApiOperation({
    summary: '发送消息到原型智能体',
    description: '向原型智能体发送消息并获取回复。'
  })
  @ApiBody({
    type: CreateMessageDto,
    description: '发送消息的请求参数'
  })
  async sendProtoMessage(@Request() req: any, @Body() createDto: CreateMessageDto) {
    if (createDto.streaming) {
      return { error: 'For streaming messages, use /coze/proto/messages/stream endpoint' };
    }
    const userId = req.user?.sub || 1;

    // 验证对话类型是否为原型智能体
    await this.cozeService.validateConversationType(createDto.conversationId, userId, BotType.PROTO);

    createDto.botType = BotType.PROTO;
    const result = await this.cozeService.sendMessage(userId, createDto);
    return {
      code: 200,
      message: '原型消息发送成功',
      data: result
    };
  }

  @Post('messages/stream')
  @ApiOperation({
    summary: '发送AI消息（流式模式）',
    description: '向AI发送消息并以Server-Sent Events (SSE)方式实时流式接收回复。适用于长文本生成，用户可以实时看到AI的回复过程。'
  })
  @ApiConsumes('application/json')
  @ApiProduces('text/event-stream')
  @ApiBody({
    type: CreateMessageDto,
    description: '发送流式消息的请求参数',
    examples: {
      longFormContent: {
        summary: '长文本生成请求',
        description: '请求AI生成较长的内容，适合流式输出',
        value: {
          conversationId: '550e8400-e29b-41d4-a716-************',
          content: '请详细介绍一下深度学习的发展历程，包括关键里程碑、重要人物和技术突破',
          type: 'text',
          streaming: true,
          metadata: {
            expectedLength: 'long',
            topic: 'deep_learning_history'
          }
        }
      },
      codeGeneration: {
        summary: '代码生成请求',
        description: '请求AI生成较复杂的代码，实时查看生成过程',
        value: {
          conversationId: '550e8400-e29b-41d4-a716-************',
          content: '请帮我写一个完整的React组件，实现一个带搜索、分页和排序功能的用户列表。要求使用TypeScript和React Hooks。',
          type: 'text',
          streaming: true,
          metadata: {
            language: 'typescript',
            framework: 'react',
            complexity: 'high',
            features: ['search', 'pagination', 'sorting']
          }
        }
      },
      tutorialExplanation: {
        summary: '教程式解释',
        description: '请求详细的分步骤解释，适合流式展示',
        value: {
          conversationId: '550e8400-e29b-41d4-a716-************',
          content: '请一步一步地教我如何从零开始构建一个RESTful API，使用Node.js和Express框架',
          type: 'text',
          streaming: true,
          metadata: {
            instructionType: 'step_by_step',
            technology: 'nodejs_express',
            audience: 'beginner'
          }
        }
      },
      documentAnalysis: {
        summary: '文档分析请求',
        description: '分析复杂文档或代码，逐步输出分析结果',
        value: {
          conversationId: '550e8400-e29b-41d4-a716-************',
          content: '请分析这个复杂的SQL查询性能问题，并提供优化建议：\nSELECT u.*, p.*, c.count FROM users u LEFT JOIN profiles p ON u.id = p.user_id LEFT JOIN (SELECT user_id, COUNT(*) as count FROM comments GROUP BY user_id) c ON u.id = c.user_id WHERE u.created_at > \'2023-01-01\' ORDER BY u.last_login_at DESC',
          type: 'text',
          streaming: true,
          metadata: {
            analysisType: 'sql_performance',
            database: 'postgresql',
            requestType: 'optimization'
          }
        }
      }
    }
  })
  @ApiResponse({
    status: 200,
    description: '流式消息发送成功，返回SSE数据流',
    headers: {
      'Content-Type': { description: 'text/event-stream' },
      'Cache-Control': { description: 'no-cache' },
      'Connection': { description: 'keep-alive' }
    },
    schema: {
      type: 'string',
      description: 'Server-Sent Events格式的数据流，每个事件都是JSON格式',
      examples: {
        streamEvents: {
          summary: '流式事件示例',
          description: '展示完整的流式响应过程中的各种事件类型',
          value: `data: {"event":"start","data":{"messageId":"123e4567-e89b-12d3-a456-426614174003","conversationId":"550e8400-e29b-41d4-a716-************"}}

data: {"event":"chunk","data":"深度学习"}

data: {"event":"chunk","data":"是人工智能"}

data: {"event":"chunk","data":"的一个重要分支"}

data: {"event":"chunk","data":"，它通过算法"}

data: {"event":"chunk","data":"让计算机从数据中学习"}

data: {"event":"done","data":{"messageId":"123e4567-e89b-12d3-a456-426614174003","status":"completed","usage":{"promptTokens":32,"completionTokens":245,"totalTokens":277},"completedAt":"2024-01-15T10:30:15Z"}}

`
        },
        errorEvent: {
          summary: '错误事件示例',
          description: '当流式处理过程中发生错误时的响应格式',
          value: `data: {"event":"start","data":{"messageId":"123e4567-e89b-12d3-a456-426614174004","conversationId":"550e8400-e29b-41d4-a716-************"}}

data: {"event":"chunk","data":"正在处理您的请求"}

data: {"event":"error","data":{"error":"AI服务暂时不可用","code":"AI_SERVICE_UNAVAILABLE","messageId":"123e4567-e89b-12d3-a456-426614174004"}}

`
        }
      }
    }
  })
  @ApiResponse({ status: 400, description: '请求参数错误或消息内容为空' })
  @ApiResponse({ status: 401, description: '用户未认证，请先登录' })
  @ApiResponse({ status: 403, description: '无权限访问该对话或消息配额不足' })
  @ApiResponse({ status: 404, description: '对话不存在' })
  @ApiResponse({ status: 429, description: '请求频率过高，请稍后再试' })
  @ApiResponse({ status: 500, description: '服务器内部错误或AI服务暂时不可用' })
  async streamMessage(@Request() req: any, @Body() createDto: CreateMessageDto, @Res() response: Response) {
    response.setHeader('Content-Type', 'text/event-stream');
    response.setHeader('Cache-Control', 'no-cache');
    response.setHeader('Connection', 'keep-alive');
    response.setHeader('Access-Control-Allow-Origin', '*');
    response.setHeader('Access-Control-Allow-Headers', 'Content-Type');

    try {
      const userId = req.user?.sub || 1;

      // 验证对话类型是否为默认智能体
      await this.cozeService.validateConversationType(createDto.conversationId, userId, BotType.DEFAULT);

      const messageStream = this.cozeService.streamMessage(userId, createDto);

      messageStream.subscribe({
        next: (data) => {
          response.write(`data: ${JSON.stringify(data)}\n\n`);
          if (data.event === 'done') {
            response.end();
          }
        },
        error: (error) => {
          response.write(`data: ${JSON.stringify({ event: 'error', error: error.message })}\n\n`);
          response.end();
        },
        complete: () => {
          response.end();
        }
      });
    } catch (error) {
      response.write(`data: ${JSON.stringify({ event: 'error', error: error.message })}\n\n`);
      response.end();
    }
  }

  @Post('interpretation/messages/stream')
  @ApiOperation({
    summary: '发送流式消息到解读智能体',
    description: '向解读智能体发送消息并以流式方式接收回复。'
  })
  @ApiConsumes('application/json')
  @ApiProduces('text/event-stream')
  @ApiBody({
    type: CreateMessageDto,
    description: '发送流式消息的请求参数'
  })
  async streamInterpretationMessage(@Request() req: any, @Body() createDto: CreateMessageDto, @Res() response: Response) {
    response.setHeader('Content-Type', 'text/event-stream');
    response.setHeader('Cache-Control', 'no-cache');
    response.setHeader('Connection', 'keep-alive');
    response.setHeader('Access-Control-Allow-Origin', '*');
    response.setHeader('Access-Control-Allow-Headers', 'Content-Type');

    try {
      const userId = req.user?.sub || 1;

      // 验证对话类型是否为解读智能体
      await this.cozeService.validateConversationType(createDto.conversationId, userId, BotType.INTERPRETATION);

      createDto.botType = BotType.INTERPRETATION;
      const messageStream = this.cozeService.streamMessage(userId, createDto);

      messageStream.subscribe({
        next: (data) => {
          response.write(`data: ${JSON.stringify(data)}\n\n`);
          if (data.event === 'done') {
            response.end();
          }
        },
        error: (error) => {
          response.write(`data: ${JSON.stringify({ event: 'error', error: error.message })}\n\n`);
          response.end();
        },
        complete: () => {
          response.end();
        }
      });
    } catch (error) {
      response.write(`data: ${JSON.stringify({ event: 'error', error: error.message })}\n\n`);
      response.end();
    }
  }

  @Post('proto/messages/stream')
  @ApiOperation({
    summary: '发送流式消息到原型智能体',
    description: '向原型智能体发送消息并以流式方式接收回复。'
  })
  @ApiConsumes('application/json')
  @ApiProduces('text/event-stream')
  @ApiBody({
    type: CreateMessageDto,
    description: '发送流式消息的请求参数'
  })
  async streamProtoMessage(@Request() req: any, @Body() createDto: CreateMessageDto, @Res() response: Response) {
    response.setHeader('Content-Type', 'text/event-stream');
    response.setHeader('Cache-Control', 'no-cache');
    response.setHeader('Connection', 'keep-alive');
    response.setHeader('Access-Control-Allow-Origin', '*');
    response.setHeader('Access-Control-Allow-Headers', 'Content-Type');

    try {
      const userId = req.user?.sub || 1;

      // 验证对话类型是否为原型智能体
      await this.cozeService.validateConversationType(createDto.conversationId, userId, BotType.PROTO);

      createDto.botType = BotType.PROTO;
      const messageStream = this.cozeService.streamMessage(userId, createDto);

      messageStream.subscribe({
        next: (data) => {
          response.write(`data: ${JSON.stringify(data)}\n\n`);
          if (data.event === 'done') {
            response.end();
          }
        },
        error: (error) => {
          response.write(`data: ${JSON.stringify({ event: 'error', error: error.message })}\n\n`);
          response.end();
        },
        complete: () => {
          response.end();
        }
      });
    } catch (error) {
      response.write(`data: ${JSON.stringify({ event: 'error', error: error.message })}\n\n`);
      response.end();
    }
  }

  @Get('conversations/:id/messages')
  @ApiOperation({
    summary: '获取对话的消息历史（默认智能体）',
    description: '分页获取指定默认智能体对话中的所有消息记录，按时间倒序排列。包括用户消息和AI回复。'
  })
  @ApiParam({
    name: 'id',
    type: 'string',
    format: 'uuid',
    description: '对话的唯一标识符',
    example: '550e8400-e29b-41d4-a716-************'
  })
  @ApiQuery({
    name: 'page',
    required: false,
    type: Number,
    description: '页码，从1开始',
    example: 1
  })
  @ApiQuery({
    name: 'pageSize',
    required: false,
    type: Number,
    description: '每页显示数量，默认50条，最大200条',
    example: 50
  })
  @ApiResponse({
    status: 200,
    description: '消息列表获取成功',
    schema: {
      type: 'object',
      properties: {
        code: { type: 'number', example: 200 },
        message: { type: 'string', example: '消息列表获取成功' },
        data: {
          type: 'array',
          items: {
            type: 'object',
            properties: {
              id: { type: 'string', format: 'uuid', description: '消息唯一标识符' },
              conversationId: { type: 'string', format: 'uuid', description: '所属对话ID' },
              content: { type: 'string', description: '消息内容' },
              role: { type: 'string', enum: ['user', 'assistant', 'system'], description: '消息角色' },
              type: { type: 'string', enum: ['text', 'image', 'file', 'object_string', 'answer', 'function_call', 'tool_output'], description: '消息类型' },
              status: { type: 'string', enum: ['pending', 'completed', 'failed'], description: '消息状态' },
              metadata: { type: 'object', description: '消息元数据' },
              usage: {
                type: 'object',
                properties: {
                  promptTokens: { type: 'number', description: '输入Token数量' },
                  completionTokens: { type: 'number', description: '输出Token数量' },
                  totalTokens: { type: 'number', description: '总Token数量' }
                },
                description: 'Token使用量统计（仅AI消息有此字段）'
              },
              createdAt: { type: 'string', format: 'date-time', description: '创建时间' },
              completedAt: { type: 'string', format: 'date-time', description: '完成时间（仅已完成消息有此字段）' }
            }
          }
        },
        pagination: {
          type: 'object',
          properties: {
            total: { type: 'number', description: '总消息数' },
            page: { type: 'number', description: '当前页码' },
            pageSize: { type: 'number', description: '每页大小' },
            totalPages: { type: 'number', description: '总页数' },
            hasNext: { type: 'boolean', description: '是否有下一页' },
            hasPrev: { type: 'boolean', description: '是否有上一页' }
          }
        },
        timestamp: { type: 'string', description: '响应时间戳' }
      }
    }
  })
  @ApiResponse({ status: 400, description: '对话ID格式错误或分页参数错误' })
  @ApiResponse({ status: 401, description: '用户未认证，请先登录' })
  @ApiResponse({ status: 403, description: '无权限访问该对话的消息' })
  @ApiResponse({ status: 404, description: '对话不存在' })
  @ApiResponse({ status: 500, description: '服务器内部错误' })
  async getConversationMessages(
    @Request() req: any,
    @Param('id', ParseUUIDPipe) conversationId: string,
    @Query('page', new ParseIntPipe({ optional: true })) page: number = 1,
    @Query('pageSize', new ParseIntPipe({ optional: true })) pageSize: number = 50,
  ) {
    const userId = req.user?.sub || 1;

    // 验证对话类型是否为默认智能体
    await this.cozeService.validateConversationType(conversationId, userId, BotType.DEFAULT);

    const result = await this.cozeService.getConversationMessages(conversationId, userId, page, pageSize);
    return {
      code: 200,
      message: '消息列表获取成功',
      data: result.messages,
      pagination: {
        total: result.total,
        page: result.page,
        pageSize: result.pageSize,
        totalPages: Math.ceil(result.total / result.pageSize),
        hasNext: result.page < Math.ceil(result.total / result.pageSize),
        hasPrev: result.page > 1
      },
      timestamp: new Date().toLocaleString('zh-CN', { timeZone: 'Asia/Shanghai' })
    };
  }

  @Get('proto/conversations/:id/messages')
  @ApiOperation({
    summary: '获取原型智能体对话的消息历史',
    description: '分页获取指定原型智能体对话中的所有消息记录，按时间倒序排列。包括用户消息和AI回复。'
  })
  @ApiParam({
    name: 'id',
    type: 'string',
    format: 'uuid',
    description: '对话的唯一标识符',
    example: '550e8400-e29b-41d4-a716-************'
  })
  @ApiQuery({
    name: 'page',
    required: false,
    type: Number,
    description: '页码，从1开始',
    example: 1
  })
  @ApiQuery({
    name: 'pageSize',
    required: false,
    type: Number,
    description: '每页显示数量，默认50条，最大200条',
    example: 50
  })
  @ApiResponse({
    status: 200,
    description: '原型智能体消息列表获取成功',
    schema: {
      type: 'object',
      properties: {
        code: { type: 'number', example: 200 },
        message: { type: 'string', example: '原型智能体消息列表获取成功' },
        data: {
          type: 'array',
          items: {
            type: 'object',
            properties: {
              id: { type: 'string', format: 'uuid', description: '消息唯一标识符' },
              conversationId: { type: 'string', format: 'uuid', description: '所属对话ID' },
              content: { type: 'string', description: '消息内容' },
              role: { type: 'string', enum: ['user', 'assistant', 'system'], description: '消息角色' },
              type: { type: 'string', enum: ['text', 'image', 'file', 'object_string', 'answer', 'function_call', 'tool_output'], description: '消息类型' },
              status: { type: 'string', enum: ['pending', 'completed', 'failed'], description: '消息状态' },
              metadata: { type: 'object', description: '消息元数据' },
              usage: {
                type: 'object',
                properties: {
                  promptTokens: { type: 'number', description: '输入Token数量' },
                  completionTokens: { type: 'number', description: '输出Token数量' },
                  totalTokens: { type: 'number', description: '总Token数量' }
                },
                description: 'Token使用量统计（仅AI消息有此字段）'
              },
              createdAt: { type: 'string', format: 'date-time', description: '创建时间' },
              completedAt: { type: 'string', format: 'date-time', description: '完成时间（仅已完成消息有此字段）' }
            }
          }
        },
        pagination: {
          type: 'object',
          properties: {
            total: { type: 'number', description: '总消息数' },
            page: { type: 'number', description: '当前页码' },
            pageSize: { type: 'number', description: '每页大小' },
            totalPages: { type: 'number', description: '总页数' },
            hasNext: { type: 'boolean', description: '是否有下一页' },
            hasPrev: { type: 'boolean', description: '是否有上一页' }
          }
        },
        timestamp: { type: 'string', description: '响应时间戳' }
      }
    }
  })
  @ApiResponse({ status: 400, description: '对话ID格式错误或分页参数错误' })
  @ApiResponse({ status: 401, description: '用户未认证，请先登录' })
  @ApiResponse({ status: 403, description: '无权限访问该对话的消息' })
  @ApiResponse({ status: 404, description: '对话不存在' })
  @ApiResponse({ status: 500, description: '服务器内部错误' })
  async getProtoConversationMessages(
    @Request() req: any,
    @Param('id', ParseUUIDPipe) conversationId: string,
    @Query('page', new ParseIntPipe({ optional: true })) page: number = 1,
    @Query('pageSize', new ParseIntPipe({ optional: true })) pageSize: number = 50,
  ) {
    const userId = req.user?.sub || 1;

    // 验证对话类型是否为原型智能体
    await this.cozeService.validateConversationType(conversationId, userId, BotType.PROTO);

    const result = await this.cozeService.getConversationMessages(conversationId, userId, page, pageSize);
    return {
      code: 200,
      message: '原型智能体消息列表获取成功',
      data: result.messages,
      pagination: {
        total: result.total,
        page: result.page,
        pageSize: result.pageSize,
        totalPages: Math.ceil(result.total / result.pageSize),
        hasNext: result.page < Math.ceil(result.total / result.pageSize),
        hasPrev: result.page > 1
      },
      timestamp: new Date().toLocaleString('zh-CN', { timeZone: 'Asia/Shanghai' })
    };
  }

  @Get('interpretation/conversations/:id/messages')
  @ApiOperation({
    summary: '获取解读智能体对话的消息历史',
    description: '分页获取指定解读智能体对话中的所有消息记录，按时间倒序排列。包括用户消息和AI回复。'
  })
  @ApiParam({
    name: 'id',
    type: 'string',
    format: 'uuid',
    description: '对话的唯一标识符',
    example: '550e8400-e29b-41d4-a716-************'
  })
  @ApiQuery({
    name: 'page',
    required: false,
    type: Number,
    description: '页码，从1开始',
    example: 1
  })
  @ApiQuery({
    name: 'pageSize',
    required: false,
    type: Number,
    description: '每页显示数量，默认50条，最大200条',
    example: 50
  })
  @ApiResponse({
    status: 200,
    description: '解读智能体消息列表获取成功',
    schema: {
      type: 'object',
      properties: {
        code: { type: 'number', example: 200 },
        message: { type: 'string', example: '解读智能体消息列表获取成功' },
        data: {
          type: 'array',
          items: {
            type: 'object',
            properties: {
              id: { type: 'string', format: 'uuid', description: '消息唯一标识符' },
              conversationId: { type: 'string', format: 'uuid', description: '所属对话ID' },
              content: { type: 'string', description: '消息内容' },
              role: { type: 'string', enum: ['user', 'assistant', 'system'], description: '消息角色' },
              type: { type: 'string', enum: ['text', 'image', 'file', 'object_string', 'answer', 'function_call', 'tool_output'], description: '消息类型' },
              status: { type: 'string', enum: ['pending', 'completed', 'failed'], description: '消息状态' },
              metadata: { type: 'object', description: '消息元数据' },
              usage: {
                type: 'object',
                properties: {
                  promptTokens: { type: 'number', description: '输入Token数量' },
                  completionTokens: { type: 'number', description: '输出Token数量' },
                  totalTokens: { type: 'number', description: '总Token数量' }
                },
                description: 'Token使用量统计（仅AI消息有此字段）'
              },
              createdAt: { type: 'string', format: 'date-time', description: '创建时间' },
              completedAt: { type: 'string', format: 'date-time', description: '完成时间（仅已完成消息有此字段）' }
            }
          }
        },
        pagination: {
          type: 'object',
          properties: {
            total: { type: 'number', description: '总消息数' },
            page: { type: 'number', description: '当前页码' },
            pageSize: { type: 'number', description: '每页大小' },
            totalPages: { type: 'number', description: '总页数' },
            hasNext: { type: 'boolean', description: '是否有下一页' },
            hasPrev: { type: 'boolean', description: '是否有上一页' }
          }
        },
        timestamp: { type: 'string', description: '响应时间戳' }
      }
    }
  })
  @ApiResponse({ status: 400, description: '对话ID格式错误或分页参数错误' })
  @ApiResponse({ status: 401, description: '用户未认证，请先登录' })
  @ApiResponse({ status: 403, description: '无权限访问该对话的消息' })
  @ApiResponse({ status: 404, description: '对话不存在' })
  @ApiResponse({ status: 500, description: '服务器内部错误' })
  async getInterpretationConversationMessages(
    @Request() req: any,
    @Param('id', ParseUUIDPipe) conversationId: string,
    @Query('page', new ParseIntPipe({ optional: true })) page: number = 1,
    @Query('pageSize', new ParseIntPipe({ optional: true })) pageSize: number = 50,
  ) {
    const userId = req.user?.sub || 1;

    // 验证对话类型是否为解读智能体
    await this.cozeService.validateConversationType(conversationId, userId, BotType.INTERPRETATION);

    const result = await this.cozeService.getConversationMessages(conversationId, userId, page, pageSize);
    return {
      code: 200,
      message: '解读智能体消息列表获取成功',
      data: result.messages,
      pagination: {
        total: result.total,
        page: result.page,
        pageSize: result.pageSize,
        totalPages: Math.ceil(result.total / result.pageSize),
        hasNext: result.page < Math.ceil(result.total / result.pageSize),
        hasPrev: result.page > 1
      },
      timestamp: new Date().toLocaleString('zh-CN', { timeZone: 'Asia/Shanghai' })
    };
  }

  @Delete('conversations/:conversationId/chats/:chatId')
  @ApiOperation({
    summary: '取消正在进行的AI对话',
    description: '取消当前正在进行的AI对话请求。用于停止长时间运行的AI生成任务，特别是流式对话。'
  })
  @ApiParam({
    name: 'conversationId',
    type: 'string',
    format: 'uuid',
    description: '对话的唯一标识符',
    example: '550e8400-e29b-41d4-a716-************'
  })
  @ApiParam({
    name: 'chatId',
    type: 'string',
    description: '聊天会话的标识符，用于标识特定的AI对话请求',
    example: 'chat_1234567890'
  })
  @ApiResponse({
    status: 200,
    description: '聊天取消成功',
    schema: {
      type: 'object',
      properties: {
        success: { type: 'boolean', description: '取消操作是否成功' }
      }
    }
  })
  @ApiResponse({ status: 400, description: '对话ID或聊天ID格式错误' })
  @ApiResponse({ status: 401, description: '用户未认证，请先登录' })
  @ApiResponse({ status: 403, description: '无权限取消该聊天' })
  @ApiResponse({ status: 404, description: '对话或聊天不存在' })
  @ApiResponse({ status: 410, description: '聊天已完成，无法取消' })
  @ApiResponse({ status: 500, description: '服务器内部错误' })
  async cancelChat(
    @Request() req: any,
    @Param('conversationId', ParseUUIDPipe) conversationId: string,
    @Param('chatId') chatId: string,
  ) {
    // 临时处理：如果没有用户认证，使用测试用户ID
    const userId = req.user?.sub || 1;
    const result = await this.cozeService.cancelChat(conversationId, chatId, userId);
    return { success: result };
  }
}