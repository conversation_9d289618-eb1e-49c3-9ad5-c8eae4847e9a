import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { PersonalityResult } from '../modules/question/entities/personality-result.entity';

@Injectable()
export class PersonalityResultRepository {
  constructor(
    @InjectRepository(PersonalityResult)
    private personalityResultRepository: Repository<PersonalityResult>
  ) { }

  async findByTypeCode(typeCode: string): Promise<PersonalityResult | null> {
    return this.personalityResultRepository.findOne({
      where: { typeCode }
    });
  }

  async findAll(): Promise<PersonalityResult[]> {
    return this.personalityResultRepository.find();
  }

  async create(data: Partial<PersonalityResult>): Promise<PersonalityResult> {
    const personalityResult = this.personalityResultRepository.create(data);
    return this.personalityResultRepository.save(personalityResult);
  }

  async update(id: number, data: Partial<PersonalityResult>): Promise<void> {
    await this.personalityResultRepository.update(id, data);
  }

  async delete(id: number): Promise<void> {
    await this.personalityResultRepository.delete(id);
  }

  async isTypeCodeExists(typeCode: string): Promise<boolean> {
    const count = await this.personalityResultRepository.count({
      where: { typeCode }
    });
    return count > 0;
  }
} 