import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { QueryTemplate } from './entities/query-template.entity';

@Injectable()
export class QueryTemplateService {
  constructor(
    @InjectRepository(QueryTemplate)
    private queryTemplateRepository: Repository<QueryTemplate>,
  ) {}

  async create(templateData: Partial<QueryTemplate>): Promise<QueryTemplate> {
    const template = this.queryTemplateRepository.create(templateData);
    return await this.queryTemplateRepository.save(template);
  }

  async findAll(): Promise<QueryTemplate[]> {
    return await this.queryTemplateRepository.find();
  }

  async findOne(id: number): Promise<QueryTemplate | null> {
    return await this.queryTemplateRepository.findOne({ where: { id } });
  }

  async update(id: number, templateData: Partial<QueryTemplate>): Promise<QueryTemplate | null> {
    await this.queryTemplateRepository.update(id, templateData);
    return await this.findOne(id);
  }

  async remove(id: number): Promise<void> {
    await this.queryTemplateRepository.delete(id);
  }
} 