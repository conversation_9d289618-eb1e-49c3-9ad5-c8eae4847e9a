import { IsE<PERSON>, <PERSON>Optional, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Min, IsNotEmpty } from 'class-validator';
import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { Transform } from 'class-transformer';

export enum PaymentType {
  TEAM_PERMISSION = 'team_permission',  // 开通团队权限
  ASSESSMENT_CREDITS = 'assessment_credits',  // 购买评估次数
  AI_INTERPRETATION_CREDITS = 'ai_interpretation_credits',  // 购买AI解读次数
}

export enum PaymentMethod {
  WECHAT_PAY = 'wechat_pay',  // 微信支付
  ALIPAY = 'alipay',  // 支付宝
}

export class CreatePaymentDto {
  @ApiProperty({ 
    enum: PaymentType, 
    description: '支付类型',
    example: PaymentType.TEAM_PERMISSION 
  })
  @IsEnum(PaymentType, { message: '支付类型格式不正确' })
  type: PaymentType;

  @ApiProperty({ 
    enum: PaymentMethod, 
    description: '支付方式',
    example: PaymentMethod.WECHAT_PAY 
  })
  @IsEnum(PaymentMethod, { message: '支付方式格式不正确' })
  method: PaymentMethod;

  @ApiProperty({ 
    description: '支付金额（分）',
    example: 9900,
    minimum: 1
  })
  @IsNumber({}, { message: '支付金额必须是数字' })
  @Min(1, { message: '支付金额必须大于0' })
  @Transform(({ value }) => parseInt(value))
  amount: number;

  @ApiPropertyOptional({ 
    description: '购买数量（购买次数时使用）',
    example: 10,
    minimum: 1
  })
  @IsOptional()
  @IsNumber({}, { message: '购买数量必须是数字' })
  @Min(1, { message: '购买数量必须大于0' })
  @Transform(({ value }) => parseInt(value))
  quantity?: number;

  @ApiPropertyOptional({ 
    description: '支付描述',
    example: '开通团队权限'
  })
  @IsOptional()
  @IsString()
  description?: string;

  @ApiPropertyOptional({ 
    description: '客户端IP地址',
    example: '127.0.0.1'
  })
  @IsOptional()
  @IsString()
  clientIp?: string;
} 