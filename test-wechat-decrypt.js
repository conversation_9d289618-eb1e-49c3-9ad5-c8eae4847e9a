const axios = require('axios');

// 测试微信用户信息解密功能
async function testWechatDecrypt() {
  try {
    const testData = {
      encryptedData: "2Jguq6v771RzmIg824V8mAD+CYBjIoABP9geaQF8qGeHHzzZlyWsIDl6L8BQGtmISAD2JbdfPw/TUVBPse1XnhWQVA+8FvCnoVfjAex58RPOtf7PG0699gHYCaxoS5sXN7xBz75fEpmH2YWFc9Xo+31p8z6k2YBN1dLPStvqfcz1Z1w5cofvXAFbEubbjuRfiRl9JKzHnXOtQxiVICyPOg==",
      iv: "kmneH1ouMGszcWXgEqkfhA==",
      code: "4a0fbec7c332c9e20c04910b287c0183da74ced7bd089b3657a724363f201d67",
      dataType: "phoneNumber"
    };

    console.log('开始测试微信用户信息解密...');
    console.log('请求数据:', JSON.stringify(testData, null, 2));

    const response = await axios.post('http://localhost:3000/users/decrypt-user-info', testData, {
      headers: {
        'Content-Type': 'application/json'
      }
    });

    console.log('解密成功!');
    console.log('响应数据:', JSON.stringify(response.data, null, 2));
  } catch (error) {
    console.error('测试失败:', error.response?.data || error.message);
  }
}

// 运行测试
testWechatDecrypt(); 