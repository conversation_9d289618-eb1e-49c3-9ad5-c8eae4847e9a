import { Injectable, Logger, BadRequestException } from '@nestjs/common';
import { PaymentStatus } from '../entities/payment.entity';

/**
 * 支付状态管理服务
 * 负责支付状态转换的验证和管理
 */
@Injectable()
export class PaymentStatusService {
  private readonly logger = new Logger(PaymentStatusService.name);

  /**
   * 定义允许的状态转换规则
   * 键为当前状态，值为允许转换到的状态数组
   */
  private readonly statusTransitionRules: Record<PaymentStatus, PaymentStatus[]> = {
    [PaymentStatus.PENDING]: [
      PaymentStatus.PROCESSING,
      PaymentStatus.CANCELLED,
      PaymentStatus.FAILED,
    ],
    [PaymentStatus.PROCESSING]: [
      PaymentStatus.SUCCESS,
      PaymentStatus.FAILED,
      PaymentStatus.CANCELLED,
    ],
    [PaymentStatus.SUCCESS]: [
      PaymentStatus.REFUNDED,
    ],
    [PaymentStatus.FAILED]: [
      PaymentStatus.PENDING, // 允许重新发起支付
    ],
    [PaymentStatus.CANCELLED]: [
      PaymentStatus.PENDING, // 允许重新发起支付
    ],
    [PaymentStatus.REFUNDED]: [], // 退款状态不允许转换到其他状态
  };

  /**
   * 验证状态转换是否合法
   * @param currentStatus 当前状态
   * @param targetStatus 目标状态
   * @returns 是否允许转换
   */
  isValidStatusTransition(currentStatus: PaymentStatus, targetStatus: PaymentStatus): boolean {
    const allowedTransitions = this.statusTransitionRules[currentStatus];
    return allowedTransitions.includes(targetStatus);
  }

  /**
   * 验证状态转换并抛出异常（如果不合法）
   * @param currentStatus 当前状态
   * @param targetStatus 目标状态
   * @param paymentId 支付记录ID（用于日志）
   */
  validateStatusTransition(currentStatus: PaymentStatus, targetStatus: PaymentStatus, paymentId?: number): void {
    if (!this.isValidStatusTransition(currentStatus, targetStatus)) {
      const errorMessage = `不允许的状态转换: ${currentStatus} -> ${targetStatus}`;
      this.logger.error(`${errorMessage} (PaymentId: ${paymentId})`);
      throw new BadRequestException(errorMessage);
    }

    this.logger.log(`状态转换验证通过: ${currentStatus} -> ${targetStatus} (PaymentId: ${paymentId})`);
  }

  /**
   * 获取指定状态允许转换到的状态列表
   * @param currentStatus 当前状态
   * @returns 允许转换到的状态列表
   */
  getAllowedTransitions(currentStatus: PaymentStatus): PaymentStatus[] {
    return this.statusTransitionRules[currentStatus] || [];
  }

  /**
   * 检查状态是否为终态（不能再转换）
   * @param status 状态
   * @returns 是否为终态
   */
  isFinalStatus(status: PaymentStatus): boolean {
    const allowedTransitions = this.statusTransitionRules[status];
    return allowedTransitions.length === 0;
  }

  /**
   * 检查状态是否为成功状态
   * @param status 状态
   * @returns 是否为成功状态
   */
  isSuccessStatus(status: PaymentStatus): boolean {
    return status === PaymentStatus.SUCCESS;
  }

  /**
   * 检查状态是否为失败状态
   * @param status 状态
   * @returns 是否为失败状态
   */
  isFailureStatus(status: PaymentStatus): boolean {
    return status === PaymentStatus.FAILED;
  }

  /**
   * 检查状态是否为取消状态
   * @param status 状态
   * @returns 是否为取消状态
   */
  isCancelledStatus(status: PaymentStatus): boolean {
    return status === PaymentStatus.CANCELLED;
  }

  /**
   * 检查状态是否为进行中状态
   * @param status 状态
   * @returns 是否为进行中状态
   */
  isProcessingStatus(status: PaymentStatus): boolean {
    return status === PaymentStatus.PROCESSING || status === PaymentStatus.PENDING;
  }

  /**
   * 检查状态是否为已完成状态（成功或失败或取消）
   * @param status 状态
   * @returns 是否为已完成状态
   */
  isCompletedStatus(status: PaymentStatus): boolean {
    return [
      PaymentStatus.SUCCESS,
      PaymentStatus.FAILED,
      PaymentStatus.CANCELLED,
      PaymentStatus.REFUNDED,
    ].includes(status);
  }

  /**
   * 获取状态的中文描述
   * @param status 状态
   * @returns 中文描述
   */
  getStatusDescription(status: PaymentStatus): string {
    const descriptions: Record<PaymentStatus, string> = {
      [PaymentStatus.PENDING]: '待支付',
      [PaymentStatus.PROCESSING]: '支付中',
      [PaymentStatus.SUCCESS]: '支付成功',
      [PaymentStatus.FAILED]: '支付失败',
      [PaymentStatus.CANCELLED]: '已取消',
      [PaymentStatus.REFUNDED]: '已退款',
    };

    return descriptions[status] || '未知状态';
  }

  /**
   * 获取状态转换的建议操作
   * @param currentStatus 当前状态
   * @returns 建议操作列表
   */
  getSuggestedActions(currentStatus: PaymentStatus): string[] {
    const actions: Record<PaymentStatus, string[]> = {
      [PaymentStatus.PENDING]: [
        '发起支付',
        '取消支付',
      ],
      [PaymentStatus.PROCESSING]: [
        '等待支付结果',
        '查询支付状态',
        '取消支付',
      ],
      [PaymentStatus.SUCCESS]: [
        '申请退款',
        '查看支付详情',
      ],
      [PaymentStatus.FAILED]: [
        '重新发起支付',
        '查看失败原因',
      ],
      [PaymentStatus.CANCELLED]: [
        '重新发起支付',
      ],
      [PaymentStatus.REFUNDED]: [
        '查看退款详情',
      ],
    };

    return actions[currentStatus] || [];
  }

  /**
   * 根据第三方支付状态映射到系统状态
   * @param thirdPartyStatus 第三方支付状态
   * @param paymentMethod 支付方式
   * @returns 系统支付状态
   */
  mapThirdPartyStatus(thirdPartyStatus: string, paymentMethod: string): PaymentStatus {
    // 微信支付状态映射
    if (paymentMethod === 'wechat_pay') {
      const wechatStatusMap: Record<string, PaymentStatus> = {
        'SUCCESS': PaymentStatus.SUCCESS,
        'REFUND': PaymentStatus.REFUNDED,
        'NOTPAY': PaymentStatus.PENDING,
        'CLOSED': PaymentStatus.CANCELLED,
        'REVOKED': PaymentStatus.CANCELLED,
        'USERPAYING': PaymentStatus.PROCESSING,
        'PAYERROR': PaymentStatus.FAILED,
      };
      return wechatStatusMap[thirdPartyStatus] || PaymentStatus.FAILED;
    }

    // 支付宝状态映射
    if (paymentMethod === 'alipay') {
      const alipayStatusMap: Record<string, PaymentStatus> = {
        'TRADE_SUCCESS': PaymentStatus.SUCCESS,
        'TRADE_FINISHED': PaymentStatus.SUCCESS,
        'TRADE_CLOSED': PaymentStatus.CANCELLED,
        'WAIT_BUYER_PAY': PaymentStatus.PENDING,
      };
      return alipayStatusMap[thirdPartyStatus] || PaymentStatus.FAILED;
    }

    // 默认返回失败状态
    return PaymentStatus.FAILED;
  }

  /**
   * 获取状态转换历史记录格式
   * @param fromStatus 原状态
   * @param toStatus 新状态
   * @param reason 转换原因
   * @param operator 操作人
   * @returns 历史记录对象
   */
  createStatusTransitionRecord(
    fromStatus: PaymentStatus,
    toStatus: PaymentStatus,
    reason?: string,
    operator?: string
  ): {
    fromStatus: PaymentStatus;
    toStatus: PaymentStatus;
    reason?: string;
    operator?: string;
    timestamp: Date;
  } {
    return {
      fromStatus,
      toStatus,
      reason,
      operator,
      timestamp: new Date(),
    };
  }
}
