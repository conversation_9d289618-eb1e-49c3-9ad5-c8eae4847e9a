import { Injectable, BadRequestException, NotFoundException, ForbiddenException } from '@nestjs/common';
import { TeamInviteRepository } from '../../repositories/team-invite.repository';
import { UserRepository } from '../../repositories/user.repository';
import { TeamInvite, InviteStatus, InviteType } from './entities/team-invite.entity';
import { User, PermissionLevel } from '../users/entities/user.entity';
import { CreateInviteDto } from './dto/create-invite.dto';
import { AcceptInviteDto } from './dto/accept-invite.dto';
import { InviteResponseDto, InviteStatsDto, MyInviteInfoDto, MyInvitedUsersDto, InvitedUserDto } from './dto/invite-response.dto';
import { ConfigService } from '@nestjs/config';

@Injectable()
export class TeamInviteService {
  constructor(
    private teamInviteRepository: TeamInviteRepository,
    private userRepository: UserRepository,
    private configService: ConfigService
  ) {}

  /**
   * 获取用户的永久邀请码（不再创建临时邀请记录）
   */
  async getMyInviteCode(userId: number): Promise<{ inviteCode: string; inviteLink: string }> {
    const user = await this.userRepository.findById(userId);
    if (!user) {
      throw new NotFoundException('用户不存在');
    }

    if (!user.canInviteOthers) {
      throw new ForbiddenException('您没有邀请他人的权限');
    }

    // 如果用户还没有邀请码，生成一个
    if (!user.myInviteCode) {
      const inviteCode = await this.generateUniqueUserInviteCode();
      await this.userRepository.update(userId, { myInviteCode: inviteCode });
      user.myInviteCode = inviteCode;
    }

    const baseUrl = this.configService.get<string>('app.baseUrl', 'http://localhost:3000');
    const inviteLink = `${baseUrl}/invite/${user.myInviteCode}`;

    return {
      inviteCode: user.myInviteCode,
      inviteLink
    };
  }

  /**
   * 接受邀请（通过用户的永久邀请码）
   */
  async acceptInvite(inviteeId: number, acceptInviteDto: AcceptInviteDto): Promise<{
    success: boolean;
    message: string;
    teamPermissionGranted: boolean;
  }> {
    const { inviteCode } = acceptInviteDto;

    // 通过邀请码找到邀请人
    const inviter = await this.userRepository.findByInviteCode(inviteCode);
    if (!inviter) {
      throw new NotFoundException('邀请码不存在或无效');
    }

    // 检查被邀请人
    const invitee = await this.userRepository.findById(inviteeId);
    if (!invitee) {
      throw new NotFoundException('用户不存在');
    }

    // 检查是否已经有邀请人
    if (invitee.invitedById) {
      throw new BadRequestException('您已经接受过邀请，无法重复接受');
    }

    // 检查是否邀请自己
    if (inviter.id === inviteeId) {
      throw new BadRequestException('不能接受自己的邀请');
    }

    // 检查邀请人是否有邀请权限
    if (!inviter.canInviteOthers) {
      throw new ForbiddenException('邀请人没有邀请权限');
    }

    // 创建邀请记录（用于统计）
    const inviteRecord = await this.teamInviteRepository.create({
      inviteCode: inviteCode,
      inviterId: inviter.id,
      inviteeId: inviteeId,
      status: InviteStatus.ACCEPTED,
      type: InviteType.DIRECT,
      acceptedAt: new Date(),
      note: '通过永久邀请码接受邀请'
    });

    // 更新被邀请人信息
    await this.userRepository.update(inviteeId, {
      invitedById: inviter.id,
      permissionLevel: PermissionLevel.Team, // 接受邀请后自动升级为团队权限
      teamPermissionGrantedAt: new Date(),
      canInviteOthers: true,
      hasShareProfitRights: true
    });

    // 更新邀请人的邀请计数
    await this.userRepository.update(inviter.id, {
      inviteCount: inviter.inviteCount + 1
    });

    return {
      success: true,
      message: '邀请接受成功，您已获得团队权限',
      teamPermissionGranted: true
    };
  }

  /**
   * 获取我的邀请信息
   */
  async getMyInviteInfo(userId: number): Promise<MyInviteInfoDto> {
    const user = await this.userRepository.findById(userId);
    if (!user) {
      throw new NotFoundException('用户不存在');
    }

    // 确保用户有邀请码
    if (!user.myInviteCode) {
      const inviteCode = await this.generateUniqueUserInviteCode();
      await this.userRepository.update(userId, { myInviteCode: inviteCode });
      user.myInviteCode = inviteCode;
    }

    // 获取邀请统计（从实际的邀请记录中统计）
    const stats = await this.teamInviteRepository.getInviteStats(userId);
    const successRate = stats.totalInvites > 0 
      ? Math.round((stats.successfulInvites / stats.totalInvites) * 100)
      : 0;

    // 获取通过该用户邀请码邀请的用户列表
    const invitedUsers = await this.userRepository.findByInviterId(userId);
    const sentInvites = invitedUsers.map(invitedUser => ({
      id: invitedUser.id,
      inviteCode: user.myInviteCode,
      inviterId: userId,
      inviterName: user.name,
      inviteeId: invitedUser.id,
      inviteeName: invitedUser.name,
      status: InviteStatus.ACCEPTED,
      type: InviteType.DIRECT,
      acceptedAt: invitedUser.teamPermissionGrantedAt,
      createdAt: invitedUser.teamPermissionGrantedAt,
      updatedAt: invitedUser.teamPermissionGrantedAt
    }));

    return {
      myInviteCode: user.myInviteCode,
      inviteCount: user.inviteCount,
      canInviteOthers: user.canInviteOthers,
      stats: {
        totalInvites: user.inviteCount,
        successfulInvites: user.inviteCount, // 所有邀请都是成功的
        pendingInvites: 0, // 永久邀请码没有待处理状态
        expiredInvites: 0, // 永久邀请码不会过期
        successRate: user.inviteCount > 0 ? 100 : 0
      },
      sentInvites: sentInvites
    };
  }

  /**
   * 获取使用我邀请码的用户详细信息
   */
  async getMyInvitedUsers(userId: number): Promise<MyInvitedUsersDto> {
    const user = await this.userRepository.findById(userId);
    if (!user) {
      throw new NotFoundException('用户不存在');
    }

    // 确保用户有邀请码
    if (!user.myInviteCode) {
      const inviteCode = await this.generateUniqueUserInviteCode();
      await this.userRepository.update(userId, { myInviteCode: inviteCode });
      user.myInviteCode = inviteCode;
    }

    // 获取通过该用户邀请码邀请的用户详细信息
    const invitedUsers = await this.userRepository.findDetailedByInviterId(userId);
    
    // 统计信息
    const totalUsers = invitedUsers.length;
    const activeUsers = invitedUsers.filter(u => u.isActive).length;
    const teamUsers = invitedUsers.filter(u => u.permissionLevel === PermissionLevel.Team).length;
    const individualUsers = invitedUsers.filter(u => u.permissionLevel === PermissionLevel.Individual).length;

    // 转换为DTO格式
    const invitedUsersDto: InvitedUserDto[] = invitedUsers.map(invitedUser => ({
      id: invitedUser.id,
      name: invitedUser.name,
      email: invitedUser.email || null, 
      phoneNumber: invitedUser.phoneNumber || null,
      joinedAt: invitedUser.teamPermissionGrantedAt || invitedUser.createdAt,
      permissionLevel: invitedUser.permissionLevel,
      userType: invitedUser.userType,
      membershipType: invitedUser.membershipType
    }));

    return {
      myInviteCode: user.myInviteCode,
      totalCount: totalUsers,
      invitedUsers: invitedUsersDto,
      summary: {
        totalUsers,
        activeUsers,
        teamUsers,
        individualUsers
      }
    };
  }

  /**
   * 获取使用我邀请码的用户数量（简化版本）
   */
  async getMyInvitedUsersCount(userId: number): Promise<{
    myInviteCode: string;
    totalCount: number;
    activeCount: number;
    teamCount: number;
  }> {
    const user = await this.userRepository.findById(userId);
    if (!user) {
      throw new NotFoundException('用户不存在');
    }

    // 确保用户有邀请码
    if (!user.myInviteCode) {
      const inviteCode = await this.generateUniqueUserInviteCode();
      await this.userRepository.update(userId, { myInviteCode: inviteCode });
      user.myInviteCode = inviteCode;
    }

    // 获取统计信息
    const stats = await this.userRepository.getInviteStats(userId);

    return {
      myInviteCode: user.myInviteCode,
      totalCount: stats.totalInvited,
      activeCount: stats.activeInvited,
      teamCount: stats.teamInvited
    };
  }

  /**
   * 检查邀请码是否有效
   */
  async checkInviteCode(inviteCode: string): Promise<{
    valid: boolean;
    inviterName?: string;
    inviterId?: number;
  }> {
    const inviter = await this.userRepository.findByInviteCode(inviteCode);
    if (!inviter) {
      return { valid: false };
    }

    if (!inviter.canInviteOthers) {
      return { valid: false };
    }

    return {
      valid: true,
      inviterName: inviter.name,
      inviterId: inviter.id
    };
  }

  /**
   * 生成唯一的用户邀请码
   */
  private async generateUniqueUserInviteCode(): Promise<string> {
    const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789';
    let inviteCode: string = '';
    let exists = true;
    
    while (exists) {
      inviteCode = '';
      for (let i = 0; i < 8; i++) {
        inviteCode += chars.charAt(Math.floor(Math.random() * chars.length));
      }
      // 检查是否已经存在
      exists = await this.userRepository.isInviteCodeExists(inviteCode);
    }
    
    return inviteCode;
  }

  // 保留一些旧的方法以兼容现有代码，但标记为已弃用
  /**
   * @deprecated 不再使用，每个用户都有永久邀请码
   */
  async createInvite(inviterId: number, createInviteDto: CreateInviteDto): Promise<InviteResponseDto> {
    // 直接返回用户的永久邀请码信息
    const { inviteCode, inviteLink } = await this.getMyInviteCode(inviterId);
    const user = await this.userRepository.findById(inviterId);
    
    if (!user) {
      throw new NotFoundException('用户不存在');
    }
    
    return {
      id: 0, // 不再有具体的邀请记录ID
      inviteCode,
      inviterId,
      inviterName: user.name,
      inviteeId: undefined,
      inviteeName: undefined,
      status: InviteStatus.PENDING,
      type: InviteType.DIRECT,
      inviteLink,
      expiresAt: undefined, // 永久有效
      acceptedAt: undefined,
      note: '永久邀请码',
      createdAt: new Date(),
      updatedAt: new Date()
    };
  }

  /**
   * @deprecated 永久邀请码不需要取消
   */
  async cancelInvite(inviteId: number, userId: number): Promise<{ success: boolean; message: string }> {
    throw new BadRequestException('永久邀请码无法取消，如需停止邀请请联系管理员');
  }

  /**
   * @deprecated 永久邀请码不会过期
   */
  async cleanupExpiredInvites(): Promise<{ count: number }> {
    return { count: 0 };
  }

  /**
   * @deprecated 使用checkInviteCode代替
   */
  async getInviteDetail(inviteId: number, userId: number): Promise<InviteResponseDto> {
    throw new BadRequestException('该功能已废弃，请使用永久邀请码系统');
  }

  private transformToResponseDto(invite: TeamInvite, inviter?: User): InviteResponseDto {
    const dto = new InviteResponseDto();
    dto.id = invite.id;
    dto.inviteCode = invite.inviteCode;
    dto.inviterId = invite.inviterId;
    dto.inviterName = inviter?.name || invite.inviter?.name || '未知';
    dto.inviteeId = invite.inviteeId;
    dto.inviteeName = invite.invitee?.name;
    dto.status = invite.status;
    dto.type = invite.type;
    dto.inviteLink = invite.inviteLink;
    dto.expiresAt = invite.expiresAt;
    dto.acceptedAt = invite.acceptedAt;
    dto.note = invite.note;
    dto.createdAt = invite.createdAt;
    dto.updatedAt = invite.updatedAt;
    
    return dto;
  }
} 