import {
  IsEmail,
  IsNotEmpty,
  IsString,
  Min<PERSON>ength,
  Matches,
  IsOptional,
  IsEnum,
  IsDateString,
} from "class-validator";
import { ApiProperty } from "@nestjs/swagger";
import { UserType, Gender } from "../entities/user.entity";

export class CreateUserDto {
  @ApiProperty({ description: "手机号（可选）", required: false })
  @IsOptional()
  @IsString()
  phoneNumber?: string;

  @ApiProperty({ description: "用户名", example: "张三" })
  @IsString()
  name: string;

  @ApiProperty({ description: "邮箱（可选）", example: "<EMAIL>", required: false })
  @IsOptional()
  @IsEmail({}, { message: "邮箱格式不正确" })
  email?: string;

  @ApiProperty({ description: "密码", example: "Password123!" })
  @IsString()
  @MinLength(8, { message: "密码至少8个字符" })
  @IsNotEmpty({ message: "密码不能为空" })
  password: string;

  @ApiProperty({ description: "验证码（当提供手机号时必填）", required: false })
  @IsOptional()
  @IsString()
  @Matches(/^\d{6}$/, { message: "验证码格式不正确" })
  captcha?: string;

  @ApiProperty({ description: "真实姓名（可选）", required: false })
  @IsOptional()
  @IsString()
  fullName?: string;

  @ApiProperty({ description: "地址（可选）", required: false })
  @IsOptional()
  @IsString()
  address?: string;

  @ApiProperty({ 
    enum: Gender, 
    description: "性别（可选）", 
    required: false,
    example: Gender.Male 
  })
  @IsOptional()
  @IsEnum(Gender, { message: "性别格式不正确" })
  gender?: Gender;

  @ApiProperty({ 
    description: "出生日期（可选）", 
    type: String, 
    format: "date", 
    required: false,
    example: "1990-01-01"
  })
  @IsOptional()
  @IsDateString({}, { message: "出生日期格式不正确" })
  birthDate?: string;

  @ApiProperty({ 
    enum: UserType, 
    description: "用户类型", 
    default: UserType.Individual,
    example: UserType.Individual 
  })
  @IsOptional()
  @IsEnum(UserType, { message: "用户类型格式不正确" })
  userType?: UserType;

  @ApiProperty({ description: "头像链接（可选）", required: false })
  @IsOptional()
  @IsString()
  profilePicture?: string;

  @ApiProperty({ description: "背景图链接（可选）", required: false })
  @IsOptional()
  @IsString()
  profileBackgroundPicture?: string;
}
