import { Injectable, Logger } from '@nestjs/common';
import { PaymentRepository } from '../../../repositories/payment.repository';
import { Payment, PaymentStatus } from '../entities/payment.entity';
import { PaymentMethod } from '../dto/create-payment.dto';

/**
 * 支付错误处理和日志服务
 */
@Injectable()
export class PaymentErrorService {
  private readonly logger = new Logger(PaymentErrorService.name);

  constructor(private readonly paymentRepository: PaymentRepository) {}

  /**
   * 记录支付错误
   * @param payment 支付记录
   * @param error 错误信息
   * @param context 错误上下文
   */
  async logPaymentError(
    payment: Payment | null,
    error: Error | string,
    context: string
  ): Promise<void> {
    const errorMessage = typeof error === 'string' ? error : error.message;
    const errorStack = typeof error === 'string' ? undefined : error.stack;

    // 记录详细的错误日志
    this.logger.error(
      `支付错误 - ${context}`,
      {
        paymentId: payment?.id,
        orderNo: payment?.orderNo,
        userId: payment?.userId,
        amount: payment?.amount,
        method: payment?.method,
        status: payment?.status,
        error: errorMessage,
        stack: errorStack,
        timestamp: new Date().toISOString(),
      }
    );

    // 如果有支付记录，更新错误信息到数据库
    if (payment) {
      try {
        await this.updatePaymentWithError(payment, errorMessage, context);
      } catch (updateError) {
        this.logger.error(`更新支付错误信息失败: ${updateError.message}`, {
          paymentId: payment.id,
          originalError: errorMessage,
        });
      }
    }
  }

  /**
   * 记录支付成功日志
   * @param payment 支付记录
   * @param additionalInfo 额外信息
   */
  async logPaymentSuccess(payment: Payment, additionalInfo?: Record<string, any>): Promise<void> {
    this.logger.log(
      `支付成功`,
      {
        paymentId: payment.id,
        orderNo: payment.orderNo,
        userId: payment.userId,
        amount: payment.amount,
        method: payment.method,
        thirdPartyTransactionId: payment.thirdPartyTransactionId,
        paidAt: payment.paidAt,
        ...additionalInfo,
        timestamp: new Date().toISOString(),
      }
    );
  }

  /**
   * 记录支付状态变更日志
   * @param payment 支付记录
   * @param oldStatus 原状态
   * @param newStatus 新状态
   * @param reason 变更原因
   */
  async logStatusChange(
    payment: Payment,
    oldStatus: PaymentStatus,
    newStatus: PaymentStatus,
    reason?: string
  ): Promise<void> {
    this.logger.log(
      `支付状态变更: ${oldStatus} -> ${newStatus}`,
      {
        paymentId: payment.id,
        orderNo: payment.orderNo,
        userId: payment.userId,
        oldStatus,
        newStatus,
        reason,
        timestamp: new Date().toISOString(),
      }
    );
  }

  /**
   * 记录支付回调日志
   * @param callbackData 回调数据
   * @param payment 支付记录
   * @param result 处理结果
   */
  async logPaymentCallback(
    callbackData: any,
    payment: Payment | null,
    result: { success: boolean; message: string }
  ): Promise<void> {
    const logLevel = result.success ? 'log' : 'warn';
    
    this.logger[logLevel](
      `支付回调处理${result.success ? '成功' : '失败'}`,
      {
        paymentId: payment?.id,
        orderNo: payment?.orderNo,
        callbackType: this.getCallbackType(callbackData),
        result,
        timestamp: new Date().toISOString(),
      }
    );
  }

  /**
   * 记录支付重试日志
   * @param payment 支付记录
   * @param retryCount 重试次数
   * @param reason 重试原因
   */
  async logPaymentRetry(payment: Payment, retryCount: number, reason: string): Promise<void> {
    this.logger.warn(
      `支付重试 - 第${retryCount}次`,
      {
        paymentId: payment.id,
        orderNo: payment.orderNo,
        userId: payment.userId,
        retryCount,
        reason,
        timestamp: new Date().toISOString(),
      }
    );

    // 更新重试信息到数据库
    try {
      await this.paymentRepository.update(payment.id, {
        retryCount,
        lastRetryAt: new Date(),
        remark: `重试原因: ${reason}`,
      });
    } catch (error) {
      this.logger.error(`更新重试信息失败: ${error.message}`);
    }
  }

  /**
   * 记录支付超时日志
   * @param payment 支付记录
   */
  async logPaymentTimeout(payment: Payment): Promise<void> {
    this.logger.warn(
      `支付超时`,
      {
        paymentId: payment.id,
        orderNo: payment.orderNo,
        userId: payment.userId,
        expiresAt: payment.expiresAt,
        currentTime: new Date().toISOString(),
      }
    );

    // 更新支付状态为已取消
    try {
      await this.paymentRepository.updateStatus(payment.id, PaymentStatus.CANCELLED, {
        failureReason: '支付超时',
      });
    } catch (error) {
      this.logger.error(`更新超时支付状态失败: ${error.message}`);
    }
  }

  /**
   * 记录第三方支付接口调用日志
   * @param payment 支付记录
   * @param apiName 接口名称
   * @param request 请求参数
   * @param response 响应结果
   * @param duration 耗时（毫秒）
   */
  async logThirdPartyApiCall(
    payment: Payment,
    apiName: string,
    request: any,
    response: any,
    duration: number
  ): Promise<void> {
    this.logger.log(
      `第三方支付接口调用: ${apiName}`,
      {
        paymentId: payment.id,
        orderNo: payment.orderNo,
        apiName,
        duration: `${duration}ms`,
        requestSize: JSON.stringify(request).length,
        responseSize: JSON.stringify(response).length,
        timestamp: new Date().toISOString(),
      }
    );
  }

  /**
   * 获取错误分类
   * @param error 错误信息
   * @returns 错误分类
   */
  getErrorCategory(error: Error | string): string {
    const errorMessage = typeof error === 'string' ? error : error.message;
    
    if (errorMessage.includes('网络') || errorMessage.includes('timeout')) {
      return 'NETWORK_ERROR';
    }
    if (errorMessage.includes('余额不足') || errorMessage.includes('insufficient')) {
      return 'INSUFFICIENT_BALANCE';
    }
    if (errorMessage.includes('签名') || errorMessage.includes('signature')) {
      return 'SIGNATURE_ERROR';
    }
    if (errorMessage.includes('参数') || errorMessage.includes('parameter')) {
      return 'PARAMETER_ERROR';
    }
    if (errorMessage.includes('权限') || errorMessage.includes('permission')) {
      return 'PERMISSION_ERROR';
    }
    
    return 'UNKNOWN_ERROR';
  }

  /**
   * 生成错误报告
   * @param startDate 开始日期
   * @param endDate 结束日期
   * @returns 错误报告
   */
  async generateErrorReport(startDate: Date, endDate: Date): Promise<{
    totalErrors: number;
    errorsByCategory: Record<string, number>;
    errorsByMethod: Record<string, number>;
    topErrors: Array<{ message: string; count: number }>;
  }> {
    try {
      // 这里应该从数据库查询错误统计数据
      // 由于当前没有专门的错误日志表，这里返回模拟数据
      // 在实际项目中，建议创建专门的错误日志表来存储详细的错误信息
      
      const { payments } = await this.paymentRepository.findAll({
        status: PaymentStatus.FAILED,
      });

      const failedPayments = payments.filter(p => 
        p.createdAt >= startDate && p.createdAt <= endDate
      );

      const errorsByCategory: Record<string, number> = {};
      const errorsByMethod: Record<string, number> = {};
      const errorMessages: Record<string, number> = {};

      failedPayments.forEach(payment => {
        // 统计错误分类
        const category = this.getErrorCategory(payment.failureReason || 'UNKNOWN_ERROR');
        errorsByCategory[category] = (errorsByCategory[category] || 0) + 1;

        // 统计支付方式
        errorsByMethod[payment.method] = (errorsByMethod[payment.method] || 0) + 1;

        // 统计错误消息
        const message = payment.failureReason || '未知错误';
        errorMessages[message] = (errorMessages[message] || 0) + 1;
      });

      // 获取前10个最常见的错误
      const topErrors = Object.entries(errorMessages)
        .sort(([, a], [, b]) => b - a)
        .slice(0, 10)
        .map(([message, count]) => ({ message, count }));

      return {
        totalErrors: failedPayments.length,
        errorsByCategory,
        errorsByMethod,
        topErrors,
      };
    } catch (error) {
      this.logger.error(`生成错误报告失败: ${error.message}`);
      throw error;
    }
  }

  /**
   * 更新支付记录的错误信息
   * @param payment 支付记录
   * @param errorMessage 错误消息
   * @param context 错误上下文
   */
  private async updatePaymentWithError(
    payment: Payment,
    errorMessage: string,
    context: string
  ): Promise<void> {
    const updateData: Partial<Payment> = {
      failureReason: errorMessage,
      remark: `错误上下文: ${context}`,
    };

    // 如果当前状态允许，更新为失败状态
    if ([PaymentStatus.PENDING, PaymentStatus.PROCESSING].includes(payment.status)) {
      updateData.status = PaymentStatus.FAILED;
    }

    await this.paymentRepository.update(payment.id, updateData);
  }

  /**
   * 获取回调类型
   * @param callbackData 回调数据
   * @returns 回调类型
   */
  private getCallbackType(callbackData: any): string {
    if (callbackData?.event_type) {
      return callbackData.event_type;
    }
    if (callbackData?.trade_status) {
      return callbackData.trade_status;
    }
    return 'UNKNOWN';
  }
}
