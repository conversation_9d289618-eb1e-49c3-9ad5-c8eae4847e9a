import { Module } from '@nestjs/common';
import { ConfigModule } from '@nestjs/config';
import { CozeService } from './coze.service';
import { CozeController } from './coze.controller';
import { RepositoriesModule } from '../../repositories/repositories.module';
import { RedisModule } from '../redis/redis.module';
import { GuardsModule } from '../../guards/guards.module';
@Module({
  imports: [
    ConfigModule,
    RepositoriesModule,
    RedisModule,
    GuardsModule,
  ],
  controllers: [CozeController],
  providers: [CozeService],
  exports: [CozeService],
})
export class CozeModule { }