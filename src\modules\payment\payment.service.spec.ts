import { Test, TestingModule } from '@nestjs/testing';
import { ConfigService } from '@nestjs/config';
import { PaymentService } from './payment.service';
import { PaymentRepository } from '../../repositories/payment.repository';
import { Payment, PaymentStatus } from './entities/payment.entity';
import { PaymentType, PaymentMethod } from './dto/create-payment.dto';
import { CreatePaymentRequestDto } from './dto/create-payment-request.dto';

describe('PaymentService', () => {
  let service: PaymentService;
  let paymentRepository: jest.Mocked<PaymentRepository>;
  let configService: jest.Mocked<ConfigService>;

  const createMockPayment = (overrides: Partial<Payment> = {}): Payment => ({
    id: 1,
    orderNo: 'ORDER_20240115103000123456',
    userId: 123,
    type: PaymentType.TEAM_PERMISSION,
    method: PaymentMethod.WECHAT_PAY,
    amount: 9900,
    quantity: 1,
    status: PaymentStatus.PENDING,
    description: '开通团队权限',
    thirdPartyTransactionId: null as any,
    paidAt: null as any,
    refundedAt: null as any,
    refundAmount: null as any,
    failureReason: null as any,
    thirdPartyResponse: null as any,
    metadata: null as any,
    clientIp: '127.0.0.1',
    notifyUrl: null as any,
    returnUrl: null as any,
    expiresAt: new Date(Date.now() + 30 * 60 * 1000),
    paymentChannel: null as any,
    remark: null as any,
    retryCount: 0,
    lastRetryAt: null as any,
    createdAt: new Date(),
    updatedAt: new Date(),
    user: null as any,
    ...overrides,
  });

  const mockConfigService = {
    get: jest.fn(),
  };

  const mockPaymentRepository = {
    create: jest.fn(),
    findById: jest.fn(),
    findByOrderNo: jest.fn(),
    findByThirdPartyTransactionId: jest.fn(),
    findByUserId: jest.fn(),
    update: jest.fn(),
    updateStatus: jest.fn(),
    updateStatusByOrderNo: jest.fn(),
    getPaymentStats: jest.fn(),
    findAll: jest.fn(),
    delete: jest.fn(),
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        PaymentService,
        {
          provide: ConfigService,
          useValue: mockConfigService,
        },
        {
          provide: PaymentRepository,
          useValue: mockPaymentRepository,
        },
      ],
    }).compile();

    service = module.get<PaymentService>(PaymentService);
    paymentRepository = module.get(PaymentRepository);
    configService = module.get(ConfigService);

    // 设置默认的配置返回值
    configService.get.mockImplementation((key: string) => {
      const configs = {
        wechatPay: {
          appid: 'test_appid',
          mchid: 'test_mchid',
          notifyUrl: 'https://test.com/notify',
          apiV3Key: 'test_api_v3_key',
        },
      };
      return configs[key];
    });
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  describe('createPaymentOrder', () => {
    it('should create a payment order successfully', async () => {
      // Arrange
      const createPaymentDto: CreatePaymentRequestDto = {
        userId: 123,
        type: PaymentType.TEAM_PERMISSION,
        method: PaymentMethod.WECHAT_PAY,
        amount: 9900,
        quantity: 1,
        description: '开通团队权限',
        clientIp: '127.0.0.1',
      };

      const mockPayment = createMockPayment();
      paymentRepository.create.mockResolvedValue(mockPayment);

      // Mock the WeChat Pay SDK
      const mockWxPayResult = {
        appId: 'test_appid',
        timeStamp: '1642234567',
        nonceStr: 'test_nonce',
        package: 'prepay_id=test_prepay_id',
        signType: 'RSA',
        paySign: 'test_sign',
      };

      // Mock the private method by spying on the service
      jest.spyOn(service as any, 'createWechatPayment').mockResolvedValue(mockWxPayResult);

      // Act
      const result = await service.createPaymentOrder(createPaymentDto);

      // Assert
      expect(paymentRepository.create).toHaveBeenCalledWith(
        expect.objectContaining({
          userId: createPaymentDto.userId,
          type: createPaymentDto.type,
          method: createPaymentDto.method,
          amount: createPaymentDto.amount,
          status: PaymentStatus.PENDING,
        })
      );

      expect(result).toEqual({
        paymentId: mockPayment.id,
        orderNo: mockPayment.orderNo,
        paymentParams: mockWxPayResult,
        expiresAt: mockPayment.expiresAt,
        createdAt: mockPayment.createdAt,
      });
    });

    it('should throw error for unsupported payment method', async () => {
      // Arrange
      const createPaymentDto: CreatePaymentRequestDto = {
        userId: 123,
        type: PaymentType.TEAM_PERMISSION,
        method: 'UNSUPPORTED_METHOD' as PaymentMethod,
        amount: 9900,
      };

      const mockPayment = createMockPayment();
      paymentRepository.create.mockResolvedValue(mockPayment);

      // Act & Assert
      await expect(service.createPaymentOrder(createPaymentDto)).rejects.toThrow('不支持的支付方式');
    });
  });

  describe('getPaymentById', () => {
    it('should return payment when found', async () => {
      // Arrange
      const mockPayment = createMockPayment();
      paymentRepository.findById.mockResolvedValue(mockPayment);

      // Act
      const result = await service.getPaymentById(1);

      // Assert
      expect(paymentRepository.findById).toHaveBeenCalledWith(1);
      expect(result).toEqual(
        expect.objectContaining({
          id: mockPayment.id,
          orderNo: mockPayment.orderNo,
          userId: mockPayment.userId,
          status: mockPayment.status,
        })
      );
    });

    it('should throw NotFoundException when payment not found', async () => {
      // Arrange
      paymentRepository.findById.mockResolvedValue(null);

      // Act & Assert
      await expect(service.getPaymentById(999)).rejects.toThrow('支付记录不存在');
    });
  });

  describe('updatePaymentStatus', () => {
    it('should update payment status successfully', async () => {
      // Arrange
      const updateStatusDto = {
        status: PaymentStatus.SUCCESS,
        thirdPartyTransactionId: 'wx_transaction_123',
        paidAt: '2024-01-15T10:30:00Z',
      };

      const mockPayment = createMockPayment();
      paymentRepository.findById.mockResolvedValue(mockPayment);
      paymentRepository.updateStatus.mockResolvedValue(undefined);

      // Act
      await service.updatePaymentStatus(1, updateStatusDto);

      // Assert
      expect(paymentRepository.findById).toHaveBeenCalledWith(1);
      expect(paymentRepository.updateStatus).toHaveBeenCalledWith(
        1,
        PaymentStatus.SUCCESS,
        expect.objectContaining({
          thirdPartyTransactionId: 'wx_transaction_123',
          paidAt: new Date('2024-01-15T10:30:00Z'),
        })
      );
    });

    it('should throw NotFoundException when payment not found', async () => {
      // Arrange
      const updateStatusDto = {
        status: PaymentStatus.SUCCESS,
      };

      paymentRepository.findById.mockResolvedValue(null);

      // Act & Assert
      await expect(service.updatePaymentStatus(999, updateStatusDto)).rejects.toThrow('支付记录不存在');
    });
  });

  describe('getUserPaymentStats', () => {
    it('should return payment statistics', async () => {
      // Arrange
      const mockStats = {
        totalPayments: 10,
        successfulPayments: 8,
        failedPayments: 2,
        totalAmount: 99000,
        successfulAmount: 79200,
      };

      paymentRepository.getPaymentStats.mockResolvedValue(mockStats);

      // Act
      const result = await service.getUserPaymentStats(123);

      // Assert
      expect(paymentRepository.getPaymentStats).toHaveBeenCalledWith(123);
      expect(result).toEqual({
        ...mockStats,
        successRate: 80, // 8/10 * 100
      });
    });
  });

  describe('queryPayments', () => {
    it('should return paginated payment list', async () => {
      // Arrange
      const queryDto = {
        page: 1,
        limit: 20,
        status: PaymentStatus.SUCCESS,
      };

      const mockPayment = createMockPayment();
      const mockPayments = [mockPayment];
      const mockResult = {
        payments: mockPayments,
        total: 1,
      };

      paymentRepository.findAll.mockResolvedValue(mockResult);

      // Act
      const result = await service.queryPayments(queryDto);

      // Assert
      expect(paymentRepository.findAll).toHaveBeenCalledWith({
        status: PaymentStatus.SUCCESS,
        limit: 20,
        offset: 0,
      });

      expect(result).toEqual({
        payments: expect.arrayContaining([
          expect.objectContaining({
            id: mockPayment.id,
            orderNo: mockPayment.orderNo,
          }),
        ]),
        total: 1,
        page: 1,
        limit: 20,
      });
    });
  });
});
