import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { User } from '../modules/users/entities/user.entity';

@Injectable()
export class UserRepository {
  constructor(
    @InjectRepository(User)
    private userRepository: Repository<User>
  ) { }

  async findById(id: number): Promise<User | null> {
    // 验证ID参数
    if (!id || isNaN(id) || id <= 0) {
      return null;
    }

    const result = await this.userRepository.query(
      `SELECT id, name, email, phoneNumber, role, userType, fullName, address, 
              gender, birthDate, isActive, profilePicture, profileBackgroundPicture,
              membershipType, membershipExpireDate, hasTeamPermission, 
              freeAssessmentCount, freeAIInterpretationCount, 
              totalAssessmentCount, totalAIInterpretationCount,
              lastLoginAt, loginCount, birthDateLocked,
              createdAt, updatedAt 
       FROM user WHERE id = ?`,
      [id] 
    );
    return result.length > 0 ? result[0] : null;
  }

  async findByPhoneNumber(phoneNumber: string): Promise<User | null> {
    return await this.userRepository.findOne({
      where: { phoneNumber }
    });
  }

  async findByEmail(email: string): Promise<User | null> {
    return await this.userRepository.findOne({
      where: { email }
    });
  }

  async findByName(name: string): Promise<User | null> {
    return await this.userRepository.findOne({
      where: { name }
    });
  }

  async findByOpenid(openidWx: string): Promise<User | null> {
    return await this.userRepository.findOne({
      where: { openidWx }
    });
  }

  async findAll(): Promise<User[]> {
    return await this.userRepository.query(`
      SELECT id, name, email, phoneNumber, role, userType, fullName, address, 
             gender, birthDate, isActive, profilePicture, profileBackgroundPicture,
             membershipType, membershipExpireDate, hasTeamPermission, 
             freeAssessmentCount, freeAIInterpretationCount, 
             totalAssessmentCount, totalAIInterpretationCount,
             lastLoginAt, loginCount, birthDateLocked,
             createdAt, updatedAt 
      FROM user
    `);
  }

  async create(user: Partial<User>): Promise<User> {
    return await this.userRepository.save(user);
  }

  async update(id: number, user: Partial<User>): Promise<void> {
    await this.userRepository.update(id, user);
  }

  async delete(id: number): Promise<void> {
    await this.userRepository.delete(id);
  }

  async updateLoginInfo(id: number): Promise<void> {
    await this.userRepository.query(
      `UPDATE user SET lastLoginAt = NOW(), loginCount = loginCount + 1 WHERE id = ?`,
      [id]
    );
  }

  async updateUsageCount(id: number, type: 'assessment' | 'aiInterpretation'): Promise<void> {
    const field = type === 'assessment' ? 'totalAssessmentCount' : 'totalAIInterpretationCount';
    await this.userRepository.query(
      `UPDATE user SET ${field} = ${field} + 1 WHERE id = ?`,
      [id]
    );
  }

  // 邀请码相关方法
  async findByInviteCode(inviteCode: string): Promise<User | null> {
    return await this.userRepository.findOne({
      where: { myInviteCode: inviteCode }
    });
  }

  async findByInviterId(inviterId: number): Promise<User[]> {
    return await this.userRepository.find({
      where: { invitedById: inviterId },
      select: ['id', 'name', 'email', 'teamPermissionGrantedAt', 'createdAt']
    });
  }

  async findDetailedByInviterId(inviterId: number): Promise<User[]> {
    return await this.userRepository.find({
      where: { invitedById: inviterId },
      select: [
        'id', 'name', 'email', 'phoneNumber', 'isActive', 
        'permissionLevel', 'userType', 'membershipType', 
        'teamPermissionGrantedAt', 'createdAt'
      ],
      order: { createdAt: 'DESC' }
    });
  }

  async isInviteCodeExists(inviteCode: string): Promise<boolean> {
    const count = await this.userRepository.count({
      where: { myInviteCode: inviteCode }
    });
    return count > 0;
  }

  async getInviteStats(inviterId: number): Promise<{
    totalInvited: number;
    activeInvited: number;
    teamInvited: number;
    individualInvited: number;
  }> {
    const invitedUsers = await this.userRepository.find({
      where: { invitedById: inviterId },
      select: ['id', 'isActive', 'permissionLevel']
    });

    const totalInvited = invitedUsers.length;
    const activeInvited = invitedUsers.filter(u => u.isActive).length;
    const teamInvited = invitedUsers.filter(u => u.permissionLevel === 'team').length;
    const individualInvited = invitedUsers.filter(u => u.permissionLevel === 'individual').length;

    return {
      totalInvited,
      activeInvited,
      teamInvited,
      individualInvited
    };
  }
} 