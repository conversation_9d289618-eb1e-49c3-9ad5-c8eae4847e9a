import { Module } from "@nestjs/common";
import { TypeOrmModule } from "@nestjs/typeorm";
import { User } from "../modules/users/entities/user.entity";
import { Card } from "../modules/cards/entities/card.entity";
import { Captcha } from "../modules/captcha/entities/captcha.entity";
import { Conversation } from "../modules/coze/entities/conversation.entity";
import { Message } from "../modules/coze/entities/message.entity";
import { TeamInvite } from "../modules/team-invite/entities/team-invite.entity";
import { Payment } from "../modules/payment/entities/payment.entity";
import { Question } from "../modules/question/entities/question.entity";
import { Option } from "../modules/question/entities/option.entity";
import { PersonalityResult } from "../modules/question/entities/personality-result.entity";
import { UserAnswerRecord } from "../modules/question/entities/user-answer-record.entity";
import { UserRepository } from "../repositories/user.repository";
import { CardRepository } from "../repositories/card.repository";
import { CaptchaRepository } from "../repositories/captcha.repository";
import { ConversationRepository } from "../repositories/conversation.repository";
import { MessageRepository } from "../repositories/message.repository";
import { TeamInviteRepository } from "../repositories/team-invite.repository";
import { PaymentRepository } from "../repositories/payment.repository";
import { QuestionRepository } from "../repositories/question.repository";
import { OptionRepository } from "../repositories/option.repository";
import { PersonalityResultRepository } from "../repositories/personality-result.repository";
import { UserAnswerRecordRepository } from "../repositories/user-answer-record.repository";

@Module({
  imports: [TypeOrmModule.forFeature([User, Card, Captcha, Conversation, Message, TeamInvite, Payment, Question, Option, PersonalityResult, UserAnswerRecord])],
  providers: [UserRepository, CardRepository, CaptchaRepository, ConversationRepository, MessageRepository, TeamInviteRepository, PaymentRepository, QuestionRepository, OptionRepository, PersonalityResultRepository, UserAnswerRecordRepository],
  exports: [UserRepository, CardRepository, CaptchaRepository, ConversationRepository, MessageRepository, TeamInviteRepository, PaymentRepository, QuestionRepository, OptionRepository, PersonalityResultRepository, UserAnswerRecordRepository],
})
export class RepositoriesModule {}