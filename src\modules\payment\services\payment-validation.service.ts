import { Injectable, Logger, BadRequestException } from '@nestjs/common';
import { PaymentStatus, Payment } from '../entities/payment.entity';
import { PaymentType, PaymentMethod } from '../dto/create-payment.dto';

/**
 * 支付验证服务
 * 负责支付相关的业务验证逻辑
 */
@Injectable()
export class PaymentValidationService {
  private readonly logger = new Logger(PaymentValidationService.name);

  /**
   * 验证支付金额是否合法
   * @param amount 支付金额（分）
   * @param paymentType 支付类型
   * @returns 验证结果
   */
  validatePaymentAmount(amount: number, paymentType: PaymentType): { isValid: boolean; message?: string } {
    // 基本金额验证
    if (!amount || amount <= 0) {
      return { isValid: false, message: '支付金额必须大于0' };
    }

    // 最小金额验证（1分）
    if (amount < 1) {
      return { isValid: false, message: '支付金额不能小于1分' };
    }

    // 最大金额验证（100万元）
    if (amount > 100000000) {
      return { isValid: false, message: '支付金额不能超过100万元' };
    }

    // 根据支付类型验证金额范围
    const typeAmountRules = this.getPaymentTypeAmountRules();
    const rule = typeAmountRules[paymentType];
    
    if (rule) {
      if (amount < rule.minAmount) {
        return { isValid: false, message: `${rule.name}最小金额为${rule.minAmount / 100}元` };
      }
      if (amount > rule.maxAmount) {
        return { isValid: false, message: `${rule.name}最大金额为${rule.maxAmount / 100}元` };
      }
    }

    return { isValid: true };
  }

  /**
   * 验证支付方式是否支持指定的支付类型
   * @param paymentMethod 支付方式
   * @param paymentType 支付类型
   * @returns 验证结果
   */
  validatePaymentMethodSupport(paymentMethod: PaymentMethod, paymentType: PaymentType): { isValid: boolean; message?: string } {
    const supportMatrix = this.getPaymentMethodSupportMatrix();
    const supportedTypes = supportMatrix[paymentMethod];

    if (!supportedTypes || !supportedTypes.includes(paymentType)) {
      return { 
        isValid: false, 
        message: `支付方式${paymentMethod}不支持${paymentType}类型的支付` 
      };
    }

    return { isValid: true };
  }

  /**
   * 验证支付订单是否可以发起支付
   * @param payment 支付记录
   * @returns 验证结果
   */
  validatePaymentInitiation(payment: Payment): { isValid: boolean; message?: string } {
    // 检查支付状态
    if (payment.status !== PaymentStatus.PENDING) {
      return { 
        isValid: false, 
        message: `当前支付状态(${payment.status})不允许发起支付` 
      };
    }

    // 检查支付是否过期
    if (payment.expiresAt && new Date() > payment.expiresAt) {
      return { 
        isValid: false, 
        message: '支付订单已过期' 
      };
    }

    // 检查支付金额
    const amountValidation = this.validatePaymentAmount(payment.amount, payment.type);
    if (!amountValidation.isValid) {
      return amountValidation;
    }

    // 检查支付方式支持
    const methodValidation = this.validatePaymentMethodSupport(payment.method, payment.type);
    if (!methodValidation.isValid) {
      return methodValidation;
    }

    return { isValid: true };
  }

  /**
   * 验证支付订单是否可以取消
   * @param payment 支付记录
   * @returns 验证结果
   */
  validatePaymentCancellation(payment: Payment): { isValid: boolean; message?: string } {
    const cancellableStatuses = [PaymentStatus.PENDING, PaymentStatus.PROCESSING];
    
    if (!cancellableStatuses.includes(payment.status)) {
      return { 
        isValid: false, 
        message: `当前支付状态(${payment.status})不允许取消` 
      };
    }

    return { isValid: true };
  }

  /**
   * 验证支付订单是否可以退款
   * @param payment 支付记录
   * @param refundAmount 退款金额（分）
   * @returns 验证结果
   */
  validatePaymentRefund(payment: Payment, refundAmount: number): { isValid: boolean; message?: string } {
    // 检查支付状态
    if (payment.status !== PaymentStatus.SUCCESS) {
      return { 
        isValid: false, 
        message: '只有支付成功的订单才能申请退款' 
      };
    }

    // 检查退款金额
    if (!refundAmount || refundAmount <= 0) {
      return { 
        isValid: false, 
        message: '退款金额必须大于0' 
      };
    }

    // 检查退款金额不能超过支付金额
    const totalRefunded = payment.refundAmount || 0;
    if (totalRefunded + refundAmount > payment.amount) {
      return { 
        isValid: false, 
        message: '退款金额不能超过支付金额' 
      };
    }

    // 检查支付时间（某些支付方式有退款时间限制）
    if (payment.paidAt) {
      const daysSincePaid = Math.floor((Date.now() - payment.paidAt.getTime()) / (1000 * 60 * 60 * 24));
      const refundTimeLimit = this.getRefundTimeLimit(payment.method);
      
      if (daysSincePaid > refundTimeLimit) {
        return { 
          isValid: false, 
          message: `超过退款时间限制(${refundTimeLimit}天)` 
        };
      }
    }

    return { isValid: true };
  }

  /**
   * 验证用户是否有权限操作支付订单
   * @param payment 支付记录
   * @param userId 用户ID
   * @returns 验证结果
   */
  validateUserPermission(payment: Payment, userId: number): { isValid: boolean; message?: string } {
    if (payment.userId !== userId) {
      return { 
        isValid: false, 
        message: '无权限操作此支付订单' 
      };
    }

    return { isValid: true };
  }

  /**
   * 验证支付回调数据的完整性
   * @param callbackData 回调数据
   * @param paymentMethod 支付方式
   * @returns 验证结果
   */
  validateCallbackData(callbackData: any, paymentMethod: PaymentMethod): { isValid: boolean; message?: string } {
    if (!callbackData) {
      return { isValid: false, message: '回调数据不能为空' };
    }

    // 根据支付方式验证必要字段
    if (paymentMethod === PaymentMethod.WECHAT_PAY) {
      const requiredFields = ['resource'];
      for (const field of requiredFields) {
        if (!callbackData[field]) {
          return { isValid: false, message: `缺少必要字段: ${field}` };
        }
      }

      // 验证resource字段的结构
      const resource = callbackData.resource;
      const resourceRequiredFields = ['ciphertext', 'associated_data', 'nonce'];
      for (const field of resourceRequiredFields) {
        if (!resource[field]) {
          return { isValid: false, message: `缺少resource必要字段: ${field}` };
        }
      }
    }

    return { isValid: true };
  }

  /**
   * 获取支付类型的金额规则
   * @returns 金额规则映射
   */
  private getPaymentTypeAmountRules(): Record<PaymentType, { name: string; minAmount: number; maxAmount: number }> {
    return {
      [PaymentType.TEAM_PERMISSION]: {
        name: '团队权限',
        minAmount: 100, // 1元
        maxAmount: 100000, // 1000元
      },
      [PaymentType.ASSESSMENT_CREDITS]: {
        name: '评估次数',
        minAmount: 100, // 1元
        maxAmount: 50000, // 500元
      },
      [PaymentType.AI_INTERPRETATION_CREDITS]: {
        name: 'AI解读次数',
        minAmount: 100, // 1元
        maxAmount: 50000, // 500元
      },
    };
  }

  /**
   * 获取支付方式支持矩阵
   * @returns 支持矩阵
   */
  private getPaymentMethodSupportMatrix(): Record<PaymentMethod, PaymentType[]> {
    return {
      [PaymentMethod.WECHAT_PAY]: [
        PaymentType.TEAM_PERMISSION,
        PaymentType.ASSESSMENT_CREDITS,
        PaymentType.AI_INTERPRETATION_CREDITS,
      ],
      [PaymentMethod.ALIPAY]: [
        PaymentType.TEAM_PERMISSION,
        PaymentType.ASSESSMENT_CREDITS,
        PaymentType.AI_INTERPRETATION_CREDITS,
      ],
    };
  }

  /**
   * 获取退款时间限制（天）
   * @param paymentMethod 支付方式
   * @returns 退款时间限制
   */
  private getRefundTimeLimit(paymentMethod: PaymentMethod): number {
    const timeLimits: Record<PaymentMethod, number> = {
      [PaymentMethod.WECHAT_PAY]: 365, // 微信支付1年
      [PaymentMethod.ALIPAY]: 365, // 支付宝1年
    };

    return timeLimits[paymentMethod] || 30; // 默认30天
  }

  /**
   * 验证并抛出异常（如果验证失败）
   * @param validationResult 验证结果
   * @param context 上下文信息
   */
  validateAndThrow(validationResult: { isValid: boolean; message?: string }, context?: string): void {
    if (!validationResult.isValid) {
      const errorMessage = context 
        ? `${context}: ${validationResult.message}` 
        : validationResult.message;
      
      this.logger.error(errorMessage);
      throw new BadRequestException(errorMessage);
    }
  }
}
