import { ApiProperty } from "@nestjs/swagger";
import { IsEnum, IsNotEmpty } from "class-validator";

export enum UsageType {
  Assessment = "assessment",
  AIInterpretation = "aiInterpretation",
}

export class UsageCheckDto {
  @ApiProperty({ 
    enum: UsageType, 
    description: "使用类型：评估或AI解读",
    example: UsageType.Assessment 
  })
  @IsEnum(UsageType, { message: "使用类型格式不正确" })
  @IsNotEmpty({ message: "使用类型不能为空" })
  type: UsageType;
}

export class UsageCheckResponseDto {
  @ApiProperty({ description: "是否可以使用（true表示可以免费使用或会员权限，false表示需要付费）" })
  canUse: boolean;

  @ApiProperty({ description: "剩余免费次数" })
  remainingFreeCount: number;

  @ApiProperty({ description: "是否为会员" })
  isMember: boolean;

  @ApiProperty({ description: "会员过期时间（如果是会员）", required: false })
  membershipExpireDate?: Date;

  @ApiProperty({ description: "提示信息" })
  message: string;
} 