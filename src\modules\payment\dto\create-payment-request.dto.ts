import { IsE<PERSON>, IsOptional, IsString, IsNumber, Min, IsNotEmpty, IsObject, IsIP } from 'class-validator';
import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { Transform } from 'class-transformer';
import { PaymentType, PaymentMethod } from './create-payment.dto';

/**
 * 创建支付请求DTO - 扩展版本，包含更多字段
 */
export class CreatePaymentRequestDto {
  @ApiProperty({ 
    description: '用户ID',
    example: 123
  })
  @IsNumber({}, { message: '用户ID必须是数字' })
  @Min(1, { message: '用户ID必须大于0' })
  @Transform(({ value }) => parseInt(value))
  userId: number;

  @ApiProperty({ 
    enum: PaymentType, 
    description: '支付类型',
    example: PaymentType.TEAM_PERMISSION 
  })
  @IsEnum(PaymentType, { message: '支付类型格式不正确' })
  type: PaymentType;

  @ApiProperty({ 
    enum: PaymentMethod, 
    description: '支付方式',
    example: PaymentMethod.WECHAT_PAY 
  })
  @IsEnum(PaymentMethod, { message: '支付方式格式不正确' })
  method: PaymentMethod;

  @ApiProperty({ 
    description: '支付金额（分）',
    example: 9900,
    minimum: 1
  })
  @IsNumber({}, { message: '支付金额必须是数字' })
  @Min(1, { message: '支付金额必须大于0' })
  @Transform(({ value }) => parseInt(value))
  amount: number;

  @ApiPropertyOptional({ 
    description: '购买数量（购买次数时使用）',
    example: 10,
    minimum: 1,
    default: 1
  })
  @IsOptional()
  @IsNumber({}, { message: '购买数量必须是数字' })
  @Min(1, { message: '购买数量必须大于0' })
  @Transform(({ value }) => value ? parseInt(value) : 1)
  quantity?: number = 1;

  @ApiPropertyOptional({ 
    description: '支付描述',
    example: '开通团队权限'
  })
  @IsOptional()
  @IsString()
  description?: string;

  @ApiPropertyOptional({ 
    description: '客户端IP地址',
    example: '127.0.0.1'
  })
  @IsOptional()
  @IsString()
  clientIp?: string;

  @ApiPropertyOptional({ 
    description: '支付扩展信息',
    example: { 
      source: 'mini_program', 
      device: 'mobile',
      version: '1.0.0'
    }
  })
  @IsOptional()
  @IsObject()
  metadata?: Record<string, any>;

  @ApiPropertyOptional({ 
    description: '支付回调地址（可选，使用默认配置）',
    example: 'https://api.example.com/payment/notify'
  })
  @IsOptional()
  @IsString()
  notifyUrl?: string;

  @ApiPropertyOptional({ 
    description: '支付成功后跳转地址',
    example: 'https://app.example.com/payment/success'
  })
  @IsOptional()
  @IsString()
  returnUrl?: string;
}

/**
 * 微信支付创建请求DTO
 */
export class CreateWechatPaymentDto extends CreatePaymentRequestDto {
  @ApiProperty({ 
    description: '微信用户openid',
    example: 'oUpF8uMuAJO_M2pxb1Q9zNjWeS6o'
  })
  @IsString()
  @IsNotEmpty()
  openid: string;

  @ApiPropertyOptional({ 
    description: '微信小程序appid（可选，使用默认配置）',
    example: 'wx1234567890abcdef'
  })
  @IsOptional()
  @IsString()
  appid?: string;
}

/**
 * 支付宝支付创建请求DTO
 */
export class CreateAlipayPaymentDto extends CreatePaymentRequestDto {
  @ApiPropertyOptional({ 
    description: '支付宝用户ID（可选）',
    example: '2088123456789012'
  })
  @IsOptional()
  @IsString()
  alipayUserId?: string;

  @ApiPropertyOptional({ 
    description: '买家支付宝账号（可选）',
    example: '<EMAIL>'
  })
  @IsOptional()
  @IsString()
  buyerEmail?: string;
}

/**
 * 支付创建响应DTO
 */
export class CreatePaymentResponseDto {
  @ApiProperty({ description: '支付记录ID' })
  paymentId: number;

  @ApiProperty({ description: '订单号' })
  orderNo: string;

  @ApiProperty({ description: '支付参数（用于前端调起支付）' })
  paymentParams: Record<string, any>;

  @ApiPropertyOptional({ description: '支付二维码（扫码支付时）' })
  qrCode?: string;

  @ApiPropertyOptional({ description: '支付链接（H5支付时）' })
  paymentUrl?: string;

  @ApiProperty({ description: '支付过期时间' })
  expiresAt: Date;

  @ApiProperty({ description: '创建时间' })
  createdAt: Date;
}

/**
 * 支付验证DTO
 */
export class PaymentVerificationDto {
  @ApiProperty({ 
    description: '订单号',
    example: 'ORDER_20240115103000123456'
  })
  @IsString()
  @IsNotEmpty()
  orderNo: string;

  @ApiPropertyOptional({ 
    description: '第三方交易号',
    example: 'wx_transaction_123456789'
  })
  @IsOptional()
  @IsString()
  thirdPartyTransactionId?: string;
}

/**
 * 支付退款请求DTO
 */
export class PaymentRefundDto {
  @ApiProperty({ 
    description: '支付记录ID或订单号',
    example: 123
  })
  paymentIdentifier: number | string;

  @ApiProperty({ 
    description: '退款金额（分）',
    example: 9900,
    minimum: 1
  })
  @IsNumber({}, { message: '退款金额必须是数字' })
  @Min(1, { message: '退款金额必须大于0' })
  refundAmount: number;

  @ApiProperty({ 
    description: '退款原因',
    example: '用户申请退款'
  })
  @IsString()
  @IsNotEmpty()
  refundReason: string;

  @ApiPropertyOptional({ 
    description: '退款备注',
    example: '7天无理由退款'
  })
  @IsOptional()
  @IsString()
  refundNote?: string;
}
