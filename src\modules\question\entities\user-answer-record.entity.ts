import { <PERSON><PERSON><PERSON>, PrimaryGeneratedColumn, Column, CreateDateColumn, UpdateDateColumn, ManyToOne, JoinColumn } from 'typeorm';
import { User } from '../../users/entities/user.entity';

@Entity('user_answer_records')
export class UserAnswerRecord {
  @PrimaryGeneratedColumn()
  id: number;

  @Column({ comment: '用户ID' })
  userId: number;

  @Column({ type: 'json', comment: '答题答案数组' })
  answers: number[];

  @Column({ comment: 'MBTI类型' })
  mbtiType: string;

  @Column({ comment: 'DISC类型' })
  discType: string;

  @Column({ comment: '最终人格类型代码' })
  finalType: string;

  @Column({ type: 'json', comment: 'MBTI各维度得分' })
  mbtiScores: { E: number; I: number; S: number; N: number; T: number; F: number; J: number; P: number };

  @Column({ type: 'json', comment: 'DISC各维度得分' })
  discScores: { D: number; I: number; S: number; C: number };

  @Column({ type: 'json', comment: '各维度得分' })
  dimensionScores: { [key: string]: number };

  @Column({ comment: '是否为当前类型', default: false })
  isCurrent: boolean;

  @Column({ type: 'text', nullable: true, comment: '备注' })
  remark: string;

  @CreateDateColumn({ comment: '创建时间' })
  createdAt: Date;

  @UpdateDateColumn({ comment: '更新时间' })
  updatedAt: Date;

  // 关联用户
  @ManyToOne(() => User, { onDelete: 'CASCADE' })
  @JoinColumn({ name: 'userId' })
  user: User;
} 